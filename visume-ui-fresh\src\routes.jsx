import React from "react";

// import RTLDefault from "views/rtl/default";

// Auth Imports
import SignIn from "views/auth/SignIn";
import Dashboard from "views/candidate/Dashboard";
import EmployerDashboard from "views/employer/EmployerDashboard";
import  ManageCredits from "./views/employer/settings/ManageCredits"
import BusinessAssociate from "views/partner/BusinessAssociate";
import ProfileCandidate from "./views/candidate/ProfileCandidate.jsx"

// Icon Imports
import {
  MdVideoFile,
  MdBarChart,
  MdPerson,
  MdLock,
  MdHome,
  MdSearch,
  MdCreditScore,
  MdOutlineSettings,
  MdOutlineSettingsSuggest,
  MdSpaceDashboard,
  MdSettings,
} from "react-icons/md";
import CreateVideoProfile from "views/candidate/CreateVideoProfile";
import JSRegister from "views/auth/JSRegister";
import TrackCandidates from "views/employer/TrackCandidates";
import AllVideoProfiles from "views/candidate/AllVideoProfiles";
import Suggestedjobs from "views/candidate/SuggestedJobs";
import JSSignup from "views/auth/JSSignup";
import SuggestedCandidates from "views/employer/SuggestedCandidates";
import { HiOutlineBriefcase, HiOutlineSparkles } from "react-icons/hi";
import { FaShareAlt } from "react-icons/fa";
import { IoSettings } from "react-icons/io5";
import { SiMockserviceworker } from "react-icons/si";
import AllMockProfiles from "views/candidate/AllMockProfiles";
import CandidateSettings from "views/candidate/candidateSettings";
import AdminDashboard from "./views/Admin/AdminDashboard";
import CandidateProfiles from "./views/Admin/CandidateProfiles";
import EmployerProfiles from "./views/Admin/EmployerProfiles";
import CandidatePlans from "./views/Admin/CandidatePlans";
import VideoProfiles from "views/Admin/VideoProfiles";

const routes = [
  {
    name: "Home",
    layout: "/candidate",
    path: "dashboard",
    icon: <MdHome className="h-6 w-6" />,
    component: <Dashboard />,
  },
  {
    name: "Suggested Jobs",
    layout: "/candidate",
    path: "suggested-jobs",
    icon: <HiOutlineSparkles className="h-6 w-6" />, // Changed icon for Suggested Jobs
    component: <Suggestedjobs />,
  },
  {
    name: "My Interviews",
    layout: "/candidate",
    path: "ongoing-recruitment",
    icon: <MdPerson className="h-6 w-6" />, // Changed icon for My Interviews
    component: <Dashboard />,
  },
  {
    name: "My Visumes",
    layout: "/candidate",
    path: "video-resume",
    icon: <MdVideoFile className="h-6 w-6" />, // Keeping the original as it fits
    component: <AllVideoProfiles />,
  },
  {
    name: "Mock Interviews",
    layout: "/candidate",
    path: "mock-interviews",
    icon: <MdSpaceDashboard className="h-6 w-6" />,
    component: <AllMockProfiles />, // Replace with the appropriate component for Track Candidates
  },
  {
    name: "Settings",
    layout: "/candidate",
    path: "profile/*", // Updated path for clarity
    icon: <MdOutlineSettings className="h-6 w-6" />, // Changed icon for Settings
    component: <ProfileCandidate /> ,
  },
  // {
  //   name: "Settings",
  //   layout: "/candidate",
  //   path: "settings", // Updated path for clarity
  //   icon: <MdOutlineSettings className="h-6 w-6" />, // Changed icon for Settings
  //   component: <CandidateSettings />,
  // },

  {
    name: "Dashboard",
    layout: "/admin",
    path: "dashboard",
    icon: <MdVideoFile className="h-6 w-6" />,
    component: <AdminDashboard />,
  },
  { 
    name:"Candidate Profiles",
    layout: "/admin",
    path: "candidateProfiles",
   icon: <MdVideoFile className="h-6 w-6" />,
   component: <CandidateProfiles />,
  },
  { 
    name:"Employer Profiles",
   layout: "/admin",
   path:"employerProfiles",
   icon:<MdVideoFile className="h-6 w-6" />,
   component: <EmployerProfiles />,
  },
  { 
    name:"Video Profiles",
   layout: "/admin",
   path:"videoProfiles",
   icon:<MdVideoFile className="h-6 w-6" />,
   component: <VideoProfiles />,
  },

  { 
    name:"Plans",
   layout: "/admin",
   path:"candidatePlans",
   icon:<MdVideoFile className="h-6 w-6" />,
   component: <CandidatePlans />
  },

  {
    name: "Home",
    layout: "/employer",
    path: "dashboard",
    icon: <MdHome className="h-6 w-6" />,
    component: <EmployerDashboard />,
  },
  {
    name: "Profile Search",
    layout: "/employer",
    path: "profile-search",
    icon: <MdSearch className="h-6 w-6" />,
    component: <EmployerDashboard />, // Replace with the appropriate component for Profile Search
  },
  {
    name: "Track Candidates",
    layout: "/employer",
    path: "track-candidates/*",
    icon: <MdBarChart className="h-6 w-6" />,
    component: <TrackCandidates />, // Replace with the appropriate component for Track Candidates
  },
  {
    name: "Positions",
    layout: "/employer",
    path: "suggested-candidates",
    icon: <HiOutlineBriefcase className="h-5 w-6" />, // Updated icon for Positions
    component: <SuggestedCandidates />, // Replace with the appropriate component for Track Candidates
  },
  {
    name: "Settings",
    layout: "/employer",
    path: "settings/*",
    icon: <MdSettings className="h-6 w-6" />,
    component: <ManageCredits />, // Replace with the appropriate component for Track Candidates
  },
  {
    name: "Business Associate",
    layout: "/business-associate",
    path: "dashboard",
    icon: <MdBarChart className="h-6 w-6" />,
    component: <BusinessAssociate />,
  },
  // {
  //   name: "Settings",
  //   layout: "/employer",
  //   path: "employerDashboard",
  //   icon: <MdLock className="h-6 w-6" />,
  //   component: <EmployerDashboard />,
  // }
];
export default routes;
