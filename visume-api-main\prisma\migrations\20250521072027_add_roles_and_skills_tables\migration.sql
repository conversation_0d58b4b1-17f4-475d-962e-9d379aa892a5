/*
  Warnings:

  - You are about to alter the column `cand_id` on the `jobseeker` table. The data in that column could be lost. The data in that column will be cast from `VarChar(50)` to `VarChar(11)`.

*/
-- AlterTable
ALTER TABLE `jobseeker` MODIFY `cand_id` VARCHAR(11) NOT NULL;

-- CreateTable
CREATE TABLE `roles` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `role_name` VARCHAR(255) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `skills` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `skill_name` VARCHAR(255) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
