const db = require("../config/db");

// Register company
exports.addNewCompany = async (req, res) => {
  const { company_name, company_description, company_website, gst } = req.body;

  // Check if files were uploaded by Multer
  const company_logo = req.files?.company_logo
    ? req.files.company_logo[0]
    : null;

  if (!company_logo) {
    return res.status(400).json({ message: "Company Logo file is required" });
  }

  try {
    // Acquire connection from the pool
    const conn = await db.promise().getConnection();

    try {
      // Check if the company already exists
      const query = "SELECT company_name FROM company WHERE company_name = ?";
      const [existingCompany] = await conn.query(query, [company_name]);

      if (existingCompany.length > 0) {
        return res.status(409).json({ message: "Company already exists" });
      }

      // Create new company
      const createCompanyQuery =
        "INSERT INTO company (company_name, company_logo, company_description, company_website, gst) VALUES (?, ?, ?, ?, ?)";
      const [result] = await conn.query(createCompanyQuery, [
        company_name,
        company_logo.filename, // Use the filename property
        company_description,
        company_website,
        gst,
      ]);

      const company_id = result.insertId; // Retrieve the new company's ID

      // Return response with company ID
      return res.status(201).json({
        message: "Company registered successfully",
        company_id,
      });
    } finally {
      conn.release(); // Always release the connection back to the pool
    }
  } catch (err) {
    console.error("Error during company registration:", err);
    return res.status(500).json({ error: err.message });
  }
};

// Get all companies
exports.getAllCompany = async (req, res) => {
  try {
    // Acquire connection from the pool
    const conn = await db.promise().getConnection();

    try {
      // Query to select all companies
      const query = "SELECT * FROM company";
      const [companies] = await conn.query(query);

      // Check if there are any companies
      if (companies.length === 0) {
        return res.status(404).json({ message: "No companies found" });
      }

      // Return the list of companies
      return res.status(200).json(companies);
    } finally {
      conn.release(); // Always release the connection back to the pool
    }
  } catch (err) {
    console.error("Error retrieving companies:", err);
    return res.status(500).json({ error: err.message });
  }
};
