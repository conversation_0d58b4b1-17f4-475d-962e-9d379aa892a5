import { useState, useCallback } from 'react';

/**
 * Hook to manage interview state and control when questions become active
 */
export function useInterviewState() {
  const [interviewState, setInterviewState] = useState('not_started'); // 'not_started', 'active', 'ended'
  const [interviewStartTime, setInterviewStartTime] = useState(null);
  const [interviewEndTime, setInterviewEndTime] = useState(null);

  const startInterview = useCallback(() => {
    setInterviewState('active');
    setInterviewStartTime(new Date().toISOString());
  }, []);

  const endInterview = useCallback(() => {
    setInterviewState('ended');
    setInterviewEndTime(new Date().toISOString());
  }, []);

  const resetInterview = useCallback(() => {
    setInterviewState('not_started');
    setInterviewStartTime(null);
    setInterviewEndTime(null);
  }, []);

  const isInterviewActive = interviewState === 'active';
  const isInterviewStarted = interviewState !== 'not_started';
  const isInterviewEnded = interviewState === 'ended';

  return {
    interviewState,
    interviewStartTime,
    interviewEndTime,
    isInterviewActive,
    isInterviewStarted,
    isInterviewEnded,
    startInterview,
    endInterview,
    resetInterview
  };
}
