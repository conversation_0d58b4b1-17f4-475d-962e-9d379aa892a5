const { generateMockResult } = require("../utils/helpers");
const { generateVideoRandomId } = require("../utils/helpers");
const { generateQuestions } = require("../utils/helpers");
const pool = require("../config/db");

exports.createMockResume = (req, res) => {
  const { candId, jobRole, skills, companyType, experience, salary } = req.body;

  // Validate the required fields
  if (
    !candId ||
    !jobRole ||
    !skills ||
    !companyType ||
    !experience ||
    !salary
  ) {
    return res.status(400).send("All fields are required.");
  }

  // Process the skills field
  const skillsString = skills.join(", ");

  // Get a connection from the pool
  pool.getConnection((err, connection) => {
    if (err) {
      console.error("Error getting database connection:", err);
      return res.status(500).send("Failed to connect to the database.");
    }

    // Check if the same role and skills already exist for the same candidate
    const checkQuery = `
                SELECT * FROM mock_interview
                WHERE cand_id = ? AND role = ? AND skills = ?`;

    connection.query(
      checkQuery,
      [candId, jobRole, skillsString],
      (err, results) => {
        if (err) {
          console.error("Error checking existing video profile:", err);
          connection.release(); // Release the connection
          return res.status(500).json({
            message: "Failed to check existing video profile.",
          });
        }

        if (results.length > 0) {
          connection.release(); // Release the connection
          return res.status(400).json({
            message:
              "A Mock Interview with the same role and skills already exists.",
          });
        } else {
          // Generate a random ID for the video profile
          const mock_interview_id = generateVideoRandomId();

          // Generate questions based on the given input fields
          generateQuestions(jobRole, skills, experience, companyType, 10)
            .then((questionsData) => {
              const questions = questionsData.questions;

              // Prepare the video profile data for insertion into the database
              const videoProfileData = {
                mock_interview_id,
                candId,
                jobRole,
                skills: skills,
                companyType,
                experience,
                salary,
                questions,
                score: null,
                status: "started",
              };

              // Prepare the SQL query for inserting the video profile data
              const sqlQuery = `
                          INSERT INTO mock_interview (
                            mock_interview_id,
                            cand_id,
                            role,
                            skills,
                            job_type,
                            experience_range,
                            salary,
                            questions,
                            score,
                            status
                          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;

              // Convert questions to JSON
              const questionsJson = JSON.stringify(videoProfileData.questions);

              // Execute the query to insert the video profile data into the database
              connection.query(
                sqlQuery,
                [
                  mock_interview_id,
                  candId,
                  jobRole,
                  skillsString,
                  companyType,
                  experience,
                  JSON.stringify(salary),
                  questionsJson,
                  null,
                  null,
                  "started",
                ],
                (error, results) => {
                  if (error) {
                    console.error("Error inserting video profile:", error);
                    connection.release(); // Release the connection
                    return res.status(500).json({
                      message: "Failed to create video profile.",
                    });
                  }

                  // Send back the response with the created video profile data
                  res.json({
                    mock_interview_id,
                    candId,
                    jobRole,
                    skills,
                    companyType,
                    experience,
                    salary,
                    questions,
                  });

                  connection.release(); // Release the connection after everything is done
                }
              );
            })
            .catch((error) => {
              console.error("Error generating interview questions:", error);
              connection.release(); // Release the connection
              res.status(500).json({
                message: "Failed to generate interview questions.",
              });
            });
        }
      }
    );
  });
};

// Finish Mock Profile API
exports.updateMockResumeData = async (req, res) => {
  const { mock_interview_id, score, questions, status, videoUrl } = req.body;

  // Validate the required fields
  if (!mock_interview_id || !score) {
    return res.status(400).json({
      message: "Mock interview ID and score are required."
    });
  }

  try {
    // Normalize and validate questions data
    let questionsString = "[]";
    if (questions) {
      try {
        if (typeof questions === 'string') {
          // Verify it's valid JSON
          JSON.parse(questions);
          questionsString = questions;
        } else {
          questionsString = JSON.stringify(questions);
        }
      } catch (jsonError) {
        return res.status(400).json({
          message: "Invalid questions format. Must be valid JSON.",
          error: jsonError.message
        });
      }
    }

    // Normalize and validate score data
    let scoreString;
    try {
      scoreString = typeof score === 'string' ? score : JSON.stringify(score);
      // Verify it's valid JSON
      JSON.parse(scoreString);
    } catch (jsonError) {
      return res.status(400).json({
        message: "Invalid score format. Must be valid JSON.",
        error: jsonError.message
      });
    }

    // Get database connection from pool
    const connection = await new Promise((resolve, reject) => {
      pool.getConnection((err, conn) => {
        if (err) {
          reject(err);
        } else {
          resolve(conn);
        }
      });
    });

    try {
      // Verify mock interview exists
      const [mockResults] = await new Promise((resolve, reject) => {
        connection.query(
          'SELECT * FROM mock_interview WHERE mock_interview_id = ?',
          [mock_interview_id],
          (err, results) => err ? reject(err) : resolve([results, null])
        );
      });

      if (!mockResults || mockResults.length === 0) {
        throw new Error('Mock interview not found');
      }

      // Update the mock interview
      await new Promise((resolve, reject) => {
        const interviewStatus = status || "notSubmitted";
        connection.query(
          `UPDATE mock_interview
           SET score = ?, status = ?, questions = ?, video_url = ?
           WHERE mock_interview_id = ?`,
           [scoreString, interviewStatus, questionsString, videoUrl, mock_interview_id],
          (err, results) => err ? reject(err) : resolve(results)
        );
      });

      res.json({
        message: "Mock Interview updated successfully.",
        mock_interview_id,
        score: JSON.parse(scoreString),
        status: status || "notSubmitted",
        videoUrl: videoUrl
      });

    } finally {
      connection.release();
    }

  } catch (error) {
    console.error("Error updating mock interview:", error);
    res.status(error.message === 'Mock interview not found' ? 404 : 500).json({
      message: error.message || "Failed to update mock interview",
      error: error.message
    });
  }
};

// Complete Mock Profile API
exports.finishMockResume = (req, res) => {
  const { score, mock_interview_id } = req.body;

  // Validate the required fields
  if (!mock_interview_id || !score.toString()) {
    return res.status(400).json({ msg: "All fields are required." });
  }

  if (score < 5) {
    return res
      .status(429)
      .json({ msg: "Score is too low, Mock Interview can't be submitted." });
  }

  try {
    // Get a connection from the pool
    pool.getConnection((err, connection) => {
      if (err) {
        console.error("Error getting database connection:", err);
        return res.status(500).send("Failed to connect to the database.");
      }

      // Check if the mock video profile exists
      const checkQuery = `SELECT cand_id FROM mock_interview WHERE mock_interview_id = ?`;
      connection.query(checkQuery, [mock_interview_id], (err, results) => {
        if (err) {
          console.error("Error checking Mock Interview profile:", err);
          connection.release(); // Always release the connection
          return res
            .status(500)
            .send("Failed to check Mock Interview profile.");
        }

        if (results.length === 0) {
          connection.release(); // Release the connection
          return res.status(404).send("Mock Interview not found.");
        }

        // Update the video profile with the status based on the score
        const updateQuery = `UPDATE mock_interview SET status = ? WHERE mock_interview_id = ?`;
        connection.query(
          updateQuery,
          [score >= 5 ? "Active" : "InActive", mock_interview_id],
          (updateError) => {
            if (updateError) {
              console.error("Error updating mock profile:", updateError);
              connection.release(); // Release the connection
              return res.status(500).send("Failed to finish mock Interview.");
            }

            // Send success response
            res.json({
              message: "Mock Interview finished successfully.",
              status: score >= 5 ? "Active" : "InActive",
            });

            // Release the connection after everything is done
            connection.release();
          }
        );
      });
    });
  } catch (error) {
    console.error("Error finishing video profile:", error);
    res
      .status(500)
      .json({ message: "Failed to finish the video profile.", error });
  }
};

// Generate Score Creation API
exports.generateMockResults = async (req, res) => {
  const { InterviewObject } = req.body;
  
  // Validate input
  if (!InterviewObject || !Array.isArray(InterviewObject) || InterviewObject.length === 0) {
    return res.status(400).json({
      message: "InterviewObject must be a non-empty array of questions and answers.",
      score: {
        Skill_Score: 5,
        Communication_Score: 5,
        Overall_Score: 5
      },
      Suggestions: "Score generation failed due to invalid input format."
    });
  }

  try {
    // Validate each question object
    const validatedQuestions = InterviewObject.map(q => ({
      question: String(q.question || '').trim(),
      keywords: Array.isArray(q.keywords) ? q.keywords : [],
      type: String(q.type || 'general').toLowerCase(),
      answer: q.answer === null || q.answer === undefined ? null : String(q.answer).trim()
    }));

    // Generate scores using the helper function
    const scores = await generateMockResult(validatedQuestions);
    
    // Validate score format
    if (!scores?.score?.Skill_Score ||
        !scores?.score?.Communication_Score ||
        !scores?.score?.Overall_Score) {
      throw new Error("Invalid score format received from score generator");
    }

    // Ensure scores are within valid range
    const normalizedScores = {
      score: {
        Skill_Score: Math.min(10, Math.max(0, scores.score.Skill_Score)),
        Communication_Score: Math.min(10, Math.max(0, scores.score.Communication_Score)),
        Overall_Score: Math.min(10, Math.max(0, scores.score.Overall_Score))
      },
      evaluation: scores.evaluation || [],
      Suggestions: scores.Suggestions || "No specific feedback available."
    };

    // Send normalized scores
    res.json(normalizedScores);

  } catch (error) {
    console.error("Error generating scores:", error);
    
    // Return default scores with error message
    res.json({
      score: {
        Skill_Score: 5,
        Communication_Score: 5,
        Overall_Score: 5
      },
      evaluation: [],
      Suggestions: `Score generation encountered an error: ${error.message}. Using default scores.`
    });
  }
};

// Fetch mock reusme by ID
exports.fetchMockResumeById = async (req, res) => {
  const { mock_resume_id } = req.params;
  try {
    // Get a connection from the pool
    pool.getConnection((err, connection) => {
      if (err) {
        console.error("Error getting database connection:", err);
        return res
          .status(500)
          .json({ message: "Failed to connect to the database." });
      }

      // SQL query to check if the video profile exists for the provided video profile ID
      const checkQuery = `
        SELECT * FROM mock_interview WHERE mock_interview_id = ?;
      `;

      connection.query(checkQuery, [mock_resume_id], (err, results) => {
        connection.release(); // Release the connection after query execution

        if (err) {
          console.error("Error fetching Mock Interview Data:", err);
          return res
            .status(500)
            .json({ message: "Failed to fetch Mock Interview Data." });
        }

        if (results.length === 0) {
          return res.status(404).json({
            message: "No Mock Interview found for this ID.",
          });
        }

        // Video profile exists, return the data
        res.status(200).json({
          message: "Mock Interview Data fetched successfully.",
          data: results[0], // Return the first matching profile
        });
      });
    });
  } catch (error) {
    console.error("Error fetching Mock Interview by Mock Interview ID:", error);
    res.status(500).json({ message: "Failed to fetch the Mock Interview." });
  }
};

exports.deleteMockInterviewById = async (req, res) => {
  const { mock_interview_id } = req.params;

  try {
    // Get a connection from the pool
    pool.getConnection((err, connection) => {
      if (err) {
        console.error("Error getting database connection:", err);
        return res
          .status(500)
          .json({ message: "Failed to connect to the database." });
      }

      // SQL query to check if the video profile exists for the provided video profile ID
      const checkQuery = `
        SELECT * FROM mock_interview WHERE mock_interview_id = ?;
      `;

      connection.query(checkQuery, [mock_interview_id], (err, results) => {
        if (err) {
          connection.release(); // Release the connection
          console.error("Error checking video profile:", err);
          return res
            .status(500)
            .json({ message: "Failed to check video profile." });
        }

        if (results.length === 0) {
          connection.release(); // Release the connection
          return res.status(404).json({
            message: "No Mock Interview found for this ID.",
          });
        }

        // Video profile exists, proceed to delete it
        const deleteQuery = `
          DELETE FROM mock_interview WHERE mock_interview_id = ?;
        `;

        connection.query(deleteQuery, [mock_interview_id], (err) => {
          connection.release(); // Release the connection

          if (err) {
            console.error("Error deleting Mock Interview:", err);
            return res
              .status(500)
              .json({ message: "Failed to delete the Mock Interview." });
          }

          // Successfully deleted the video profile
          res.status(200).json({
            message: "Mock Interview deleted successfully.",
          });
        });
      });
    });
  } catch (error) {
    console.error("Error deleting Mock Interview by Mock Interview ID:", error);
    res.status(500).json({ message: "Failed to delete the Mock Interview." });
  }
};

// Fetch video profiles by candidate ID
exports.listMockProfilesByCandidateId = (req, res) => {
  const { cand_id } = req.params;

  try {
    // Get a connection from the pool
    pool.getConnection((err, connection) => {
      if (err) {
        console.error("Error getting database connection:", err);
        return res.status(500).send("Failed to connect to the database.");
      }

      // SQL query to select video profiles by candidate ID
      const sqlQuery = `SELECT * FROM mock_interview WHERE cand_id = ?`;

      connection.query(sqlQuery, [cand_id], (error, results) => {
        connection.release(); // Release the connection

        if (error) {
          console.error("Error fetching Mock Interview from database:", error);
          return res.status(500).send("Failed to retrieve Mock Interviews.");
        }

        // Send back the retrieved video profiles
        res.json(results);
      });
    });
  } catch (error) {
    console.error("Error listing Mock Interviews by candidate ID:", error);
    res.status(500).send("Failed to list Mock Interviews.");
  }
};
