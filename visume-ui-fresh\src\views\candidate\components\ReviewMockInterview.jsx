import React, { useState } from "react";
import toast from "react-hot-toast";
import { BsExclamationCircle } from "react-icons/bs";
import { useNavigate, useParams } from "react-router-dom";

function ReviewMockInterview({
  videoUrl = "",
  score = {
    score: {
      Communication_Score: 0,
      Skill_Score: 0,
      Overall_Score: 0,
    },
    Suggestions: "",
    evaluation: [], // Change to empty array as default
  },
  status = "NotSubmitted",
  finishVideoProfile = () => {},
  onRetry = () => {},
}) {
  const { vpid } = useParams();
  const [toolTip, showToolTip] = useState(false);
  const navigate = useNavigate();

  const handleDelete = async () => {
    try {
      const videoProfileId = vpid;
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/mock-resume/${videoProfileId}`,
        {
          method: "DELETE",
        }
      );

      if (response.ok) {
        toast.success("Mock Resume discarded successfully");
        navigate("/candidate/dashboard");
        localStorage.removeItem(`questions`);
        localStorage.removeItem(`ReviewVideoProfile${vpid}`);
        window.location.reload(); // Ensure the page is reloaded to reflect changes
      } else {
        const { message } = await response.json();
        toast.error(message);
        console.error("Failed to delete Mock Resume");
      }
    } catch (error) {
      console.error("Error:", error);
    }
  };
  return (
    <div className="flex h-full min-h-screen w-full flex-col items-center justify-start bg-gray-50 py-6 dark:bg-gray-900 lg:px-6">
      {/* Container */}
      <div className="flex h-full w-full flex-col rounded-t-xl bg-white p-6 shadow-lg dark:bg-gray-800">
        <span className=" absolute text-xl font-bold text-gray-800 dark:text-gray-200 md:mb-0 lg:mb-0">
          Review Your Mock Interview
        </span>
        <div className="flex h-full w-full flex-col gap-6 md:mt-10 md:items-start lg:mt-10 lg:flex-row lg:items-start">
          {/* Left Side: Video Preview */}
          <div className="flex w-full flex-col items-start justify-evenly space-y-4 lg:w-3/5">
            <div className="relative h-[60vh] overflow-hidden rounded-xl border-0 border-gray-300 bg-gray-800 shadow-md">
              {videoUrl ? (
                <div className="relative h-full w-full">
                  <video
                    src={videoUrl}
                    className="h-full w-full object-cover"
                    controls
                    controlsList="nodownload"
                    onContextMenu={e => e.preventDefault()}
                  />
                  <div className="absolute bottom-4 right-4 rounded bg-black bg-opacity-50 px-2 py-1 text-sm text-white">
                    Mock Interview Recording
                  </div>
                </div>
              ) : (
                <div className="flex h-full flex-col items-center justify-center text-gray-400">
                  <svg
                    className="mb-2 h-12 w-12"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                    />
                  </svg>
                  <p>No video recording available</p>
                  <p className="mt-1 text-sm">Try retaking the interview</p>
                </div>
              )}
            </div>

            {/* Overview Section (below video preview) */}
            <div className=" rounded-xl bg-white p-4 shadow-md dark:bg-gray-700 lg:mt-10">
              <h3 className="text-md font-semibold text-gray-800 dark:text-gray-200">
                Overview
              </h3>
              <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                This page contains the scores of the Mock interview taken and
                evaluated by AI. The AI has assessed the candidate’s skill,
                communication, and overall performance. Here, users can review
                the video, check their scores, and ensure everything is accurate
                before submitting it into the database.
              </p>
            </div>
          </div>

          {/* Right Side: Score and Suggestions */}
          <div className="flex h-max w-full flex-col space-y-4 rounded-xl bg-white p-6 shadow-md dark:bg-gray-700 md:mt-0 lg:mt-0 lg:w-2/5">
            {/* Scores */}
            <div className="flex flex-col space-y-4">
              <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
                Your Scores
              </h3>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                {/* Skill Score */}
                <div
                  className={`relative flex transform flex-col items-center justify-center rounded-lg p-4 shadow-lg transition-transform duration-300 ease-in-out hover:scale-105 ${
                    score.score.Skill_Score < 5
                      ? "bg-gradient-to-tr from-red-100 to-red-200 dark:from-red-700 dark:to-red-800"
                      : score.score.Skill_Score <= 7
                      ? "bg-gradient-to-tr from-yellow-100 to-yellow-200 dark:from-yellow-700 dark:to-yellow-800"
                      : "bg-gradient-to-tr from-green-100 to-green-200 dark:from-green-700 dark:to-green-800"
                  }`}
                >
                  <span className="text-lg font-bold text-gray-800 dark:text-gray-200">
                    Skill
                  </span>
                  <span
                    className={`text-4xl font-bold ${
                      score.score.Skill_Score < 5
                        ? "text-red-600 dark:text-red-400"
                        : score.score.Skill_Score <= 7
                        ? "text-yellow-600 dark:text-yellow-400"
                        : "text-green-600 dark:text-green-400"
                    }`}
                  >
                    {score.score.Skill_Score || 0}
                  </span>
                  {/* Animated Background Accent */}
                  <div
                    className={`absolute bottom-0 left-0 h-1 w-full ${
                      score.score.Skill_Score < 5
                        ? "bg-red-400 dark:bg-red-500"
                        : score.score.Skill_Score <= 7
                        ? "bg-yellow-400 dark:bg-yellow-500"
                        : "bg-green-400 dark:bg-green-500"
                    } transition-width duration-500 ease-in-out`}
                  ></div>
                </div>

                {/* Communication Score */}
                <div
                  className={`relative flex transform flex-col items-center justify-center rounded-lg p-4 shadow-lg transition-transform duration-300 ease-in-out hover:scale-105 ${
                    score.score.Communication_Score < 5
                      ? "bg-gradient-to-tr from-red-100 to-red-200 dark:from-red-700 dark:to-red-800"
                      : score.score.Communication_Score <= 7
                      ? "bg-gradient-to-tr from-yellow-100 to-yellow-200 dark:from-yellow-700 dark:to-yellow-800"
                      : "bg-gradient-to-tr from-green-100 to-green-200 dark:from-green-700 dark:to-green-800"
                  }`}
                >
                  <span className="text-lg font-bold text-gray-800 dark:text-gray-200">
                    Communication
                  </span>
                  <span
                    className={`text-4xl font-bold ${
                      score.score.Communication_Score < 5
                        ? "text-red-600 dark:text-red-400"
                        : score.score.Communication_Score <= 7
                        ? "text-yellow-600 dark:text-yellow-400"
                        : "text-green-600 dark:text-green-400"
                    }`}
                  >
                    {score.score.Communication_Score || 0}
                  </span>
                  {/* Animated Background Accent */}
                  <div
                    className={`absolute bottom-0 left-0 h-1 w-full ${
                      score.score.Communication_Score < 5
                        ? "bg-red-400 dark:bg-red-500"
                        : score.score.Communication_Score <= 7
                        ? "bg-yellow-400 dark:bg-yellow-500"
                        : "bg-green-400 dark:bg-green-500"
                    } transition-width duration-500 ease-in-out`}
                  ></div>
                </div>

                {/* Overall Score */}
                <div
                  className={`relative flex transform flex-col items-center justify-center rounded-lg p-4 shadow-lg transition-transform duration-300 ease-in-out hover:scale-105 ${
                    score.score.Overall_Score < 5
                      ? "bg-gradient-to-tr from-red-100 to-red-200 dark:from-red-700 dark:to-red-800"
                      : score.score.Overall_Score <= 7
                      ? "bg-gradient-to-tr from-yellow-100 to-yellow-200 dark:from-yellow-700 dark:to-yellow-800"
                      : "bg-gradient-to-tr from-green-100 to-green-200 dark:from-green-700 dark:to-green-800"
                  }`}
                >
                  <span className="text-nowrap px-2 text-lg font-bold text-gray-800 dark:text-gray-200">
                    Overall Score
                  </span>
                  <span
                    className={`text-4xl font-bold ${
                      score.score.Overall_Score < 5
                        ? "text-red-600 dark:text-red-400"
                        : score.score.Overall_Score <= 7
                        ? "text-yellow-600 dark:text-yellow-400"
                        : "text-green-600 dark:text-green-400"
                    }`}
                  >
                    {score.score.Overall_Score || 0}
                  </span>
                  {/* Animated Background Accent */}
                  <div
                    className={`absolute bottom-0 left-0 h-1 w-full ${
                      score.score.Overall_Score < 5
                        ? "bg-red-400 dark:bg-red-500"
                        : score.score.Overall_Score <= 7
                        ? "bg-yellow-400 dark:bg-yellow-500"
                        : "bg-green-400 dark:bg-green-500"
                    } transition-width duration-500 ease-in-out`}
                  ></div>
                </div>
              </div>
            </div>

            {/* Suggestions */}
            <div className="flex flex-col">
              <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
                Suggestions
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {score.Suggestions}
              </p>
            </div>

            {/* Status */}
            <div className="group relative flex flex-col">
              <h3 className="flex items-center justify-start gap-2 text-lg font-semibold text-gray-800 dark:text-gray-200">
                Status
                <BsExclamationCircle
                  onMouseEnter={() => showToolTip(true)}
                  onMouseLeave={() => showToolTip(false)}
                  className="group-hover:tooltip relative cursor-pointer text-[1rem]"
                />
                {toolTip && (
                  <div className="absolute bottom-full left-0 z-10 mb-2 block w-max rounded-md bg-gray-800 bg-opacity-50 px-3 py-1 text-sm text-white">
                    Status will be Active if score is above 5, otherwise
                    Inactive.
                  </div>
                )}
              </h3>
              <div className={`mt-2 rounded-lg border p-2 ${
                status === "active"
                  ? "border-green-500 bg-green-50 text-green-700"
                  : "border-red-500 bg-red-50 text-red-700"
              }`}>
                {status === "active" ? "Active" : "Inactive"}
                <p className="mt-1 text-xs">
                  {status === "active"
                    ? "Your interview meets the required criteria"
                    : "Your score is below the required threshold"
                  }
                </p>
              </div>
            </div>

            {/* Finish Video profile (inside card now) */}
            <div className="mt-6 flex w-full items-center justify-between space-x-4">
              <div className="flex space-x-4">
                <button
                  className="flex items-center rounded-lg bg-red-500 px-4 py-3 font-semibold text-white shadow-md transition-all duration-200 hover:bg-red-600"
                  onClick={() => {
                    if (window.confirm("Are you sure you want to discard this mock interview?")) {
                      handleDelete();
                    }
                  }}
                >
                  <svg
                    className="mr-2 h-5 w-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                    />
                  </svg>
                  Discard
                </button>
                <button
                  onClick={onRetry}
                  className="inline-flex cursor-pointer items-center rounded-lg bg-gray-500 px-4 py-3 font-semibold text-white shadow-md transition-all duration-200 hover:bg-gray-600"
                >
                  <svg
                    className="mr-2 h-5 w-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                    />
                  </svg>
                  Retry
                </button>
                
                <button
                  className={`inline-flex cursor-pointer items-center rounded-lg px-4 py-3 font-semibold text-white shadow-md transition-all duration-200 ${
                    score.score.Overall_Score < 5
                      ? "bg-gray-500 hover:bg-gray-600"
                      : "bg-green-500 hover:bg-green-600"
                  }`}
                  onClick={score.score.Overall_Score >= 5 ? finishVideoProfile : undefined}
                  disabled={score.score.Overall_Score < 5}
                >
                  <svg
                    className="mr-2 h-5 w-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                  {score.score.Overall_Score >= 5 ? "Submit" : "Score Too Low"}
                  <span className="ml-1 text-xs">
                    ({score.score.Overall_Score}/10)
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* Evaluation Section */}
      <div className="flex w-full flex-col rounded-b-xl bg-white p-6 shadow-md dark:bg-gray-700">
        <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
          Evaluation Details
        </h3>
        <div className="mt-4 space-y-6">
          {Array.isArray(score.evaluation) && score.evaluation.length > 0 ? (
            score.evaluation.map((item, index) => (
              <div
                key={index}
                className="rounded-lg bg-gray-100 p-4 shadow-sm dark:bg-gray-800"
              >
                <div className="flex flex-col space-y-2">
                  <h4 className="text-md font-semibold text-gray-800 dark:text-gray-200">
                    Question {index + 1}: {item.Question}
                  </h4>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    <strong>Your Answer:</strong>
                    <p className="mt-2">
                      {item.Your_Answer === null || item.Your_Answer === undefined ||
                       item.Your_Answer === "null" ||
                       (typeof item.Your_Answer === 'string' && item.Your_Answer.trim() === "")
                        ? "-"
                        : item.Your_Answer}
                    </p>
                  </div>
                  {item.Expected_Answer && (
                    <div className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                      <strong>Expected Answer:</strong>
                      <p className="mt-2">{item.Expected_Answer}</p>
                    </div>
                  )}
                </div>
              </div>
            ))
          ) : (
            <div className="text-center text-gray-500 dark:text-gray-400">
              <p>No detailed evaluation available</p>
              <p className="mt-2 text-sm">This may happen if there was an error processing your answers.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default ReviewMockInterview;
