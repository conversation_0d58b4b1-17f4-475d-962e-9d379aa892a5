import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import Cookies from "js-cookie";
import toast from "react-hot-toast";
import { ProfileCard } from "./ProfilesUI";
import ProfileSkelLoader from "./components/ProfileSkelLoader";

const SuggestedCandidates = () => {
  const navigate = useNavigate();
  const [candidateData, setCandidateData] = useState([]);
  const [selectedProfiles, setSelectedProfiles] = useState(new Set());
  const [loadingId, setLoadingId] = useState(null);
  const [shortListedProfiles, setShortListedProfiles] = useState([]);
  const emp_id = Cookies.get("employerId");

  const handleShortlist = async (id, cand_id) => {
    if (loadingId === id) return;
    setLoadingId(id);

    try {
      if (!emp_id) {
        return toast.error("You need to be an employer to shortlist profiles");
      }

      const response = await fetch(
        `${
          import.meta.env.VITE_APP_HOST
        }/api/v1/employer-profiles/shortlist-candidate`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ emp_id, cand_id: id }),
        }
      );

      if (!response.ok) {
        const msg = await response.json();
        toast.error(msg.message);
        throw new Error(`HTTP error! status: ${msg.message}`);
      }

      const data = await response.json();
      toast((t) => (
        <span className="flex items-center">
          <div className="mr-[10px]">
            <i className="fas fa-check-circle text-[#28a745]"></i>
          </div>
          <div className="flex items-start justify-start gap-2">
            {data.message}
            <button
              onClick={() => {
                toast.dismiss(t.id);
                navigate("/employer/track-candidates");
              }}
              className="ml-[10px] cursor-pointer rounded-[4px] border-0 bg-[#28a745] px-2 py-1 text-[#fff]"
            >
              Shortlisted Candidates
            </button>
          </div>
        </span>
      ));
      setSelectedProfiles((prev) => {
        const newSelectedProfiles = new Set(prev);
        if (newSelectedProfiles.has(id)) {
          newSelectedProfiles.delete(id);
        } else {
          newSelectedProfiles.add(id);
        }
        return newSelectedProfiles;
      });
    } catch (error) {
      console.error("Error during shortlist operation:", error);
    } finally {
      setLoadingId(null);
    }
  };

  const getShortListedProfiles = async () => {
    try {
      const emp_id = Cookies.get("employerId");
      if (!emp_id) {
        toast.error("You are not an employeer");
        navigate("/");
        return;
      }
      fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/shortlisted-profiles/${emp_id}`
      )
        .then((res) => res.json())
        .then((data) => {
          if (data.data && data.data.length) {
            const shortIds = [];
            data.data.map((e) => shortIds.push(e.video_profile_id));
            setShortListedProfiles(shortIds);
          }
        });
    } catch (err) {
      console.log(`Error`, err);
    }
  };

  useEffect(() => {
    getShortListedProfiles();
  }, []);

  useEffect(() => {
    // Get the values from cookies
    const getAllProfiles = async () => {
      try {
        const dummyProfiles = Array.from({ length: 10 }, (_, index) => ({
          name: `Loading... ${index}`,
        }));
        console.log(dummyProfiles)

        setCandidateData((prev) => [...prev, ...dummyProfiles]);

        const data = await fetch(
          `${
            import.meta.env.VITE_APP_HOST
          }/api/v1/getSuggestedCandidates?emp_id=${emp_id}`
        );
        const res = await data.json();

        setCandidateData((prev) => {
          const newProfiles = prev.slice(0, prev.length - 10);
          return [...newProfiles, ...res.candidateProfiles];
        });
      } catch (err) {
        console.log(`Error`, err);
      }
    };
    getAllProfiles();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 pb-8">
      <div className="container mx-auto">
        <div className="px-4">
          <div className="flex items-center space-x-2 py-2">
            <h3 className="text-xl font-semibold">Visume Suggested Profiles</h3>
          </div>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {candidateData && candidateData.length > 0 ? (
              candidateData.map((profile) =>
                profile.id ? (
                  <ProfileCard
                    shortListedProfiles={shortListedProfiles}
                    key={profile.id}
                    keyValue={profile.id}
                    {...profile}
                    isShortlisted={selectedProfiles.has(profile.id)}
                    onShortlist={handleShortlist}
                    isLoading={loadingId === profile.id}
                  />
                ) : (
                  <ProfileSkelLoader key={`skeleton-${Math.random()}`} />
                )
              )
            ) : (
              <div className="col-span-full flex items-center justify-center py-8 text-gray-500">
                <p className="text-center text-lg">
                  No Suggested Profiles found
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SuggestedCandidates;
