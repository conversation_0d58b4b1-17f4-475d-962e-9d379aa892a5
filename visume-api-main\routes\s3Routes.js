const express = require("express");
const router = express.Router();
const s3Upload = require("../controllers/s3UploadController");

// Debug middleware for S3 routes
router.use((req, res, next) => {
    console.log('[DEBUG] S3 Route accessed:', req.method, req.path);
    next();
});

// Health check endpoint to validate S3 connectivity
router.get('/s3/health', (req, res, next) => {
    console.log('[DEBUG] S3 health check route hit');
    next();
}, s3Upload.checkS3Health);

router.get(
    "/get-s3-url/:fileName",
    (req, res, next) => {
        console.log('[DEBUG] Get S3 URL route hit for file:', req.params.fileName);
        next();
    },
    s3Upload.getS3Url
);
module.exports = router;
