import React, { useState } from 'react';
import 'react-responsive-modal/styles.css';
import { Modal } from 'react-responsive-modal';
import ProfileCandidate from 'views/candidate/ProfileCandidate';

const PopUp = () => {
  const [modelOpen, setModelOpen] = useState(false);

  const onOpenModal = () => setModelOpen(true);
  const onCloseModal = () => setModelOpen(false);

  return (
    <div>
      <button onClick={onOpenModal}>Open modal</button>
      <Modal open={modelOpen} onClose={onCloseModal} classNames="w-full">
          <ProfileCandidate />
      </Modal>
    </div>
  );
};
export default PopUp;

