import { useEffect, useState } from "react";
import { EyeIcon, EyeOffIcon, HelpCircle } from "lucide-react";
import Cookies from "js-cookie";
import toast from "react-hot-toast";
import { useNavigate } from "react-router-dom";

const CandidateSettings = () => {
  const navigate = useNavigate();
  // State to hold password fields and error messages
  const cand_id = Cookies.get("candId");
  const [loading, setLoading] = useState(false);
  const [oldPassword, setOldPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [error, setError] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [showOldPassword, setShowOldPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Function to handle password change
  const handlePasswordChange = async () => {
    if (!cand_id) {
      toast.error("No Token Found, Pls Login Again");
      navigate("/candidate/signIn");
    }
    // Basic validation
    if (!oldPassword || !newPassword || !confirmPassword) {
      setError("Please fill in all fields.");
      return;
    }

    if (newPassword !== confirmPassword) {
      setError("New passwords do not match.");
      return;
    }
    try {
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/changePassword`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            cand_id,
            password: oldPassword,
            newPassword,
          }), // Send JSON
        }
      );

      // Log the response
      const responseData = await response.json();
      if (!response.ok) {
        toast.error(responseData.message);
      } else {
        setOldPassword("");
        setNewPassword("");
        setConfirmPassword("");
        toast.success(responseData.message);
      }
    } catch (err) {
      const errorMessage =
        error.response?.data?.message || "An unexpected error occurred.";
      toast.error(errorMessage); // Display the JSON error message
      console.error("Error:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (successMessage) {
      toast.success(successMessage);
    }
    if (error) {
      toast.error(error);
    }
  }, [successMessage, error]);

  return (
    <div className="card scrollbar-hide hover:scrollbar-thumb-rounded-full col-span-full h-full max-h-screen overflow-y-auto rounded-md
                   bg-white p-3 scrollbar-thin hover:scrollbar-thumb-gray-900 dark:bg-navy-700 dark:text-white
                   dark:hover:scrollbar-thumb-gray-300 lg:col-span-5 2xl:col-span-5">
      <div className="mx-auto max-w-full p-2">
        <h2 className="mb-4 text-xl font-semibold">Password Settings</h2>
        <p>Change your password and security settings here.</p>

        <div className="mt-4">
          {/* Old Password Field */}
          <label
            className="block text-sm font-medium text-gray-700"
            htmlFor="old-password"
          >
            Old Password
          </label>
          <div className="relative mt-1 block w-3/4 rounded-md border border-gray-300 p-2 pr-10 sm:w-96">
            <input
              type={showOldPassword ? "text" : "password"}
              id="old-password"
              value={oldPassword}
              onChange={(e) => setOldPassword(e.target.value)}
              className="outline-none w-full"
              placeholder="Enter your old password"
            />
            <button
              type="button"
              onClick={() => setShowOldPassword(!showOldPassword)}
              className="absolute right-3 top-1/2 -translate-y-1/2 transform"
            >
              {showOldPassword ? (
                <EyeOffIcon className="h-5 w-5 text-gray-600" />
              ) : (
                <EyeIcon className="h-5 w-5 text-gray-600" />
              )}
            </button>
          </div>
        </div>

        <div className="mt-4">
          {/* New Password Field */}
          <label
            className="block text-sm font-medium text-gray-700"
            htmlFor="new-password"
          >
            New Password
          </label>
          <div className="relative mt-1 block w-3/4 rounded-md border border-gray-300 p-2 pr-10 sm:w-96">
            <input
              type={showNewPassword ? "text" : "password"}
              id="new-password"
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
              className="outline-none w-full"
              placeholder="Enter a new password"
            />
            <button
              type="button"
              onClick={() => setShowNewPassword(!showNewPassword)}
              className="absolute right-3 top-1/2 -translate-y-1/2 transform"
            >
              {showNewPassword ? (
                <EyeOffIcon className="h-5 w-5 text-gray-600" />
              ) : (
                <EyeIcon className="h-5 w-5 text-gray-600" />
              )}
            </button>
          </div>
        </div>

        <div className="mt-4">
          {/* Confirm New Password Field */}
          <label
            className="block text-sm font-medium text-gray-700"
            htmlFor="confirm-password"
          >
            Confirm New Password
          </label>
          <div className="relative mt-1 block w-3/4 rounded-md border border-gray-300 p-2 pr-10 sm:w-96">
            <input
              type={showConfirmPassword ? "text" : "password"}
              id="confirm-password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              className="outline-none w-full"
              placeholder="Confirm your new password"
            />
            <button
              type="button"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              className="absolute right-3 top-1/2 -translate-y-1/2 transform"
            >
              {showConfirmPassword ? (
                <EyeOffIcon className="h-5 w-5 text-gray-600" />
              ) : (
                <EyeIcon className="h-5 w-5 text-gray-600" />
              )}
            </button>
          </div>
        </div>

        <div className="mt-6">
          {/* Change Password Button */}
          <button
            onClick={loading ? undefined : handlePasswordChange}
            className="mx-auto w-3/4 rounded-md bg-brand-500 py-2 font-semibold text-white hover:bg-brand-600 sm:w-96"
          >
            {loading ? (
              <svg
                className="h-4 w-4 animate-spin text-white"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                  fill="none"
                />
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                />
              </svg>
            ) : (
              "Change Password"
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default CandidateSettings;
