import React from "react";

export default function QuestionDisplay({ question, followUp }) {
  return (
    <div className="h-2/5 rounded-xl border-2 border-[#6b88ff] bg-white p-4 shadow-md">
      <h2 className="flex items-center text-lg font-bold text-[#6b88ff]">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="1em"
          height="1em"
          viewBox="0 0 24 24"
          className="mr-2"
        >
          <path
            fill="#6b88ff"
            d="M16 2H8C4.691 2 2 4.691 2 8v13a1 1 0 0 0 1 1h13c3.309 0 6-2.691 6-6V8c0-3.309-2.691-6-6-6m-5 10.5A1.5 1.5 0 0 1 9.5 14c-.086 0-.168-.011-.25-.025c-.083.01-.164.025-.25.025a2 2 0 1 1 2-2c0 .085-.015.167-.025.25c.013.082.025.164.025.25m4 1.5c-.086 0-.167-.015-.25-.025a1.5 1.5 0 0 1-.25.025a1.5 1.5 0 0 1-1.5-1.5c0-.085.012-.168.025-.25c-.01-.083-.025-.164-.025-.25a2 2 0 1 1 2 2"
          />
        </svg>
        AI Interviewer is speaking...
      </h2>
      <div className="mt-4 space-y-4">
        <p className="text-xl text-gray-700">{question}</p>
        {followUp && (
          <div className="mt-2">
            <p className="text-sm font-semibold text-[#6b88ff]">Follow-up:</p>
            <p className="text-lg text-gray-600">{followUp}</p>
          </div>
        )}
      </div>
    </div>
  );
}
