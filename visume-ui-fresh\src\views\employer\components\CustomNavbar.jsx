import React from "react";
import Dropdown from "../../../components/dropdown";
import { Link } from "react-router-dom";
import { RiMoonFill, RiSunFill } from "react-icons/ri";
import avatar from "assets/img/avatars/avatar4.png";
import Cookies from "js-cookie";
import { useNavigate } from "react-router-dom";
import LogoImage from 'assets/img/Visume-logo-icon.png';

const CustomNavbar = ({ links }) => {
  const navigate = useNavigate();
  const [darkmode, setDarkmode] = React.useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = React.useState(false);

  const deletecookies = () => {
    Cookies.remove('jstoken');
    Cookies.remove('role');
    Cookies.remove('candId');
    Cookies.remove('formData');
    Cookies.remove('questions');
    Cookies.remove('videoProfileId');
    Cookies.remove('skills');
    Cookies.remove('jobRole');
    navigate('/auth/signin');
  };

  return (
    <nav className="sticky top-1 z-40 mb-1 flex flex-col sm:flex-row items-center justify-between rounded-xl bg-white/10 backdrop-blur-xl dark:bg-[#0b14374d] p-2">
      
      <div className="w-full sm:w-auto flex items-center justify-between px-4 py-2 rounded-full bg-white shadow-sm shadow-blue-300 dark:!bg-navy-800 dark:shadow-none">
        {/* Logo Section */}
        <div className="flex items-center">

          <div  className="font-poppins text-xl sm:text-2xl font-bold text-brand-500 dark:text-white flex items-center cursor-pointer" 
            onClick={() => navigate('/employer')}>

            <img src={LogoImage} alt="Visume logo" className="h-6 sm:h-7 w-6 sm:w-7" />
            <span className="ml-2">Visume.ai</span>

          </div>

        </div>

        {/* Mobile Menu Button */}
        <button className="sm:hidden p-2" onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}>

           <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={isMobileMenuOpen ? "M6 18L18 6M6 6l12 12" : "M4 6h16M4 12h16M4 18h16"} />
           </svg>

        </button>

      </div>

      {/* Navigation Links and Controls */}
      <div className={`${isMobileMenuOpen ? 'flex' : 'hidden'} sm:flex flex-col sm:flex-row items-center w-full sm:w-auto mt-4 sm:mt-0 space-y-4 sm:space-y-0 sm:space-x-6`}>
        {/* Links */}
        <div className="flex flex-col sm:flex-row items-center space-y-4 sm:space-y-0 sm:space-x-6">
          {links.map((link, index) => (
            <Link
              key={index}
              to={link.url}
              className="flex items-center text-xs lg:text-base text-gray-600 dark:text-white hover:text-brand-500 hover:font-semibold transition-all duration-200"
            >
              {link.icon && <span className="mr-2">{link.icon}</span>}
              {link.text}
            </Link>
          ))}
        </div>

        {/* Controls */}
        <div className="flex items-center space-x-4">
          {/* Dark Mode Toggle */}
          <button
            className="text-gray-600 dark:text-white"
            onClick={() => {
              document.body.classList.toggle("dark");
              setDarkmode(!darkmode);
            }}
          >
            {darkmode ? (
              <RiSunFill className="h-5 w-5" />
            ) : (
              <RiMoonFill className="h-5 w-5" />
            )}
          </button>

          {/* Profile Dropdown */}
          <Dropdown
            button={
              <img
                className="h-8 w-8 sm:h-10 sm:w-10 rounded-full"
                src={avatar}
                alt="Profile"
              />
            }
            children={
              <div className="flex w-56 flex-col justify-start rounded-[20px] bg-white bg-cover bg-no-repeat shadow-xl shadow-shadow-500 dark:!bg-navy-700 dark:text-white dark:shadow-none">
                <div className="p-4">
                  <div className="flex items-center gap-2">
                    <p className="text-sm font-bold text-navy-700 dark:text-white">
                      👋 Hey, {Cookies.get('role')}
                    </p>
                  </div>
                </div>
                <div className="h-px w-full bg-gray-200 dark:bg-white/20" />
                <div className="flex flex-col p-4">
                  <a
                    href=" "
                    className="text-sm text-gray-800 dark:text-white hover:dark:text-white"
                  >
                    Profile Settings
                  </a>
                  <a
                    href=" "
                    onClick={deletecookies}
                    className="mt-3 text-sm font-medium text-red-500 hover:text-red-500 transition duration-150 ease-out hover:ease-in"
                  >
                    Log Out
                  </a>
                </div>
              </div>
            }
            classNames={"py-2 top-8 -left-[180px] w-max"}
          />
        </div>
      </div>
    </nav>
  );
};

export default CustomNavbar;