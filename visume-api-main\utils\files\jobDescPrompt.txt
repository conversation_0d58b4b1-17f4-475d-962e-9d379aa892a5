Given the extracted text from a PDF Job Description, structure the information into a well-organized JSON format. The JSON should include only the following key sections as found in Job Descriptions:

    Role: The job title or position name.
    Skills: A list of containing all skills in an single array.
    Experience: The range of experience required for the role, categorized as:
        return  "0-1" for 0-1 years
        return  "2-3" for 2-3 years
        return  "4-5" for 4-5 years
        return "5+" for  5+ years
    Location: An array of possible locations for the role, if mentioned in the job description.
    Salary: If provided, include salary or compensation details.

Ensure the JSON is properly formatted, with clear and consistent naming conventions for the fields and sections. The JSON should be optimized for easy readability and machine processing.
Example JSON Structure:

{
  "role": "Senior Software Engineer",
  "skills": ["JavaScript", "Python", "React", "Node.js", "CSS", "HTML", "Tailwind CSS", "Django" ],
  "experience": "0-1",
  },
  "location": [
    "Remote",
    "New York, USA",
    "San Francisco, USA"
  ]
}
