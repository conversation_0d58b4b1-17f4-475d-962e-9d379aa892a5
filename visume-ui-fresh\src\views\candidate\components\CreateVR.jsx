import React, { useState, useRef, useEffect } from "react";
import Cookies from "js-cookie";
import { useNavigate } from "react-router-dom";
import Loader from "components/Loader";
import toast from "react-hot-toast";
import { HiAcademicCap, HiBriefcase, HiLightBulb, HiOfficeBuilding, HiOutlineVideoCamera, HiStar, HiUserGroup } from "react-icons/hi";
import { X } from "lucide-react";

const CreateVR = ({ togglePopupVR }) => {
  const navigate = useNavigate();
  const [step, setStep] = useState(1);

  const candId = Cookies.get("candId"); // Retrieve the candId from the cookie

  const saveqnscookie = (data) => {
    try {
      if (!data || !data.questions) {
        throw new Error("Invalid data format: missing questions");
      }
      
      const cookieData = JSON.stringify(data);
      // console.log("Saving to cookie:", cookieData);
      
      Cookies.set("CreateVRres", cookieData, { expires: 7 }); // Expires in 7 days
      
      // Verify the data was saved correctly
      const savedData = Cookies.get("CreateVRres");
      if (!savedData) {
        throw new Error("Failed to save questions to cookie");
      }
    } catch (err) {
      console.error("Error saving to cookie:", err);
      throw err;
    }
  };

  const [formData, setFormData] = useState({
    candId: candId,
    jobRole: "",
    skills: [],
    companyType: "",
    experience: "",
    salary: {
      current: "",
      expected: "",
    },
  });
  const [skillInput, setSkillInput] = useState("");
  const [jobRoleInput, setJobRoleInput] = useState("");
  const [jobRoleSuggestions, setJobRoleSuggestions] = useState([]);
  const [skillSuggestions, setSkillSuggestions] = useState([]);
  const [showJobRoleSuggestions, setShowJobRoleSuggestions] = useState(false);
  const [showSkillSuggestions, setShowSkillSuggestions] = useState(false);
  const formRef = useRef(null);
  const skillRef = useRef(null);
  const jobRef = useRef(null);
  const [error, setError] = useState(""); // Error state

  const [isLoading, setIsLoading] = useState(false);

  const fetchJobRoles = async () => {
    try {
      const response = await fetch(`${import.meta.env.VITE_APP_HOST}/api/v1/get-roles`);
      const data = await response.json();
  
      if (data && Array.isArray(data.roles)) {
        return data.roles.map((role) => role.role_name);
      } else {
        console.warn("Roles data is not in the expected format.");
        return [];
      }
    } catch (error) {
      console.error("Error fetching roles:", error);
      return [];
    }
  };
  
  const fetchSkills = async () => {
    try {
      const response = await fetch(`${import.meta.env.VITE_APP_HOST}/api/v1/get-skills`);
      const data = await response.json();
  
      if (data && Array.isArray(data.skills)) {
        return data.skills.map((skill) => skill.skill_name);
      } else {
        console.warn("Skills data is not in the expected format.");
        return [];
      }
    } catch (error) {
      console.error("Error fetching skills:", error);
      return [];
    }
  };
  
  const fetchJobRolesAndSkills = async () => {
    try {
      // Fetch roles and skills in parallel
      const [roleNames, skillNames] = await Promise.all([fetchJobRoles(), fetchSkills()]);
  
      // Update state with the fetched data
      setJobRoleSuggestions(roleNames || []);
      setSkillSuggestions(skillNames || []);
  
      if (roleNames.length === 0) console.warn("No roles found.");
      if (skillNames.length === 0) console.warn("No skills found.");
  
      // Show error only if both fetches failed
      if (roleNames.length === 0 && skillNames.length === 0) {
        throw new Error("Failed to fetch both roles and skills");
      }
    } catch (error) {
      console.error("Fetch error:", error);
      toast.error("Failed to load roles and skills");
    }
  };
  
  
  useEffect(() => {
    fetchJobRolesAndSkills();
  }, []);

 
  // Handle Job Role input
  const handleJobRoleInput = (e) => {
    const value = e.target.value.trim().toLowerCase(); // Normalize input
    setJobRoleInput(e.target.value); // Update the raw input value
    setFormData({ ...formData, jobRole: e.target.value }); // Update form data
  
    if (value === "") {
      // If the input is empty, show the full list of suggestions
      setShowJobRoleSuggestions(false);
      setJobRoleSuggestions([]);
    } else {
      // Filter from the original list of suggestions
      const filteredSuggestions = jobRoleSuggestions.filter((role) =>
        role.toLowerCase().includes(value)
      );
  
      setShowJobRoleSuggestions(filteredSuggestions.length > 0); // Show suggestions if there are matches
      setJobRoleSuggestions(filteredSuggestions); // Update suggestions list
      setShowSkillSuggestions(false); // Hide skill suggestions
    }
  };

  const handleJobRoleSuggestionClick = (suggestion) => {
    setJobRoleInput(suggestion);
    setFormData({ ...formData, jobRole: suggestion });
    setShowJobRoleSuggestions(false);
  };

  // Handle Skill input
  const handleSkillInput = (e) => {
    const value = e.target.value.trim().toLowerCase(); // Normalize input
    setSkillInput(e.target.value); // Update the raw input value
  
    if (value === "") {
      // If the input is empty, show the full list of suggestions
      setShowSkillSuggestions(false);
      setSkillSuggestions([]);
    } else {
      // Filter from the original list of suggestions
      const filteredSuggestions = skillSuggestions.filter(
        (skill) =>
          skill.toLowerCase().includes(value) &&
          !formData.skills.includes(skill) // Exclude already selected skills
      );
  
      setShowSkillSuggestions(filteredSuggestions.length > 0); // Show suggestions if there are matches
      setSkillSuggestions(filteredSuggestions); // Update suggestions list
      setShowJobRoleSuggestions(false); // Hide job role suggestions
    }
  };

  const handleSkillSuggestionClick = (suggestion) => {
    setSkillInput(""); // Clear the input after selection
    setFormData({
      ...formData,
      skills: [...formData.skills, suggestion], // Add the selected skill
    });
    setShowSkillSuggestions(false); // Hide suggestions
  };


  const addSkill = (skill) => {
    if (!formData.skills.includes(skill)) {
      setFormData({
        ...formData,
        skills: [...formData.skills, skill],
      });
      setSkillInput("");
      setShowSkillSuggestions(false);
    }
  };

  const removeSkill = (skillToRemove) => {
    setFormData({
      ...formData,
      skills: formData.skills.filter((skill) => skill !== skillToRemove),
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    setIsLoading(true); // Show the loader
    console.log("Form submitted:", formData);
    // saveFormDataToCookie(formData); // Save form data to a cookie

    const url = `${import.meta.env.VITE_APP_HOST}/api/v1/create-video-resume`;
    console.log("Post method to", url);

    // Make an API call using fetch
    fetch(url, {
      method: "POST", // HTTP method
      headers: {
        "Content-Type": "application/json", // Specify JSON format
      },
      body: JSON.stringify(formData), // Convert formData to JSON string
    })
      .then((response) => {
        const data = response.json().then((data) => {
          if (!response.ok) {
            toast.error(data.message);
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          return data; // Parsed JSON response
        });
        return data
      })
      .then((data) => {
        // Add detailed logging
        console.log("API Response:", data);
        console.log("Questions from API:", data.questions);
        
        // Validate questions array before saving
        if (!data.questions || !Array.isArray(data.questions) || data.questions.length === 0) {
          throw new Error("No questions received from API");
        }
        
        localStorage.setItem("formData", JSON.stringify(formData));
        saveqnscookie(data); // save questions and videoProfileId
        
        // Verify cookie was set
        const savedData = Cookies.get("CreateVRres");
        console.log("Saved cookie data:", savedData);
        
        navigate(`/candidate/interview/${data.videoProfileId}`);
      })
      .catch((error) => {
        console.error("API Error:", error);
        
        let errorMessage = "Failed to generate interview questions. ";
        
        if (error.message === "No questions received from API") {
          errorMessage += "Please try again or contact support if the issue persists.";
        } else if (error.response?.data?.message) {
          errorMessage += error.response.data.message;
        } else {
          errorMessage += "An unexpected error occurred.";
        }
        
        toast.error(errorMessage);
        setIsLoading(false);
      })
      .finally(() => {
        setIsLoading(false); // Hide the loader
        // const createVRResJson = Cookies.get("CreateVRres");
        // const parsedData = JSON.parse(createVRResJson);
        // const videoProfileId = parsedData.videoProfileId;
        // navigate(`/candidate/interview/${videoProfileId}`);

        // window.location.reload(); // Reload the page
      });
  };

  const handleNextStep = (e) => {
    e.preventDefault();
    // Validate based on current step
    if (step === 1) {
      if (!formData.jobRole || formData.skills.length === 0) {
        setError("Please select a job role and add at least one skill.");
        return;
      }
    } else if (step === 2) {
      if (!formData.companyType) {
        setError("Please select a company type.");
        return;
      }
    } else if (step === 3) {
      if (!formData.experience) {
        setError("Please select your experience level.");
        return;
      }
      if (
        (formData.experience === "2-3" || formData.experience === "4-5") &&
        (!formData.salary.current || !formData.salary.expected)
      ) {
        setError("Please enter both current and expected salary.");
        return;
      }
    }
    setError(""); // Clear the error if validation passes
    if (step < 4) {
      setStep(step + 1);
    }
  };

  const handlePrevStep = () => {
    if (step > 1) {
      setStep(step - 1);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;

    if (name === "currentSalary") {
      // Handling the nested salary.current
      setFormData((prevState) => ({
        ...prevState,
        salary: {
          ...prevState.salary,
          current: value, // Update only salary.current
        },
      }));
    } else if (name === "expectedSalary") {
      // Handling the nested salary.expected
      setFormData((prevState) => ({
        ...prevState,
        salary: {
          ...prevState.salary,
          expected: value, // Update only salary.expected
        },
      }));
    } else {
      // Handling other fields
      setFormData({ ...formData, [name]: value });
    }
  };

  const salaryOptions = [
    { value: "0-5", label: "0-5 LPA" },
    { value: "5-10", label: "5-10 LPA" },
    { value: "10-15", label: "10-15 LPA" },
    { value: "15-20", label: "15-20 LPA" },
    { value: "20+", label: "20+ LPA" },
  ];

  return (
    <div className="fixed inset-0 z-[9999] flex h-screen w-screen items-center justify-center bg-gray-800 bg-opacity-50 backdrop-blur-sm">
      <div className="w-full p-3">
        <div
          ref={formRef}
          className="mx-auto max-w-2xl rounded-md bg-white px-8 py-4"
        >
          <div className="mb-5 mt-0 flex w-full items-center justify-between">
            <h2 className="text-center text-xl mt-2 font-semibold text-blue-800 flex items-center gap-2">
              <HiOutlineVideoCamera /> Create Video Resume
            </h2>
            <div className="">
              <button
                onClick={togglePopupVR}
                className=" text-gray-600 hover:text-gray-900"
              >
                <svg
                  className="h-6 w-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M6 18L18 6M6 6l12 12"
                  ></path>
                </svg>
              </button>
            </div>
          </div>

          <form
            id="multistepForm"
            className="flex flex-col justify-between rounded-lg"
            onSubmit={handleSubmit}
          >
            {/* Progress Bar */}
            <div className="relative mb-8 h-4 rounded-full bg-gray-200">
              <div
                id="progressBar"
                className="absolute left-0 top-0 h-4 rounded-full bg-blue-600 transition-all duration-500"
                style={{ width: `${(step / 4) * 100}%` }}
              ></div>
            </div>

            {/* Error Message */}
            {error && (
              <div className="mb-4 font-semibold text-red-500">{error}</div>
            )}

            {/* Step 1 */}
            {step === 1 && (
              <div className="step">
                <div>
                  <h1 className="mb-1 text-2xl font-semibold text-gray-800">
                    Select Job Role and Skills
                  </h1>
                  <p className="mb-6 text-gray-500">
                    Choose a job role and add your skills.
                  </p>
                </div>

                {/* Job Role Input */}
                <div className="relative mb-4">
                  <label htmlFor="jobRole" className="mb-2 block text-gray-700">
                    Job Role
                  </label>
                  <input
                    required
                    id="jobRole"
                    name="jobRole"
                    type="text"
                    value={jobRoleInput}
                    onChange={handleJobRoleInput}
                    onFocus={() => setShowJobRoleSuggestions(true)}
                    placeholder="Please select a job role or type your own."
                    className="w-full rounded-lg border border-gray-300 px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  {showJobRoleSuggestions && jobRoleSuggestions.length > 0 && (
                    <ul
                      ref={jobRef}
                      className="absolute z-10 mt-1 max-h-40 w-full overflow-y-auto rounded-lg border border-gray-300 bg-white shadow-lg"
                    >
                      {jobRoleSuggestions.map((suggestion, index) => (
                        <li
                          key={index}
                          className="cursor-pointer px-4 py-2 hover:bg-blue-100"
                          onClick={() =>
                            handleJobRoleSuggestionClick(suggestion)
                          }
                        >
                          {suggestion}
                        </li>
                      ))}
                    </ul>
                  )}
                </div>

                {/* Skills Input */}
                <div className="relative">
                  <label className="mb-2 block text-gray-700">Skills</label>
                  <input
                    type="text"
                    id="skill-input"
                    value={skillInput}
                    onChange={handleSkillInput}
                    onFocus={() => setShowSkillSuggestions(true)}
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        e.preventDefault(); // Prevent form submission or any other default action
                        // If user presses Enter without selecting a suggestion, manually add the input as a skill
                        if (skillInput.trim()) {
                          addSkill(skillInput.trim());
                          setSkillInput(""); // Reset input after adding
                        }
                      }
                    }}
                    className="w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500"
                    placeholder="Please add a skill or select from suggestions."
                  />
                  {showSkillSuggestions && skillSuggestions.length > 0 && (
                    <ul
                      ref={skillRef}
                      className="absolute z-10 mt-1 max-h-40 w-full overflow-y-auto rounded-lg border border-gray-300 bg-white shadow-lg"
                    >
                      {skillSuggestions.map((suggestion, index) => (
                        <li
                          key={index}
                          className="cursor-pointer px-4 py-2 hover:bg-blue-100"
                          onClick={() => addSkill(suggestion)}
                        >
                          {suggestion}
                        </li>
                      ))}
                    </ul>
                  )}
                  <div className="mt-3 h-auto max-h-[100px] w-full overflow-y-auto">
                    <div id="skills-container" className="flex flex-wrap gap-2">
                      {formData.skills.map((skill, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center gap-1 px-3 py-1 bg-blue-50 text-blue-700 rounded-full text-sm"
                        >
                          {skill}
                          <button
                            type="button"
                            className="hover:bg-blue-100 rounded-full p-0.5 transition-colors"
                            onClick={() => removeSkill(skill)}
                          >
                            <X size={14} />
                          </button>
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Step 2 */}
            {step === 2 && (
              <div className="step">
                <h1 className="mb-1 text-3xl font-semibold text-gray-800">
                  Type of Company
                </h1>
                <p className="mb-6 text-gray-500">
                  Select your preferred type of company.
                </p>
                <div className="flex space-x-4">
                  <label
                    className={`flex flex-col items-center justify-center w-32 cursor-pointer rounded-lg border p-4 text-center transition ${
                      formData.companyType === "MNC"
                        ? "border-blue-500 bg-blue-100 shadow-md"
                        : "hover:bg-gray-50"
                    }`}
                  >
                    <HiOfficeBuilding
                      className={`mb-2 h-8 w-8 ${
                        formData.companyType === "MNC" ? "text-blue-500" : "text-gray-400"
                      }`}
                    />
                    <input
                      type="radio"
                      name="companyType"
                      value="MNC"
                      onChange={handleInputChange}
                      className="hidden"
                    />
                    <span className="text-sm font-medium text-gray-700">MNC</span>
                  </label>
                  <label
                    className={`flex flex-col items-center justify-center w-32 cursor-pointer rounded-lg border p-4 text-center transition ${
                      formData.companyType === "Startup"
                        ? "border-blue-500 bg-blue-100 shadow-md"
                        : "hover:bg-gray-50"
                    }`}
                  >
                    <HiLightBulb
                      className={`mb-2 h-8 w-8 ${
                        formData.companyType === "Startup" ? "text-blue-500" : "text-gray-400"
                      }`}
                    />
                    <input
                      type="radio"
                      name="companyType"
                      value="Startup"
                      onChange={handleInputChange}
                      className="hidden"
                    />
                    <span className="text-sm font-medium text-gray-700">Startup</span>
                  </label>
                  <label
                    className={`flex flex-col items-center justify-center w-32 cursor-pointer rounded-lg border p-4 text-center transition ${
                      formData.companyType === "Both"
                        ? "border-blue-500 bg-blue-100 shadow-md"
                        : "hover:bg-gray-50"
                    }`}
                  >
                    <HiUserGroup
                      className={`mb-2 h-8 w-8 ${
                        formData.companyType === "Both" ? "text-blue-500" : "text-gray-400"
                      }`}
                    />
                    <input
                      type="radio"
                      name="companyType"
                      value="Both"
                      onChange={handleInputChange}
                      className="hidden"
                    />
                    <span className="text-sm font-medium text-gray-700">Both</span>
                  </label>
                </div>
              </div>
            )}

            {/* Step 3 */}
            {step === 3 && (
              <div className="step">
                <h1 className="mb-0 text-3xl font-semibold text-gray-800">Experience Level</h1>
                <p className="mb-6 text-gray-500">Select your experience level.</p>

                <div className="flex space-x-4">
                  {/* Fresher */}
                  <label
                    className={`flex flex-col items-center justify-center w-40 cursor-pointer rounded-lg border p-4 text-center transition ${
                      formData.experience === "0-1"
                        ? "border-blue-500 bg-blue-100 shadow-md"
                        : "hover:bg-gray-50"
                    }`}
                  >
                    <HiAcademicCap
                      className={`mb-2 h-8 w-8 ${
                        formData.experience === "0-1" ? "text-blue-500" : "text-gray-400"
                      }`}
                    />
                    <input
                      type="radio"
                      name="experience"
                      value="0-1"
                      onChange={handleInputChange}
                      className="hidden"
                    />
                    <span className="text-sm font-medium text-gray-700">Fresher</span>
                  </label>

                  {/* Intermediate */}
                  <label
                    className={`flex flex-col items-center justify-center w-40 cursor-pointer rounded-lg border p-4 text-center transition ${
                      formData.experience === "2-3"
                        ? "border-blue-500 bg-blue-100 shadow-md"
                        : "hover:bg-gray-50"
                    }`}
                  >
                    <HiBriefcase
                      className={`mb-2 h-8 w-8 ${
                        formData.experience === "2-3" ? "text-blue-500" : "text-gray-400"
                      }`}
                    />
                    <input
                      type="radio"
                      name="experience"
                      value="2-3"
                      onChange={handleInputChange}
                      className="hidden"
                    />
                    <span className="text-sm font-medium text-gray-700">2-3 years Intermediate</span>
                  </label>

                  {/* Experienced */}
                  <label
                    className={`flex flex-col items-center justify-center w-40 cursor-pointer rounded-lg border p-4 text-center transition ${
                      formData.experience === "4-5"
                        ? "border-blue-500 bg-blue-100 shadow-md"
                        : "hover:bg-gray-50"
                    }`}
                  >
                    <HiStar
                      className={`mb-2 h-8 w-8 ${
                        formData.experience === "4-5" ? "text-blue-500" : "text-gray-400"
                      }`}
                    />
                    <input
                      type="radio"
                      name="experience"
                      value="4-5"
                      onChange={handleInputChange}
                      className="hidden"
                    />
                    <span className="text-sm font-medium text-gray-700">4-5 years Experienced</span>
                  </label>
                </div>

                {/* Conditional Salary Fields */}
                {(formData.experience === "2-3" || formData.experience === "4-5") && (
                  <div className="mt-6 space-y-4">
                    {/* Current Salary */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Current Salary</label>
                      <select
                        value={formData.salary.current}
                        onChange={(e) =>
                          setFormData({
                            ...formData,
                            salary: {
                              current: e.target.value,
                              expected: formData.salary.expected,
                            },
                          })
                        }
                        className="w-full rounded-lg border border-gray-300 px-3 py-2 text-sm shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500"
                      >
                        <option value="">Select current salary</option>
                        {salaryOptions.map((option) => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Expected Salary */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Expected Salary</label>
                      <select
                        value={formData.salary.expected}
                        onChange={(e) =>
                          setFormData({
                            ...formData,
                            salary: {
                              current: formData.salary.current,
                              expected: e.target.value,
                            },
                          })
                        }
                        className="w-full rounded-lg border border-gray-300 px-3 py-2 text-sm shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500"
                      >
                        <option value="">Select expected salary</option>
                        {salaryOptions.map((option) => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Step 4 */}
            {step === 4 && (
              <div className="step mx-auto w-full rounded-lg bg-white p-4">
                <h1 className="mb-4 text-center text-2xl font-bold text-gray-800">
                  Confirm Your Details
                </h1>
                <p className="mb-6 text-center text-gray-500">
                  Please review your selections.
                </p>
                <div className="flex w-full items-center justify-center">
                  <div className="mb-6 w-full max-w-md rounded-lg border border-gray-200 bg-gray-100 p-5 shadow-inner">
                    <ul className="space-y-4 text-gray-700">
                      <li className="flex justify-between">
                        <span className="font-medium text-gray-600">
                          Job Role:
                        </span>
                        <span className="font-semibold text-gray-800">
                          {formData.jobRole}
                        </span>
                      </li>
                      <li className="flex justify-between">
                        <span className="font-medium text-gray-600">
                          Skills:
                        </span>
                        <span className="font-semibold text-gray-800">
                          {formData.skills.join(", ")}
                        </span>
                      </li>
                      <li className="flex justify-between">
                        <span className="font-medium text-gray-600">
                          Company Type:
                        </span>
                        <span className="font-semibold text-gray-800">
                          {formData.companyType}
                        </span>
                      </li>
                      <li className="flex justify-between">
                        <span className="font-medium text-gray-600">
                          Experience:
                        </span>
                        <span className="font-semibold text-gray-800">
                          {formData.experience}
                        </span>
                      </li>
                      {(formData.experience === "2-3" ||
                        formData.experience === "4-5") && (
                        <>
                          <li className="flex justify-between">
                            <span className="font-medium text-gray-600">
                              Current Salary:
                            </span>
                            <span className="font-semibold text-gray-800">
                              {formData.salary.current}
                            </span>
                          </li>
                          <li className="flex justify-between">
                            <span className="font-medium text-gray-600">
                              Expected Salary:
                            </span>
                            <span className="font-semibold text-gray-800">
                              {formData.salary.expected}
                            </span>
                          </li>
                        </>
                      )}
                    </ul>
                  </div>
                </div>
                <p className="mb-8 text-center text-gray-500">
                  Click submit to create your video profile.
                </p>
              </div>
            )}

            {/* Navigation Buttons */}
            <div className="mt-8 flex justify-between">
              {step > 1 && (
                <button
                  type="button"
                  id="prevBtn"
                  className="rounded-lg text-blue-600 bg-white px-4 py-2 mb-2 font-semibold transition-all duration-300 ease-in-out transform hover:bg-blue-600 hover:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                  onClick={handlePrevStep}
                >
                  Previous
                </button>
              )}
              {step < 4 ? (
                <button
                  type="button"
                  id="nextBtn"
                  className="rounded-lg bg-blue-600 px-4 py-2 mb-2 font-semibold text-white transition-all duration-300 ease-in-out transform hover:bg-blue-700 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                  onClick={handleNextStep}
                >
                  Next
                </button>
              ) : (
                <button
                  type="submit"
                  id="submitBtn"
                  className="rounded-lg bg-green-600 px-4 py-2 mb-2 font-semibold text-white transition hover:bg-green-700"
                >
                  Submit
                </button>
              )}
            </div>
          </form>

          {/* Loader */}
          {isLoading && <Loader text={"Creating your video profile..."} />}
        </div>
      </div>
    </div>
  );
};

export default CreateVR;
