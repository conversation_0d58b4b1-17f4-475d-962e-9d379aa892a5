const { generateSingleQuestion } = require("../utils/helpers");

exports.generateNextQuestion = async (req, res) => {
  try {
    const { role, skills, previousQuestions, previousAnswers } = req.body;

    // Validate required fields
    if (!role) {
      return res.status(400).json({
        success: false,
        message: "Role is required for question generation"
      });
    }

    // Check if we've met minimum questions requirement
    const questionCount = (previousQuestions || []).length + 1;
    const MIN_QUESTIONS = 5;
    console.log(`Generating question ${questionCount} of minimum ${MIN_QUESTIONS}`);

    // Handle skills and ensure variety in question types
    let skillsArray = [];
    if (skills) {
      skillsArray = Array.isArray(skills) ? skills : [skills];
    } else {
      console.warn("No skills provided, using general questions");
      skillsArray = ["general"];
    }

    // Analyze previous questions for context
    const previousTypes = (previousQuestions || []).map(q => q.type);
    const lastType = previousTypes[previousTypes.length - 1];
    const technicalCount = previousTypes.filter(t => t === 'technical').length;
    const behavioralCount = previousTypes.filter(t => t === 'behavioral').length;

    // Determine next question type based on balance
    let preferredType;
    if (questionCount <= MIN_QUESTIONS) {
      if (technicalCount < 3) {
        preferredType = 'technical';
      } else if (behavioralCount < 2) {
        preferredType = 'behavioral';
      } else {
        preferredType = lastType === 'technical' ? 'behavioral' : 'technical';
      }
    } else {
      // After minimum questions, alternate types
      preferredType = lastType === 'technical' ? 'behavioral' : 'technical';
    }

    console.log('Question context:', {
      questionCount,
      technicalCount,
      behavioralCount,
      preferredType
    });

    // Combine previous questions and answers
    const previousQA = (previousQuestions || []).map((q, index) => {
      const answer = previousAnswers ? previousAnswers[index] : null;
      console.log(`Previous Q&A ${index + 1}:`, { question: q.question, answer });
      return {
        question: q.question,
        answer: answer,
        type: q.type // Include question type for better context
      };
    });
  
    // Log request details for debugging
    console.log('Generating question for:', {
      role,
      skillsArray,
      questionCount: previousQA.length + 1
    });

    const nextQuestion = await generateSingleQuestion(role, previousQA, skillsArray, preferredType);

    // Handle fallback case
    if (nextQuestion._fallback) {
      res.status(200).json({
        success: true,
        question: nextQuestion.question,
        type: nextQuestion.type,
        keywords: nextQuestion.keywords,
        difficulty: nextQuestion.difficulty,
        time_limit: "2 minutes",
        _fallback: true
      });
    } else {
      res.status(200).json({
        success: true,
        question: nextQuestion.question,
        type: nextQuestion.type,
        keywords: nextQuestion.keywords,
        difficulty: nextQuestion.difficulty,
        time_limit: nextQuestion.time_limit || "2 minutes"
      });
    }

  } catch (error) {
    console.error("Error generating next question:", error);
    console.error('Question generation error:', error);
    
    // Determine appropriate fallback based on previous questions
    let fallbackQuestion;
    if (previousQA.length === 0) {
      fallbackQuestion = {
        question: `Tell me about your experience with ${role}`,
        type: "behavioral",
        keywords: ["experience", role],
        difficulty: "medium",
        time_limit: "2 minutes"
      };
    } else {
      const lastQuestion = previousQA[previousQA.length - 1];
      fallbackQuestion = {
        question: lastQuestion.type === "behavioral"
          ? `What technical challenges have you faced in your ${role} role?`
          : "How do you handle difficult situations in your work?",
        type: lastQuestion.type === "behavioral" ? "technical" : "behavioral",
        keywords: ["problem-solving", "challenges", "experience"],
        difficulty: "medium",
        time_limit: "2 minutes"
      };
    }

    res.status(500).json({
      success: false,
      message: error.message || "Failed to generate next question",
      error_details: error.stack,
      fallback: fallbackQuestion
    });
  }
};