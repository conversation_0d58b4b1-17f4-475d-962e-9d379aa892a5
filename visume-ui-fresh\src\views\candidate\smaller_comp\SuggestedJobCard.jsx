import React from 'react';

const SuggestedJobCard = ({ job, iconUrl }) => {
  return (
    <div className="p-4 bg-white dark:bg-navy-900 rounded-lg shadow-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-300 ease-in-out border border-light-gray cursor-pointer">
      <div
        className="flex items-center gap-4"
        onClick={() => window.open(job.url, '_blank', 'noopener,noreferrer')}
      >
        {/* Image */}
        <div className="flex-shrink-0">
          <img src={iconUrl} alt="Company Logo" className="w-12 h-12 rounded-full" />
        </div>

        {/* Text */}
        <div className="flex-1">
          <h2 className="text-base font-semibold text-gray-900 dark:text-white">
            {job.title}
          </h2>
          <div className="mt-1 text-sm text-gray-700 dark:text-gray-400">
            {job.company}
          </div>
          <div className="mt-1 text-xs font-medium text-gray-600 dark:text-gray-500">
            {job.openings} Openings
          </div>
        </div>
      </div>
    </div>
  );
};

export default SuggestedJobCard;
