const { S3Client, PutObjectCommand, HeadBucketCommand } = require("@aws-sdk/client-s3");
const { getSignedUrl } = require("@aws-sdk/s3-request-presigner");
const dotenv = require("dotenv");

dotenv.config();

async function validateS3Connection() {
  // Validate environment variables
  const configStatus = {
    region: process.env.AWS_REGION ? '✓' : '✗',
    bucket: process.env.AWS_BUCKET_NAME ? '✓' : '✗',
    accessKeyId: process.env.AWS_ACCESS_KEY_ID ? '✓' : '✗',
    secretKey: process.env.AWS_SECRET_ACCESS_KEY ? '✓' : '✗'
  };

  try {

    console.log('S3 Configuration Status:');
    console.log('- Region:', configStatus.region, process.env.AWS_REGION || 'missing');
    console.log('- Bucket:', configStatus.bucket, process.env.AWS_BUCKET_NAME || 'missing');
    console.log('- Access Key:', configStatus.accessKeyId);
    console.log('- Secret Key:', configStatus.secretKey);

    if (!process.env.AWS_REGION || !process.env.AWS_BUCKET_NAME) {
      throw new Error('Missing required AWS configuration');
    }

    // Test credentials
    await s3Client.config.credentials();
    console.log('AWS credentials validated');
    
    // Test bucket access
    const command = new HeadBucketCommand({
      Bucket: process.env.AWS_BUCKET_NAME,
    });

    await s3Client.send(command);
    console.log('Successfully validated S3 bucket access');
    return {
      success: true,
      config: configStatus
    };
  } catch (err) {
    console.error('S3 validation error:', {
      name: err.name,
      message: err.message,
      code: err.code
    });
    return {
      success: false,
      error: err.name,
      message: err.message,
      code: err.code,
      config: configStatus
    };
  }
}

let s3Client = null;

// Initialize S3 client if environment variables are available
const initializeS3Client = () => {
  if (!process.env.AWS_ACCESS_KEY_ID || !process.env.AWS_SECRET_ACCESS_KEY || !process.env.AWS_REGION) {
    console.error('Missing required AWS configuration. Please ensure these environment variables are set:');
    console.error('- AWS_REGION');
    console.error('- AWS_ACCESS_KEY_ID');
    console.error('- AWS_SECRET_ACCESS_KEY');
    console.error('- AWS_BUCKET_NAME');
    return false;
  }

  try {
    s3Client = new S3Client({
      region: process.env.AWS_REGION,
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      },
    });

    // Test the S3 client configuration
    s3Client.config.credentials().catch(err => {
      console.error('Failed to load AWS credentials:', err);
      s3Client = null;
      return false;
    });

    return true;
  } catch (err) {
    console.error('Error initializing S3 client:', err);
    s3Client = null;
    return false;
  }
};

// Initialize S3 on module load
const initialized = initializeS3Client();
if (!initialized) {
  console.error('Failed to initialize S3 client. S3 operations will not be available.');
}

// Health check endpoint
exports.checkS3Health = async function (req, res) {
  console.log('[DEBUG] S3 health check requested');
  
  if (!s3Client) {
    return res.status(503).json({
      status: 'unhealthy',
      error: 'S3ClientNotInitialized',
      message: 'S3 client is not initialized',
      configuration: null
    });
  }

  const validation = await validateS3Connection();
  
  if (validation.success) {
    res.status(200).json({
      status: 'healthy',
      message: 'S3 connection validated',
      configuration: validation.config
    });
  } else {
    res.status(503).json({
      status: 'unhealthy',
      error: validation.error,
      message: validation.message,
      code: validation.code,
      configuration: validation.config
    });
  }
};

exports.getS3Url = async function (req, res) {
  if (!s3Client) {
    return res.status(503).json({
      error: "Storage service unavailable",
      details: "S3 client is not initialized"
    });
  }

  console.log('Generating S3 URL for file:', req.params.fileName);
  try {
    const fileName = req.params.fileName;
    const contentType = req.query.contentType || "application/octet-stream";

    if (!process.env.AWS_BUCKET_NAME) {
      throw new Error("S3 bucket name not configured");
    }

    const command = new PutObjectCommand({
      Bucket: process.env.AWS_BUCKET_NAME,
      Key: fileName,
      ContentType: contentType,
    });

    const url = await getSignedUrl(s3Client, command, { expiresIn: 300 });
    res.status(200).json({ url });

  } catch (err) {
    console.error("Error generating signed URL:", err);

    if (err.name === "CredentialsProviderError") {
      return res.status(503).json({
        error: "Storage service unavailable",
        details: "Invalid AWS credentials configuration"
      });
    }

    if (err.name === "NoSuchBucket") {
      return res.status(503).json({
        error: "Storage service unavailable",
        details: "Unable to access storage bucket. Please contact support."
      });
    }

    res.status(503).json({
      error: "Storage service unavailable",
      details: "Unable to access storage bucket. Please contact support."
    });
  }
};

// exports.getUploadUrlFromCacheOrNew = async function (req,res) {
//     if (!cachedUploadUrl || !cachedAuthToken) {
//       await generateNewUploadUrl();
//     } else {
//       try {
//         await axios.head(cachedUploadUrl);
//       } catch (error) {
//         if (
//           error.response &&
//           (error.response.status === 503 ||
//             error.response.code === "expired_auth_token")
//         ) {
//           await generateNewUploadUrl();
//         } else {
//           console.info("Using cached upload URL and auth token");
//         }
//       }
//     }
  
//       res.status(200).json({ uploadUrl: cachedUploadUrl, authToken: cachedAuthToken });
  
//   };
  
//   async function generateNewUploadUrl() {
//     const authDetails = await authorizeB2();
//     const uploadUrlData = await getUploadUrl(
//       authDetails.apiUrl,
//       authDetails.authToken
//     );
//     cachedUploadUrl = uploadUrlData.uploadUrl;
//     cachedAuthToken = uploadUrlData.authorizationToken;
//   }
  
//   async function authorizeB2() {
//     const authRes = await axios({
//       method: "GET",
//       url: `https://api.backblazeb2.com/b2api/v3/${apiMethodName}`,
//       auth: {
//         username: "005521a5a22d8eb0000000001", // Replace with your actual applicationKeyId
//         password: "K005nfgdB52wn6vv3Z+OCB1/E8jfBZg", // Replace with your actual applicationKey
//       },
//     });
  
//     console.info("Success getting B2 auth details");
  
//     const data = authRes.data;
//     console.log(data.apiInfo);
  
//     if (!data.apiInfo.storageApi.apiUrl) {
//       throw new Error(`Missing property "apiUrl" in ${apiMethodName} response.`);
//     }
//     if (!data.authorizationToken) {
//       throw new Error(
//         `Missing property "authorizationToken" in ${apiMethodName} response.`
//       );
//     }
  
//     return {
//       apiUrl: data.apiInfo.storageApi.apiUrl,
//       authToken: data.authorizationToken,
//     };
//   }
  
//   async function getUploadUrl(apiUrl, authToken) {
//     const uploadUrlRes = await axios({
//       method: "POST",
//       url: `${apiUrl}/b2api/v3/${getUploadUrlMethodName}`,
//       headers: {
//         Authorization: authToken,
//       },
//       data: {
//         bucketId: "f5b2512a154ad2129d080e1b", // Replace with your actual bucketId
//       },
//     });
  
//     console.info("Success getting upload URL");
  
//     const uploadUrlData = uploadUrlRes.data;
//     if (!uploadUrlData.uploadUrl) {
//       throw new Error(
//         `Missing property "uploadUrl" in ${getUploadUrlMethodName} response.`
//       );
//     }
  
//     return uploadUrlData;
//   }
