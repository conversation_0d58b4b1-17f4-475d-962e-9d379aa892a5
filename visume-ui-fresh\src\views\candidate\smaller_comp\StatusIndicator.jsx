import React from "react";

export default function StatusIndicator({ label, status }) {
  const statusColor = status === "success" ? "text-green-500" : "text-red-500";
  const statusIcon = status === "success" ? "✔" : "✖";

  return (
    <div className="flex items-center">
      <div className="flex h-8 w-8 items-center justify-center rounded-full">
        <span className={`text-xl ${statusColor}`}>{statusIcon}</span>
      </div>
      <p className="status-text ml-2">{label}</p>
    </div>
  );
}
