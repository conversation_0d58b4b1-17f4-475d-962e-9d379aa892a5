import { button } from "@material-tailwind/react";
import React, { useState } from "react";
import toast from "react-hot-toast";
import {
  HiArrowR<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  HiUserGroup,
} from "react-icons/hi";
import { MdDelete } from "react-icons/md";
import { useNavigate } from "react-router-dom";

const VideoProfileCard = ({ profile, toggleVideoProfilePopup }) => {
  const [loader, setLoader] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [resumeIdToDelete, setResumeIdToDelete] = useState(null);

  const deleteVisume = async (id) => {
    try {
      setLoader(true);
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/video-resume/${id}`,
        {
          method: "DELETE",
        }
      );

      if (response.ok) {
        toast.success("Video profile deleted successfully");
        window.location.reload(); // Ensure the page is reloaded to reflect changes
      } else {
        console.error("Failed to delete video profile");
      }
    } catch (error) {
      console.error("Error:", error);
    } finally {
      setLoader(false);
      setIsModalOpen(false); // Close modal after delete attempt
    }
  };

  const handleDeleteClick = (id) => {
    setResumeIdToDelete(id);
    setIsModalOpen(true);
  };

  const navigate = useNavigate();

  return (
    <div className="col-span-full mb-2 rounded-lg bg-gray-100 p-4 transition-all duration-300 ease-in-out dark:bg-navy-900">
      <div className="flex flex-col gap-4">
        {/* Profile Details */}
        <div className="flex items-start justify-between">
          <div className="flex flex-col">
            <div className="mb-1 flex items-center gap-2">
              <h2 className="text-base font-bold text-gray-800 dark:text-white">
                {profile.role}
              </h2>
            </div>
            <ul className="flex flex-wrap gap-2">
              {profile.skills.map((skill, index) => (
                <li
                  key={index}
                  className="rounded-md bg-gray-800 px-2 py-1 text-[11px] text-white dark:bg-blue-600 dark:text-gray-300"
                >
                  {skill}
                </li>
              ))}
            </ul>
          </div>

          {/* View/Continue Button */}
          <div className="flex flex-col sm:flex-row gap-2 items-center">
            {profile.status.toLowerCase() === "notsubmitted" ||
            profile.status.toLowerCase() === "started" ||
            profile.status.trim() === "" ? (
              <>
                <button
                  onClick={() => navigate(`/candidate/interview/${profile.vpid}`)}
                  className="w-full sm:w-auto flex items-center justify-center gap-1 rounded-lg bg-brand-500 px-3 py-1.5 text-sm text-white transition-colors hover:bg-blue-600"
                >
                  <HiArrowRight className="w-4 h-4" />
                  Continue
                </button>
                <button
                  onClick={() => handleDeleteClick(profile.vpid)}
                  className="w-full sm:w-auto flex items-center justify-center gap-1 rounded-lg bg-red-500 px-3 py-1.5 text-sm text-white transition-colors hover:bg-red-600"
                >
                  {loader ? (
                    <svg
                      className="h-4 w-4 animate-spin text-white"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                        fill="none"
                      />
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      />
                    </svg>
                  ) : (
                    <>
                      <MdDelete className="w-4 h-4" />
                      Delete
                    </>
                  )}
                </button>
              </>
            ) : (
              <button
                onClick={() => navigate(`/candidate/videoResume/${profile.vpid}`)}
                className="w-full sm:w-auto flex items-center justify-center gap-1 rounded-full border-2 border-brand-500 px-3 py-1.5 text-sm font-semibold text-brand-500 shadow-md transition-colors hover:bg-brand-500 hover:text-white hover:shadow-lg"
              >
                <HiEye className="w-4 h-4" />
                View
              </button>
            )}
          </div>
        </div>

        {/* Additional Information */}
        <div className="flex items-center justify-start gap-6 text-sm text-gray-600 dark:text-gray-300">
          <div className="flex items-center gap-2">
            <HiEye className="text-blue-500" />
            <span className="font-semibold">10 Views</span>
          </div>
          <div className="flex items-center gap-2">
            <HiClipboardList className="text-green-500" />
            <span className="font-semibold">3 Shortlists</span>
          </div>
          <div className="flex items-center gap-2">
            <HiUserGroup className="text-purple-500" />
            <span className="font-semibold">1 Interviews</span>
          </div>
        </div>
      </div>

      {/* Confirmation Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 flex items-center justify-center bg-gray-500 bg-opacity-50 z-[45]">
          <div className="bg-white rounded-lg p-5 shadow-md">
            <h3 className="text-lg font-bold mb-4">Delete Confirmation</h3>
            <p>Are you sure you want to delete this video resume?</p>
            <div className="mt-4 flex justify-end">
              <button
                onClick={() => setIsModalOpen(false)}
                className="mr-2 rounded-md bg-gray-300 px-4 py-2 text-black"
              >
                Cancel
              </button>
              <button
                onClick={() => deleteVisume(resumeIdToDelete)}
                className="rounded-md bg-red-500 px-4 py-2 text-white"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default VideoProfileCard;