import React from "react";
import Button from "../smaller_comp/Button";

export default function TermsAndConditionsSection({ onAccept }) {
  return (
    <div className="flex h-full w-full items-start justify-center pt-0">
      <div className="flex h-full w-full justify-center">
        <main className="container m-auto mt-0">
          <div className="blue-shadow mx-auto max-w-4xl transform rounded-xl border-2 bg-white p-4 text-center shadow-xl duration-500 hover:scale-105 hover:shadow-2xl">
            <div className="mx-6 flex flex-col items-center justify-center">
              <h1 className="px-18 text-wrap p-12 pb-8 text-2xl">
                Accept Terms and Conditions
              </h1>
              <ul className="list-disc space-y-4 pl-12 text-left text-base font-medium">
                <li>
                  By proceeding, you consent to the sharing of your screen
                  recording, microphone audio, and system audio with the
                  employers for the purpose of this interview.
                </li>
                <li>
                  Please ensure that you are in a quiet environment, free from
                  distractions, to maintain the quality of the interview
                  process.
                </li>
                <li>
                  Note: You will enter full screen after accepting these terms.
                </li>
              </ul>
            </div>
            <div className="row-span-1 my-4 px-6">
              <div className="flex items-center justify-center">
                <Button onClick={onAccept}>Accept</Button>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
