{"name": "horizon-ui-tailwind-react", "version": "1.1.0", "private": true, "dependencies": {"@aws-sdk/client-polly": "^3.699.0", "@aws-sdk/credential-provider-cognito-identity": "^3.699.0", "@chakra-ui/hooks": "^2.1.4", "@chakra-ui/modal": "^2.2.9", "@chakra-ui/popover": "^2.1.8", "@chakra-ui/portal": "^2.0.15", "@chakra-ui/system": "^2.3.5", "@chakra-ui/tooltip": "^2.2.6", "@clerk/clerk-react": "^5.11.1", "@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@ffmpeg/core": "^0.12.6", "@ffmpeg/ffmpeg": "^0.12.10", "@material-tailwind/react": "^2.1.10", "@tanstack/react-table": "^8.7.9", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "@uiw/react-codemirror": "^4.23.2", "apexcharts": "3.35.5", "codemirror": "^5.65.17", "express": "^4.21.1", "framer-motion": "^7.10.2", "groq-sdk": "^0.22.0", "js-cookie": "^3.0.5", "lucide-react": "^0.445.0", "react": "^18.2.0", "react-apexcharts": "1.4.0", "react-calendar": "^3.9.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "react-icons": "^4.12.0", "react-loading-skeleton": "^3.5.0", "react-router-dom": "^6.4.0", "react-type-animation": "^3.2.0", "tailwind-scrollbar": "^3.1.0", "tailwindcss-rtl": "^0.9.0"}, "scripts": {"start": "vite", "build": "vite build", "pretty": "prettier --write \"./**/*.{js,jsx,json}\""}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.8", "postcss": "^8.4.16", "prettier": "^2.8.3", "prettier-plugin-tailwindcss": "^0.2.1", "tailwindcss": "^3.1.8", "vite": "^4.5.5"}}