import React from 'react'

const Loader = ({text}) => {
  return (
 <>
 <div className="absolute inset-0 flex h-screen w-screen items-center justify-center bg-opacity-50 backdrop-blur-md z-50">
 <div className="flex flex-col items-center">
      <svg className="animate-spin h-16 w-16 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"></path>
      </svg>
      <span className="mt-4 text-gray-700">{text?text:<p>loading...</p>}</span>
    </div>
 </div>
 </>
  )
}

export default Loader