// src/views/candidate/CreateVideoProfile.jsx
import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import DeviceTest from "./DeviceTest";

function CreateVideoProfile() {
  const { videoResumeId: videoProfileId } = useParams();
  const navigate = useNavigate();
  const [userConsent, setUserConsent] = useState(false);

  useEffect(() => {
    if (!videoProfileId) {
      // Redirect to the candidate route if no video profile ID is found
      navigate("/candidate");
    }
  }, [videoProfileId, navigate]);

  const handleProceed = () => {
    // Implement device tests and transitions
    console.log("Proceeding to interview");
  };

  return (
    <div>
      {!userConsent ? (
        <div>
          <h1>Consent Form</h1>
          {/* Include form elements for consent and role selection */}
          <button onClick={() => setUserConsent(true)}>Proceed</button>
        </div>
      ) : (
        <DeviceTest onStart={handleProceed} />
      )}
    </div>
  );
}

export default CreateVideoProfile;
