import { useState } from "react";
import { HiClipboard, HiShare, HiVideoCamera } from "react-icons/hi";
import { useLocation } from "react-router-dom";

const SidebarCard = () => {
  const [copied, setCopied] = useState(false);
  const location = useLocation()
  const layout = `/${location.pathname.split("/")[1]}`;

  
  const visumeLink = "https://visume.co.in/referral"; // The link to copy

  const handleCopy = () => {
    navigator.clipboard.writeText(visumeLink)
      .then(() => {
        setCopied(true);
        setTimeout(() => {
          setCopied(false); // Clear the message after a few seconds
        }, 2000);
      })
      .catch(err => console.error('Failed to copy: ', err));
  };
  return (

      <div>
         {layout === "/admin"?"":<div className="relative mt-4 md:mt-14 flex w-[256px] justify-center rounded-[20px] bg-gradient-to-br from-[#868CFF] via-[#432CF3] to-brand-500 pb-4">
      <div className="absolute -top-12 flex h-16 w-16 md:h-20 md:w-20 items-center justify-center rounded-full border-[4px] border-white bg-gradient-to-b from-[#868CFF] to-brand-500 dark:!border-navy-800">
       <HiVideoCamera className="text-white text-4xl" />
      </div>

      <div className="mt-6 md:mt-12 flex h-fit flex-col items-center">
        <p className="text-lg font-bold text-white">Refer a Friend!</p>
        <p className="mt-1 px-4 text-center text-sm text-white">
          Share this link with your friend and invite them to create a Free Visume!
        </p>

        <button
          onClick={handleCopy} // Change to button for better semantics
          className="text-medium mt-5 flex items-center gap-2 rounded-full bg-gradient-to-b from-white/50 to-white/10 py-[8px] px-9 text-center text-base text-white hover:bg-gradient-to-b hover:from-white/40 hover:to-white/5 cursor-pointer"
        >
          {copied ? (
            <>
              <HiClipboard className="text-white" />
              Link copied!
            </>
          ) : (
            <>
              <HiShare />
              Copy Link
            </>
          )}
        </button>

        </div>
    </div>}
      </div>
      

  );
};

export default SidebarCard;
