/* eslint-disable */

import { <PERSON><PERSON><PERSON>, Hi<PERSON>enu, Hi<PERSON> } from "react-icons/hi";
import Links from "./components/Links";
import SidebarCard from "./components/SidebarCard";

// import SidebarCard from "components/sidebar/componentsrtl/SidebarCard";
import routes from "routes";
import { ArrowLeftToLine, Code, PanelLeftClose } from "lucide-react";
import LogoImage from 'assets/img/Visume-logo-icon.png';

const Sidebar = ({ open, onClose }) => {
  return (
    <div
      className={`m-3 w-[250px] rounded-md sm:none duration-175 linear fixed !z-50 flex min-h-full flex-col bg-white shadow-2xl shadow-white/5 transition-all dark:!bg-navy-800 dark:text-white md:!z-50 lg:!z-50 xl:!z-0 ${
        open ? "translate-x-0" : "-translate-x-96"
      }`}
    >
      <span
        className="absolute top-6 right-6 block cursor-pointer xl:hidden"
        onClick={onClose}
      >
        <ArrowLeftToLine className="w-5 text-gray-800" />
      </span>

      <div className="py-5 pl-5 flex items-center"> {/* Change justify-start to justify-center */}
        <div className=" font-poppins text-[24px] font-bold text-brand-500 dark:text-white text-left flex items-center">
        <img src={LogoImage} alt="Visume logo" className="h-7 w-7 mb-1" />
            <span className="ml-2 text-2xl font-bold">Visume.ai</span>
        </div>
      </div>
      <div className=" mb-3 h-px bg-gray-300 dark:bg-white/30" />
      {/* Nav item */}

      <ul className="mb-auto pt-1">
        <Links routes={routes} onClose={onClose}/>
      </ul>

      {/* Free Horizon Card */}
      <div className="flex justify-center p-5">
        <SidebarCard />
      </div>

      {/* Nav item end */}
    </div>
  );
};

export default Sidebar;
