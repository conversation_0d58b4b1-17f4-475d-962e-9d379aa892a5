import React from 'react'
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";

function ProfileSkelLoader({keyValue}) {
  return (
<div key={keyValue} className="max-w-lg  rounded-lg bg-white p-4 shadow-lg">
          <div className="flex items-start space-x-4">
            {/* Profile Image Skeleton */}
            <div className="h-16 w-16">
              <Skeleton circle={true} height={64} width={64} />
            </div>

            {/* Profile Info Skeleton */}
            <div className="flex-grow">
              <div className="mb-1 flex items-center gap-4">
                {/* Name Skeleton */}
                <Skeleton width={100} height={20} />
                {/* Role Skeleton */}
                <Skeleton width={80} height={20} />
              </div>

              {/* Salary and Location Skeleton */}
              <p className="flex items-center text-gray-500">
                <Skeleton width={120} />
              </p>

              {/* Skills Skeleton */}
              <div className="mt-2 flex space-x-2 text-[13px]">
                <Skeleton width={60} height={30} />
                <Skeleton width={60} height={30} />
                <Skeleton width={60} height={30} />
              </div>
            </div>
          </div>

          {/* Wishlist and Invite Buttons Skeleton */}
          <div className="mt-4 flex justify-center gap-4 border-t border-gray-200 pt-4">
            <Skeleton width={100} height={40} />
            <Skeleton width={100} height={40} />
          </div>
        </div>
  )
}

export default ProfileSkelLoader
