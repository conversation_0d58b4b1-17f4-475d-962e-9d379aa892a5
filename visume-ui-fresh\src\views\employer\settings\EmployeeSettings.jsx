import React, { useState, useEffect } from "react";
import ManageCredits from "./ManageCredits";
import Cookies from "js-cookie";
import { data } from "autoprefixer";
import { Link, Navigate } from "react-router-dom";
import { HiArrowSmallRight } from "react-icons/hi2";
import {Upload,FileText,} from "lucide-react"
//import ManageCredits from "./ManageCredits";

function CompanyProfile() {
  // State variables
  // const [companyName, setCompanyName] = useState("Sisyphus Ventures");
  // const [companyURL, setCompanyURL] = useState("sisyphus");
  // const [twitterHandle, setTwitterHandle] = useState("sisyphusvc");
  // const [facebookHandle, setFacebookHandle] = useState("sisyphusvc");
  // const [linkedinHandle, setLinkedinHandle] = useState("sisyphusvc");
  const candId = Cookies.get("candId");
  const [logo, setLogo] = useState(null); // for logo upload
  const [name,setName] = useState("")
  const [email,setEmail] = useState("")
  const [mobile,setMobile] = useState("")
  const [gender,setGender] = useState("")
  const [language,setLanguage] = useState("")
  const [location,setLocation] = useState("")
  const [edit,setEdit] = useState(true)
  const [uploadedFile, setUploadedFile] = useState(null);
  const [errors, setErrors] = useState({});
  
  

  
   
  // Fetch initial profile data (assuming from an API or similar)
  useEffect(() => {
    const fetchProfileData = async () => {
      // Replace with actual fetch call if needed
      try {
        const response = await fetch(`${import.meta.env.VITE_APP_HOST}/api/v1/candidate/${candId}`);
        const data = await response.json();
        console.log(data)
        setName(data.candidateProfile[0].cand_name)
        setEmail(data.candidateProfile[0].cand_email)
        setMobile(data.candidateProfile[0].cand_mobile)
        setGender(data.candidateProfile[0].gender)
        setLanguage(data.candidateProfile[0].languages_known.replace(/^\["|"\]$/g, ""))
        setLocation(data.candidateProfile[0].preferred_location.replace(/^\["|"\]$/g, ""))
        
        // const data = { companyName: "Sisyphus Ventures", companyURL: "sisyphus" };   //sample data
        // setCompanyName(data.companyName);
        // setCompanyURL(data.companyURL);
      } catch (error) {
        console.error("Failed to fetch profile data:", error);
      }
    };

    fetchProfileData();
  }, []);

  // Handlers for form inputs
  // const handleCompanyNameChange = (e) => setCompanyName(e.target.value);
  // const handleCompanyURLChange = (e) => setCompanyURL(e.target.value);
  // const handleTwitterChange = (e) => setTwitterHandle(e.target.value);
  // const handleFacebookChange = (e) => setFacebookHandle(e.target.value);
  // const handleLinkedinChange = (e) => setLinkedinHandle(e.target.value);

  // Handle logo upload
  const handleLogoUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      setLogo(URL.createObjectURL(file));
    }
  };

  const handleFileUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      const isPdf = file.type === "application/pdf";
      if (isPdf) {
      // setFormData({ ...formData, resume: file });
      setUploadedFile(file);
      // setErrors({ ...errors, resume: "" });
      } else {
        // setErrors({ ...errors, resume: "Please upload a valid PDF file." });
        setUploadedFile(null); // Clear the uploaded file if it's invalid
    }
    }
  };

  // Handle form submission (e.g., to save changes)

  const handleSaveChanges = async (e) => {
    e.preventDefault();
    // Here, you'd typically send the data to the backend
    console.log("Saved profile data:", {
      companyName,
      companyURL,
      twitterHandle,
      facebookHandle,
      linkedinHandle,
    });
  };

  return (
    <div className="col-span-4">
      {/* JSX content from previous response */}
      
      <div>

        <div className="min-h-screen bg-gray-100">
          
          <header className="w-full h-28 bg-cover bg-center"
            style={{
              backgroundImage: `url("https://images.unsplash.com/photo-1557683316-973673baf926?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2029&q=80")`,
            }} >

            <div className="max-w-7xl mx-auto h-full">
              <div className="h-full w-full bg-cover bg-center opacity-90"></div>
            </div>

          </header>

          <div className="relative max-w-6xl mx-auto bg-white shadow-lg  mt-[-20px] z-10 p-8">

            
            <div className="relative mb-8">

              <div className="absolute -top-20 left-8 ">

                <img src={logo || "https://wallpapers.com/images/featured/cute-profile-picture-s52z1uggme5sj92d.jpg"}
                  alt="Profile" className="w-32 h-32 cursor-pointer rounded-full border-4 border-white object-cover"
                  onClick={()=>document.getElementById("logo-upload").click()}/>

                    <input type="file" id="logo-upload"  onChange={handleLogoUpload}  className="hidden" />

              </div>

              {/* <div className="ml-44 mt-4">
                <h1 className="text-3xl font-bold">{companyName}</h1>
                <a
                  href={`https://untitledui.com/${companyURL}`}
                  className="text-blue-500 hover:underline"
                >
                  untitledui.com/{companyURL}
                </a>
              </div> */}

             {/* {editLogo?
                <button className="absolute top-0 right-0 bg-blue-500 text-white px-4 py-2 rounded
                        hover:bg-blue-600 transition duration-300" onClick={()=>setEditLogo(false)}>
                Edit Logo
              </button>
              :
               <input type="file"  onChange={handleLogoUpload} 
               className="w-1/2 absolute top-0 right-0 border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-blue-500 transition-colors duration-200 cursor-pointer"
             />
               } */}


            </div>

           

            {/* <p className="text-gray-600 mb-5">Update your photo and details here.</p> */}

            
           {edit?

            <div className="border border-gray-200 rounded-xl p-8 mt-24 w-[75%] mx-auto">

            <div className="flex justify-between items-center mb-8">
                <h2 className="text-3xl font-semibold ">My Profile</h2>
               {edit?
               <button className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition duration-300"
                       onClick={()=>setEdit(false)} >
                      Edit Profiles
                </button>
               :
               <button className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition duration-300"
                       onClick={()=>setEdit(true)}>Save changes</button>} 
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 justify-items-center ">

            <div className="p-2 flex flex-col gap-2 w-[80%] mb-3">
            <h3  className="text-gray-500 text-lg">Name  </h3>
                <span className="">{name}</span>
           </div>

           
           <div className="p-2 flex flex-col gap-2 w-[80%] mb-3">
            <h3  className="text-gray-500 text-lg">Email </h3>
                <span className="">{email}</span>
           </div>
            
            
           <div className=" p-2 flex flex-col gap-2 w-[80%] mb-3">
            <h3  className="text-gray-500 text-lg">Mobile </h3>
                <span className="">{mobile}</span>
           </div>

            
           <div className=" p-2 flex flex-col gap-2 w-[80%] mb-3">
            <h3  className="text-gray-500 text-lg">Gender</h3>
                <span>{gender}</span>
           </div>

            
           <div className=" p-2 flex flex-col gap-2 w-[80%] mb-3">
            <h3  className="text-gray-500 text-lg">Language</h3>
                <span>{language}</span>
           </div>

           
           <div className=" p-2 flex flex-col gap-2 w-[80%] mb-3">
            <h3  className="text-gray-500 text-lg">Location</h3>
                <span>{location}</span>
           </div>

            </div>

            <div className="ml-8 mt-7">

              {edit?
                 <a href="/resume" target="_blank" className="p-2 bg-gray-700 text-white rounded-lg">View Resume</a>
                 :
                ""
              }
              
            </div>


          </div>
             
             :

            <form className=" border border-gray-200 rounded-xl p-8 mt-24 w-[75%] mx-auto">

              <div className="flex justify-between items-center mb-8">
                <h2 className="text-3xl font-semibold ">My Profile</h2>
               {edit?
               <button className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition duration-300"
                       onClick={()=>setEdit(false)} >
                      Edit
                </button>
               :
               <button className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition duration-300"
                       onClick={()=>setEdit(true)}>Save changes</button>} 
             </div>

           <div className="grid grid-cols-1 lg:grid-cols-2 justify-items-center">


          <div className="p-2 flex flex-col gap-2 w-[80%] mb-3">
             <label htmlFor="name" className="text-gray-500 text-lg">Name  </label>
             <input type="text" name="name" value={name} onChange={(e) => setName(e.target.value)}
                    className="border border-gray-500 p-1 rounded-lg outline-none" />
          </div>

         <div className=" p-2 flex flex-col gap-2 w-[80%] mb-3">
            <label htmlFor="email" className="text-gray-500 text-lg">Email</label>
            <input type="email" name="email" value={email} onChange={(e) => setEmail(e.target.value)}
                   className="border border-gray-500 p-1 rounded-lg outline-none" />
         </div>

         <div className=" p-2 flex flex-col gap-2 w-[80%] mb-3">
            <label htmlFor="number" className="text-gray-500" text-lg>Mobile</label>
            <input type="number" name="mobile" value={mobile} onChange={(e) => setMobile(e.target.value)}
                    className="border border-gray-500 p-1 rounded-lg outline-none"/>
        </div>

        <div className=" p-2 flex flex-col gap-2 w-[80%] mb-3">
           <label htmlFor="gender" className="text-gray-500 text-lg">Gender </label>
           <input type="text" name="gender" value={gender} onChange={(e) => setGender(e.target.value)}
                  className="border border-gray-500 p-1 rounded-lg outline-none"  />
        </div>

        <div className=" p-2 flex flex-col gap-2 w-[80%] mb-3">
            <label htmlFor="language" className="text-gray-500 text-lg">Language know </label>
            <input type="text" name="language" value={language} onChange={(e) => setLanguage(e.target.value)}
                   className="border border-gray-500 p-1 rounded-lg outline-none" />
       </div>

        <div className=" p-2 flex flex-col gap-2 w-[80%] mb-3">
           <label htmlFor="location" className="text-gray-500 text-lg">Prefered Location </label>
           <input type="text" name="location" value={location} onChange={(e) => setLocation(e.target.value)}
                  className="border border-gray-500 p-1 rounded-lg outline-none" />
       </div>

           </div>

           <div className="ml-8 mt-7">

      {edit?
            <a href="/resume" target="_blank" className="p-2 bg-gray-700 text-white rounded-lg"
               onClick={()=>setEdit(false)}>View Resume</a>
           :
           <div className="">

                <div className="hover:text-brand-200">

                <label className="mb-1 block text-sm font-medium text-gray-700">
                  Update your resume<span className="text-red-500">*</span>
                </label>

                <input type="file" accept=".pdf, .doc, .docx" className="hidden"
                  onChange={handleFileUpload} id="resume-upload-input"/>
                <div
                  className="cursor-pointer rounded-md border-2 border-dashed p-4 text-center hover:border-brand-200"
                  onClick={() =>
                    document.getElementById("resume-upload-input").click()
                  }
                >
                  {uploadedFile ? (
                    <div className="flex items-center justify-center">
                      <FileText className="h-12 w-12 text-brand-200" />
                      <span className="ml-2 text-sm text-gray-600">
                        {uploadedFile.name}
                      </span>
                    </div>
                  ) : (
                    <>
                      <Upload className="mx-auto h-12 w-12 text-gray-400" />
                      <p className="mt-1 text-sm text-gray-600">
                        Upload your resume in PDF or DOC format
                      </p>
                      <p className="text-xs text-gray-500">
                        Supported formats: PDF, DOC, DOCX (Max 5 MB)
                      </p>
                    </>
                  )}
                </div>
                {errors.resume && (
                  <p className="mt-1 text-sm text-red-500">{errors.resume}</p>
                )}
              </div>
               
             

         </div>
        }

</div>

            </form>}
             

            {/* <div className=" flex gap-11 items-center mt-7">

                <a href="/resume" target="_blank" className="p-2 bg-gray-700 text-white rounded-lg">View Resume</a>
                <a className="p-2 bg-gray-700 text-white rounded-lg">Update your resume</a>
             
            </div> */}

           

            {/* <div className="-mx-4 border-b border-gray-300 my-4 mt-10"></div> */}

            {/* <div className="border-b-2 border-gray-300 pb-3 flex items-center space-x-12">
              <div className="flex-shrink-0 py-12">
                <h2 className="text-lg font-semibold text-gray-900 mb-1">Public profile</h2>
                <p className="text-sm text-gray-500">This will be displayed on your profile.</p>
              </div>
              <div className="flex-grow space-y-3">
                <div>
                  <input
                    type="text"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Company Name"
                    value={companyName}
                    onChange={handleCompanyNameChange}
                  />
                </div>
                <div className="flex">
                  <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
                    untitledui.com/
                  </span>
                  <input
                    type="text"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-r-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Company URL"
                    value={companyURL}
                    onChange={handleCompanyURLChange}
                  />
                </div>
              </div>
            </div> */}

            {/* <div className="flex items-start space-x-12 py-10">

              <div className="flex-shrink-0">
                <h2 className="text-lg font-semibold text-gray-900 mb-1">Choose your logo</h2>
                 <p className="text-sm text-gray-500 mb-6">
                  Update your company logo and choose where to display it.
                </p> 
              </div>

              <div className="flex-grow">

                <input type="file"  onChange={handleLogoUpload} 
                  className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-blue-500 transition-colors duration-200 cursor-pointer"
                />

              </div>

            </div>
             */}

            {/* <div className="max-w-2xl mx-auto">
              <h2 className="text-lg font-semibold mb-2">Social Profiles</h2>
              <form onSubmit={handleSaveChanges} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Twitter</label>
                  <input
                    type="text"
                    className="w-full border border-gray-300 rounded-md p-2"
                    placeholder="Twitter Handle"
                    value={twitterHandle}
                    onChange={handleTwitterChange}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Facebook</label>
                  <input
                    type="text"
                    className="w-full border border-gray-300 rounded-md p-2"
                    placeholder="Facebook Handle"
                    value={facebookHandle}
                    onChange={handleFacebookChange}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">LinkedIn</label>
                  <input
                    type="text"
                    className="w-full border border-gray-300 rounded-md p-2"
                    placeholder="LinkedIn Handle"
                    value={linkedinHandle}
                    onChange={handleLinkedinChange}
                  />
                </div>
                <div className="text-right">
                  <button
                    type="submit"
                    className="bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition duration-300"
                  >
                    Save Changes
                  </button>
                </div>
              </form>
            </div> */}

          </div>

        </div>

      </div>

    </div>
  );
}

export default CompanyProfile;
