import ReactDOM from "react-dom/client";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-router-dom";
import "./index.css";
import { Clerk<PERSON>rovider } from "@clerk/clerk-react";
import App from "./App";
// Import your publishable key
const PUBLISHABLE_KEY = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY;


if (!PUBLISHABLE_KEY) {
  throw new Error("Missing Publishable Key");
}
const root = ReactDOM.createRoot(document.getElementById("root"));

root.render(
  <BrowserRouter>
    <ClerkProvider
      publishableKey={PUBLISHABLE_KEY}
      navigate={(to) => window.location.href = to}
      afterSignInUrl="/candidate/onboarding"
      afterSignUpUrl="/candidate/onboarding"
      afterSignOutUrl="/"
      frontendApi="special-gar-70.clerk.accounts.dev">
      <App />
    </ClerkProvider>
  </BrowserRouter>
);
