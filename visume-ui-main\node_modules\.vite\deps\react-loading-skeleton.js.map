{"version": 3, "sources": ["../../react-loading-skeleton/dist/index.js"], "sourcesContent": ["'use client';\nimport React from 'react';\n\n/**\n * @internal\n */\nconst SkeletonThemeContext = React.createContext({});\n\n/* eslint-disable react/no-array-index-key */\nconst defaultEnableAnimation = true;\n// For performance & cleanliness, don't add any inline styles unless we have to\nfunction styleOptionsToCssProperties({ baseColor, highlightColor, width, height, borderRadius, circle, direction, duration, enableAnimation = defaultEnableAnimation, customHighlightBackground, }) {\n    const style = {};\n    if (direction === 'rtl')\n        style['--animation-direction'] = 'reverse';\n    if (typeof duration === 'number')\n        style['--animation-duration'] = `${duration}s`;\n    if (!enableAnimation)\n        style['--pseudo-element-display'] = 'none';\n    if (typeof width === 'string' || typeof width === 'number')\n        style.width = width;\n    if (typeof height === 'string' || typeof height === 'number')\n        style.height = height;\n    if (typeof borderRadius === 'string' || typeof borderRadius === 'number')\n        style.borderRadius = borderRadius;\n    if (circle)\n        style.borderRadius = '50%';\n    if (typeof baseColor !== 'undefined')\n        style['--base-color'] = baseColor;\n    if (typeof highlightColor !== 'undefined')\n        style['--highlight-color'] = highlightColor;\n    if (typeof customHighlightBackground === 'string')\n        style['--custom-highlight-background'] = customHighlightBackground;\n    return style;\n}\nfunction Skeleton({ count = 1, wrapper: Wrapper, className: customClassName, containerClassName, containerTestId, circle = false, style: styleProp, ...originalPropsStyleOptions }) {\n    var _a, _b, _c;\n    const contextStyleOptions = React.useContext(SkeletonThemeContext);\n    const propsStyleOptions = { ...originalPropsStyleOptions };\n    // DO NOT overwrite style options from the context if `propsStyleOptions`\n    // has properties explicity set to undefined\n    for (const [key, value] of Object.entries(originalPropsStyleOptions)) {\n        if (typeof value === 'undefined') {\n            delete propsStyleOptions[key];\n        }\n    }\n    // Props take priority over context\n    const styleOptions = {\n        ...contextStyleOptions,\n        ...propsStyleOptions,\n        circle,\n    };\n    // `styleProp` has the least priority out of everything\n    const style = {\n        ...styleProp,\n        ...styleOptionsToCssProperties(styleOptions),\n    };\n    let className = 'react-loading-skeleton';\n    if (customClassName)\n        className += ` ${customClassName}`;\n    const inline = (_a = styleOptions.inline) !== null && _a !== void 0 ? _a : false;\n    const elements = [];\n    const countCeil = Math.ceil(count);\n    for (let i = 0; i < countCeil; i++) {\n        let thisStyle = style;\n        if (countCeil > count && i === countCeil - 1) {\n            // count is not an integer and we've reached the last iteration of\n            // the loop, so add a \"fractional\" skeleton.\n            //\n            // For example, if count is 3.5, we've already added 3 full\n            // skeletons, so now we add one more skeleton that is 0.5 times the\n            // original width.\n            const width = (_b = thisStyle.width) !== null && _b !== void 0 ? _b : '100%'; // 100% is the default since that's what's in the CSS\n            const fractionalPart = count % 1;\n            const fractionalWidth = typeof width === 'number'\n                ? width * fractionalPart\n                : `calc(${width} * ${fractionalPart})`;\n            thisStyle = { ...thisStyle, width: fractionalWidth };\n        }\n        const skeletonSpan = (React.createElement(\"span\", { className: className, style: thisStyle, key: i }, \"\\u200C\"));\n        if (inline) {\n            elements.push(skeletonSpan);\n        }\n        else {\n            // Without the <br />, the skeleton lines will all run together if\n            // `width` is specified\n            elements.push(React.createElement(React.Fragment, { key: i },\n                skeletonSpan,\n                React.createElement(\"br\", null)));\n        }\n    }\n    return (React.createElement(\"span\", { className: containerClassName, \"data-testid\": containerTestId, \"aria-live\": \"polite\", \"aria-busy\": (_c = styleOptions.enableAnimation) !== null && _c !== void 0 ? _c : defaultEnableAnimation }, Wrapper\n        ? elements.map((el, i) => React.createElement(Wrapper, { key: i }, el))\n        : elements));\n}\n\nfunction SkeletonTheme({ children, ...styleOptions }) {\n    return (React.createElement(SkeletonThemeContext.Provider, { value: styleOptions }, children));\n}\n\nexport { SkeletonTheme, Skeleton as default };\n"], "mappings": ";;;;;;;;;AACA,mBAAkB;AAKlB,IAAM,uBAAuB,aAAAA,QAAM,cAAc,CAAC,CAAC;AAGnD,IAAM,yBAAyB;AAE/B,SAAS,4BAA4B,EAAE,WAAW,gBAAgB,OAAO,QAAQ,cAAc,QAAQ,WAAW,UAAU,kBAAkB,wBAAwB,0BAA2B,GAAG;AAChM,QAAM,QAAQ,CAAC;AACf,MAAI,cAAc;AACd,UAAM,uBAAuB,IAAI;AACrC,MAAI,OAAO,aAAa;AACpB,UAAM,sBAAsB,IAAI,GAAG,QAAQ;AAC/C,MAAI,CAAC;AACD,UAAM,0BAA0B,IAAI;AACxC,MAAI,OAAO,UAAU,YAAY,OAAO,UAAU;AAC9C,UAAM,QAAQ;AAClB,MAAI,OAAO,WAAW,YAAY,OAAO,WAAW;AAChD,UAAM,SAAS;AACnB,MAAI,OAAO,iBAAiB,YAAY,OAAO,iBAAiB;AAC5D,UAAM,eAAe;AACzB,MAAI;AACA,UAAM,eAAe;AACzB,MAAI,OAAO,cAAc;AACrB,UAAM,cAAc,IAAI;AAC5B,MAAI,OAAO,mBAAmB;AAC1B,UAAM,mBAAmB,IAAI;AACjC,MAAI,OAAO,8BAA8B;AACrC,UAAM,+BAA+B,IAAI;AAC7C,SAAO;AACX;AACA,SAAS,SAAS,EAAE,QAAQ,GAAG,SAAS,SAAS,WAAW,iBAAiB,oBAAoB,iBAAiB,SAAS,OAAO,OAAO,WAAW,GAAG,0BAA0B,GAAG;AAChL,MAAI,IAAI,IAAI;AACZ,QAAM,sBAAsB,aAAAA,QAAM,WAAW,oBAAoB;AACjE,QAAM,oBAAoB,EAAE,GAAG,0BAA0B;AAGzD,aAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,yBAAyB,GAAG;AAClE,QAAI,OAAO,UAAU,aAAa;AAC9B,aAAO,kBAAkB,GAAG;AAAA,IAChC;AAAA,EACJ;AAEA,QAAM,eAAe;AAAA,IACjB,GAAG;AAAA,IACH,GAAG;AAAA,IACH;AAAA,EACJ;AAEA,QAAM,QAAQ;AAAA,IACV,GAAG;AAAA,IACH,GAAG,4BAA4B,YAAY;AAAA,EAC/C;AACA,MAAI,YAAY;AAChB,MAAI;AACA,iBAAa,IAAI,eAAe;AACpC,QAAM,UAAU,KAAK,aAAa,YAAY,QAAQ,OAAO,SAAS,KAAK;AAC3E,QAAM,WAAW,CAAC;AAClB,QAAM,YAAY,KAAK,KAAK,KAAK;AACjC,WAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAChC,QAAI,YAAY;AAChB,QAAI,YAAY,SAAS,MAAM,YAAY,GAAG;AAO1C,YAAM,SAAS,KAAK,UAAU,WAAW,QAAQ,OAAO,SAAS,KAAK;AACtE,YAAM,iBAAiB,QAAQ;AAC/B,YAAM,kBAAkB,OAAO,UAAU,WACnC,QAAQ,iBACR,QAAQ,KAAK,MAAM,cAAc;AACvC,kBAAY,EAAE,GAAG,WAAW,OAAO,gBAAgB;AAAA,IACvD;AACA,UAAM,eAAgB,aAAAA,QAAM,cAAc,QAAQ,EAAE,WAAsB,OAAO,WAAW,KAAK,EAAE,GAAG,GAAQ;AAC9G,QAAI,QAAQ;AACR,eAAS,KAAK,YAAY;AAAA,IAC9B,OACK;AAGD,eAAS,KAAK,aAAAA,QAAM;AAAA,QAAc,aAAAA,QAAM;AAAA,QAAU,EAAE,KAAK,EAAE;AAAA,QACvD;AAAA,QACA,aAAAA,QAAM,cAAc,MAAM,IAAI;AAAA,MAAC,CAAC;AAAA,IACxC;AAAA,EACJ;AACA,SAAQ,aAAAA,QAAM,cAAc,QAAQ,EAAE,WAAW,oBAAoB,eAAe,iBAAiB,aAAa,UAAU,cAAc,KAAK,aAAa,qBAAqB,QAAQ,OAAO,SAAS,KAAK,uBAAuB,GAAG,UAClO,SAAS,IAAI,CAAC,IAAI,MAAM,aAAAA,QAAM,cAAc,SAAS,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,IACpE,QAAQ;AAClB;AAEA,SAAS,cAAc,EAAE,UAAU,GAAG,aAAa,GAAG;AAClD,SAAQ,aAAAA,QAAM,cAAc,qBAAqB,UAAU,EAAE,OAAO,aAAa,GAAG,QAAQ;AAChG;", "names": ["React"]}