name: Deploy to VPS (Backend)

on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v2
        with:
          token: ${{ secrets.PAT }}

      - name: Update code on VPS
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.VPS_HOST }}
          username: ${{ secrets.VPS_USERNAME }}
          key: ${{ secrets.VPS_SSH_KEY }}
          command_timeout: 10m
          script: |
            cd /home/<USER>/htdocs/api.zoomjobs.in
            git config --global url.https://${{ secrets.PAT }}:<EMAIL>/.insteadOf https://github.com/
            git pull origin main
            npm install
            pm2 restart visume-be
