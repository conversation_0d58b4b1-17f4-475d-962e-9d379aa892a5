import { useEffect, useState } from "react";
import { Upload } from "lucide-react";
import { useNavigate } from "react-router-dom";
import Cookies from "js-cookie";

const GeneralProfile = () => {
  const navigate = useNavigate();
  const emp_id = Cookies.get("employerId");

  const [loading, setLoading] = useState(true);
  const [editActive, setEditActive] = useState(false);
  const [empName, setEmpName] = useState("");
  const [companyUrl, setCompanyUrl] = useState("");
  const [twitterHandle, setTwitterHandle] = useState("");
  const [facebookHandle, setFacebookHandle] = useState("");
  const [linkedinHandle, setLinkedinHandle] = useState("");
  const [includeInReports, setIncludeInReports] = useState(true);
  const [includeInEmails, setIncludeInEmails] = useState(true);

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log("Submitted:", {
      twitterHandle,
      facebookHandle,
      linkedinHandle,
    });
    setEditActive(false);
  };

  const getProfileData = async () => {
    setLoading(true);
    try {
      const profileReq = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/getEmployerDetails`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: emp_id,
          },
        }
      );
      const profileJson = await profileReq.json();
      
      const profileData = profileJson.data || {};
      console.log(profileData);
      if (profileData) {
        setEmpName(profileData.emp_name);
        setCompanyUrl(profileData.company_website);
        setTwitterHandle(profileData?.company_twitterHandle || "");
        setFacebookHandle(profileData?.company_facebookHandle || "");
        setLinkedinHandle(profileData?.company_linkedinHandle || "");
      }
    } catch (err) {
      console.error("Error fetching profile data:", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (emp_id) {
      getProfileData();
    } else {
      navigate("/candidate/signIn");
    }
  }, []);

  return (
    <div className="min-h-screen -mt-3 bg-gray-100">
      <header
        className="h-20 md:h-24k w-full bg-cover bg-center"
        style={{
          backgroundImage:
            "url('https://images.unsplash.com/photo-1557683316-973673baf926?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2029&q=80')",
        }}
      >
        <div className="mx-auto h-full max-w-7xl">
          <div className="h-full w-full bg-cover bg-center opacity-90"></div>
        </div>
      </header>

      <div className="relative z-10 max-w-6xl rounded-lg bg-white p-8 shadow-lg">
        {loading ? (
          <div className="flex items-center justify-center py-10">
            <div className="h-8 w-8 animate-spin rounded-full border-4 border-blue-500 border-t-transparent"></div>
          </div>
        ) : (
          <div>
            <div className="relative mb-8">
              <div className="absolute -top-20 md:-top-24 -left-4 md:left-3 lg:left-6">
                <span className="text-sm md:text-lg flex h-16 md:h-28 w-16 md:w-28 items-center justify-center rounded-full border-4 border-white bg-brand-600 object-cover text-white">
                  {empName ? empName.slice(0, 10) : ""}
                  {empName && empName.length > 10 && "..."}
                </span>
              </div>

              <div className="flex justify-between items-center pt-0 md:pt-10">
                <div>
                  <h1 className="text-[15px] md:text-xl lg:text-2xl font-bold">{empName}</h1>
                  <a href={`https://untitledui.com/${companyUrl}`} className="text-blue-500 hover:underline text-[14px] md:text-xl">
                    {`untitledui.com/${companyUrl}`}
                  </a>
                </div>
                <div>
                  <button onClick={() => setEditActive(true)} className="rounded bg-blue-500 text-[10px] md:text-base px-1.5 py-0.5 md:px-4 md:py-2 text-white transition duration-300 hover:bg-blue-600">
                    Edit Profile
                  </button>
                </div>
              </div>
            </div>

            <h2 className="mb-2 text-[17px] md:text-2xl font-semibold">Company Profile</h2>
            <p className="mb-6 text-gray-600 text-sm md:text-base">
              Update your company photo and details here.
            </p>

            <div className="-mx-4 my-4 border-b border-gray-300"></div>

            <div className="flex flex-col md:flex border-b-2 border-gray-300 pb-6">
              <div className="flex-shrink-0 py-3">
                <h2 className="mb-1 text-[17px] md:text-2xl font-semibold text-gray-900">
                  Public Profile
                </h2>
                <p className="text-sm text-gray-500">
                  This will be displayed on your profile.
                </p>
              </div>

              <div className="flex-grow space-y-4">
                <div className="mr-11 md:mr-0">
                  <input
                    type="text"
                    value={empName}
                    onChange={(e) => setEmpName(e.target.value)}
                    className={`w-[215px] rounded-md border border-gray-300 px-3 py-1.5 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      !editActive ? "cursor-not-allowed" : ""
                    }`}
                    placeholder="Company Name"
                    disabled={!editActive}
                  />
                </div>

                <div className="flex">
                  <span className="inline-flex items-center rounded-l-md border border-r-0 border-gray-300 bg-gray-50 px-2 text-sm text-gray-500">
                    untitledui.com/
                  </span>
                  <input
                    type="text"
                    value={companyUrl}
                    onChange={(e) => setCompanyUrl(e.target.value)}
                    className={`w-[110px] rounded-r-md border border-gray-300 px-2 py-1.5 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      !editActive ? "cursor-not-allowed" : ""
                    }`}
                    placeholder="Company URL"
                    disabled={!editActive}
                  />
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 space-y-6 lg:grid-cols-2 lg:space-x-12 py-10">
              <div>
                <h2 className="mb-2.5 text-[17px] md:text-2xl font-semibold text-gray-900">
                  Company logo
                </h2>
                <p className="mb-6 text-sm text-gray-500">
                  Update your company logo and choose where to display it.
                </p>
                <div className="flex justify-center lg:justify-start">
                  <div
                    className={`flex h-16 w-16 items-center justify-center rounded-full bg-blue-500 ${
                      !editActive ? "cursor-not-allowed" : ""
                    }`}
                  >
                    <svg
                      className="h-10 w-10 text-white"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"></path>
                    </svg>
                  </div>
                </div>
              </div>

              <div className={`flex-grow ${!editActive ? "cursor-not-allowed" : ""}`}>
                <div
                  className={`cursor-pointer rounded-lg border-2 border-dashed border-gray-300 p-4 text-center transition-colors duration-200 hover:border-blue-500 ${
                    !editActive ? "pointer-events-none cursor-not-allowed" : ""
                  }`}
                >
                  <p className="mt-1 text-sm text-gray-500">
                    <span className="font-medium text-blue-600 hover:underline">
                      Click to upload
                    </span>{" "}
                    or drag and drop
                  </p>
                  <p className="mt-1 text-xs text-gray-500">
                    SVG, PNG, JPG or GIF (max. 800x400px)
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default GeneralProfile;
