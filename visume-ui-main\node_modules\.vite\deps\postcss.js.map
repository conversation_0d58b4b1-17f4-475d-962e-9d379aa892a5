{"version": 3, "sources": ["../../postcss/lib/postcss.mjs"], "sourcesContent": ["import postcss from './postcss.js'\n\nexport default postcss\n\nexport const stringify = postcss.stringify\nexport const fromJSON = postcss.fromJSON\nexport const plugin = postcss.plugin\nexport const parse = postcss.parse\nexport const list = postcss.list\n\nexport const document = postcss.document\nexport const comment = postcss.comment\nexport const atRule = postcss.atRule\nexport const rule = postcss.rule\nexport const decl = postcss.decl\nexport const root = postcss.root\n\nexport const CssSyntaxError = postcss.CssSyntaxError\nexport const Declaration = postcss.Declaration\nexport const Container = postcss.Container\nexport const Processor = postcss.Processor\nexport const Document = postcss.Document\nexport const Comment = postcss.Comment\nexport const Warning = postcss.Warning\nexport const AtRule = postcss.AtRule\nexport const Result = postcss.Result\nexport const Input = postcss.Input\nexport const Rule = postcss.Rule\nexport const Root = postcss.Root\nexport const Node = postcss.Node\n"], "mappings": ";;;;;;;;AAAA,qBAAoB;AAEpB,IAAO,kBAAQ,eAAAA;AAER,IAAM,YAAY,eAAAA,QAAQ;AAC1B,IAAM,WAAW,eAAAA,QAAQ;AACzB,IAAM,SAAS,eAAAA,QAAQ;AACvB,IAAM,QAAQ,eAAAA,QAAQ;AACtB,IAAM,OAAO,eAAAA,QAAQ;AAErB,IAAM,WAAW,eAAAA,QAAQ;AACzB,IAAM,UAAU,eAAAA,QAAQ;AACxB,IAAM,SAAS,eAAAA,QAAQ;AACvB,IAAM,OAAO,eAAAA,QAAQ;AACrB,IAAM,OAAO,eAAAA,QAAQ;AACrB,IAAM,OAAO,eAAAA,QAAQ;AAErB,IAAM,iBAAiB,eAAAA,QAAQ;AAC/B,IAAM,cAAc,eAAAA,QAAQ;AAC5B,IAAM,YAAY,eAAAA,QAAQ;AAC1B,IAAM,YAAY,eAAAA,QAAQ;AAC1B,IAAM,WAAW,eAAAA,QAAQ;AACzB,IAAM,UAAU,eAAAA,QAAQ;AACxB,IAAM,UAAU,eAAAA,QAAQ;AACxB,IAAM,SAAS,eAAAA,QAAQ;AACvB,IAAM,SAAS,eAAAA,QAAQ;AACvB,IAAM,QAAQ,eAAAA,QAAQ;AACtB,IAAM,OAAO,eAAAA,QAAQ;AACrB,IAAM,OAAO,eAAAA,QAAQ;AACrB,IAAM,OAAO,eAAAA,QAAQ;", "names": ["postcss"]}