import React from 'react';

export default function Button({ children, onClick, variant = 'primary', className = '' }) {
  const baseClasses = 'rounded-lg px-6 py-4 font-semibold transition-colors duration-500';
  const variantClasses = variant === 'primary' 
    ? 'bg-blue-600 text-white hover:bg-blue-700' 
    : 'border-2 border-gray-600 text-gray-600 hover:bg-gray-100';

  return (
    <button 
      className={`${baseClasses} ${variantClasses} ${className}`}
      onClick={onClick}
    >
      {children}
    </button>
  );
}