{"version": 3, "sources": ["../../@aws-sdk/client-cognito-identity/dist-es/auth/httpAuthSchemeProvider.js", "../../@aws-sdk/client-cognito-identity/dist-es/endpoint/EndpointParameters.js", "../../@aws-sdk/client-cognito-identity/package.json", "../../@aws-sdk/client-cognito-identity/dist-es/endpoint/ruleset.js", "../../@aws-sdk/client-cognito-identity/dist-es/endpoint/endpointResolver.js", "../../@aws-sdk/client-cognito-identity/dist-es/runtimeConfig.shared.js", "../../@aws-sdk/client-cognito-identity/dist-es/runtimeConfig.browser.js", "../../@aws-sdk/client-cognito-identity/dist-es/auth/httpAuthExtensionConfiguration.js", "../../@aws-sdk/client-cognito-identity/dist-es/runtimeExtensions.js", "../../@aws-sdk/client-cognito-identity/dist-es/CognitoIdentityClient.js", "../../@aws-sdk/client-cognito-identity/dist-es/models/CognitoIdentityServiceException.js", "../../@aws-sdk/client-cognito-identity/dist-es/models/models_0.js", "../../@aws-sdk/client-cognito-identity/dist-es/protocols/Aws_json1_1.js", "../../@aws-sdk/client-cognito-identity/dist-es/commands/CreateIdentityPoolCommand.js", "../../@aws-sdk/client-cognito-identity/dist-es/commands/DeleteIdentitiesCommand.js", "../../@aws-sdk/client-cognito-identity/dist-es/commands/DeleteIdentityPoolCommand.js", "../../@aws-sdk/client-cognito-identity/dist-es/commands/DescribeIdentityCommand.js", "../../@aws-sdk/client-cognito-identity/dist-es/commands/DescribeIdentityPoolCommand.js", "../../@aws-sdk/client-cognito-identity/dist-es/commands/GetCredentialsForIdentityCommand.js", "../../@aws-sdk/client-cognito-identity/dist-es/commands/GetIdCommand.js", "../../@aws-sdk/client-cognito-identity/dist-es/commands/GetIdentityPoolRolesCommand.js", "../../@aws-sdk/client-cognito-identity/dist-es/commands/GetOpenIdTokenCommand.js", "../../@aws-sdk/client-cognito-identity/dist-es/commands/GetOpenIdTokenForDeveloperIdentityCommand.js", "../../@aws-sdk/client-cognito-identity/dist-es/commands/GetPrincipalTagAttributeMapCommand.js", "../../@aws-sdk/client-cognito-identity/dist-es/commands/ListIdentitiesCommand.js", "../../@aws-sdk/client-cognito-identity/dist-es/commands/ListIdentityPoolsCommand.js", "../../@aws-sdk/client-cognito-identity/dist-es/commands/ListTagsForResourceCommand.js", "../../@aws-sdk/client-cognito-identity/dist-es/commands/LookupDeveloperIdentityCommand.js", "../../@aws-sdk/client-cognito-identity/dist-es/commands/MergeDeveloperIdentitiesCommand.js", "../../@aws-sdk/client-cognito-identity/dist-es/commands/SetIdentityPoolRolesCommand.js", "../../@aws-sdk/client-cognito-identity/dist-es/commands/SetPrincipalTagAttributeMapCommand.js", "../../@aws-sdk/client-cognito-identity/dist-es/commands/TagResourceCommand.js", "../../@aws-sdk/client-cognito-identity/dist-es/commands/UnlinkDeveloperIdentityCommand.js", "../../@aws-sdk/client-cognito-identity/dist-es/commands/UnlinkIdentityCommand.js", "../../@aws-sdk/client-cognito-identity/dist-es/commands/UntagResourceCommand.js", "../../@aws-sdk/client-cognito-identity/dist-es/commands/UpdateIdentityPoolCommand.js", "../../@aws-sdk/client-cognito-identity/dist-es/CognitoIdentity.js", "../../@aws-sdk/client-cognito-identity/dist-es/pagination/ListIdentityPoolsPaginator.js"], "sourcesContent": ["import { resolveAwsSdkSigV4Config, } from \"@aws-sdk/core\";\nimport { getSmithyContext, normalizeProvider } from \"@smithy/util-middleware\";\nexport const defaultCognitoIdentityHttpAuthSchemeParametersProvider = async (config, context, input) => {\n    return {\n        operation: getSmithyContext(context).operation,\n        region: (await normalizeProvider(config.region)()) ||\n            (() => {\n                throw new Error(\"expected `region` to be configured for `aws.auth#sigv4`\");\n            })(),\n    };\n};\nfunction createAwsAuthSigv4HttpAuthOption(authParameters) {\n    return {\n        schemeId: \"aws.auth#sigv4\",\n        signingProperties: {\n            name: \"cognito-identity\",\n            region: authParameters.region,\n        },\n        propertiesExtractor: (config, context) => ({\n            signingProperties: {\n                config,\n                context,\n            },\n        }),\n    };\n}\nfunction createSmithyApiNoAuthHttpAuthOption(authParameters) {\n    return {\n        schemeId: \"smithy.api#noAuth\",\n    };\n}\nexport const defaultCognitoIdentityHttpAuthSchemeProvider = (authParameters) => {\n    const options = [];\n    switch (authParameters.operation) {\n        case \"GetCredentialsForIdentity\": {\n            options.push(createSmithyApiNoAuthHttpAuthOption(authParameters));\n            break;\n        }\n        case \"GetId\": {\n            options.push(createSmithyApiNoAuthHttpAuthOption(authParameters));\n            break;\n        }\n        case \"GetOpenIdToken\": {\n            options.push(createSmithyApiNoAuthHttpAuthOption(authParameters));\n            break;\n        }\n        case \"UnlinkIdentity\": {\n            options.push(createSmithyApiNoAuthHttpAuthOption(authParameters));\n            break;\n        }\n        default: {\n            options.push(createAwsAuthSigv4HttpAuthOption(authParameters));\n        }\n    }\n    return options;\n};\nexport const resolveHttpAuthSchemeConfig = (config) => {\n    const config_0 = resolveAwsSdkSigV4Config(config);\n    return {\n        ...config_0,\n    };\n};\n", "export const resolveClientEndpointParameters = (options) => {\n    return {\n        ...options,\n        useDualstackEndpoint: options.useDualstackEndpoint ?? false,\n        useFipsEndpoint: options.useFipsEndpoint ?? false,\n        defaultSigningName: \"cognito-identity\",\n    };\n};\nexport const commonParams = {\n    UseFIPS: { type: \"builtInParams\", name: \"useFipsEndpoint\" },\n    Endpoint: { type: \"builtInParams\", name: \"endpoint\" },\n    Region: { type: \"builtInParams\", name: \"region\" },\n    UseDualStack: { type: \"builtInParams\", name: \"useDualstackEndpoint\" },\n};\n", "{\n  \"name\": \"@aws-sdk/client-cognito-identity\",\n  \"description\": \"AWS SDK for JavaScript Cognito Identity Client for Node.js, Browser and React Native\",\n  \"version\": \"3.699.0\",\n  \"scripts\": {\n    \"build\": \"concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types'\",\n    \"build:cjs\": \"node ../../scripts/compilation/inline client-cognito-identity\",\n    \"build:es\": \"tsc -p tsconfig.es.json\",\n    \"build:include:deps\": \"lerna run --scope $npm_package_name --include-dependencies build\",\n    \"build:types\": \"tsc -p tsconfig.types.json\",\n    \"build:types:downlevel\": \"downlevel-dts dist-types dist-types/ts3.4\",\n    \"clean\": \"rimraf ./dist-* && rimraf *.tsbuildinfo\",\n    \"extract:docs\": \"api-extractor run --local\",\n    \"generate:client\": \"node ../../scripts/generate-clients/single-service --solo cognito-identity\",\n    \"test:e2e\": \"yarn g:vitest run -c vitest.config.e2e.ts --mode development\",\n    \"test:e2e:watch\": \"yarn g:vitest watch -c vitest.config.e2e.ts\"\n  },\n  \"main\": \"./dist-cjs/index.js\",\n  \"types\": \"./dist-types/index.d.ts\",\n  \"module\": \"./dist-es/index.js\",\n  \"sideEffects\": false,\n  \"dependencies\": {\n    \"@aws-crypto/sha256-browser\": \"5.2.0\",\n    \"@aws-crypto/sha256-js\": \"5.2.0\",\n    \"@aws-sdk/client-sso-oidc\": \"3.699.0\",\n    \"@aws-sdk/client-sts\": \"3.699.0\",\n    \"@aws-sdk/core\": \"3.696.0\",\n    \"@aws-sdk/credential-provider-node\": \"3.699.0\",\n    \"@aws-sdk/middleware-host-header\": \"3.696.0\",\n    \"@aws-sdk/middleware-logger\": \"3.696.0\",\n    \"@aws-sdk/middleware-recursion-detection\": \"3.696.0\",\n    \"@aws-sdk/middleware-user-agent\": \"3.696.0\",\n    \"@aws-sdk/region-config-resolver\": \"3.696.0\",\n    \"@aws-sdk/types\": \"3.696.0\",\n    \"@aws-sdk/util-endpoints\": \"3.696.0\",\n    \"@aws-sdk/util-user-agent-browser\": \"3.696.0\",\n    \"@aws-sdk/util-user-agent-node\": \"3.696.0\",\n    \"@smithy/config-resolver\": \"^3.0.12\",\n    \"@smithy/core\": \"^2.5.3\",\n    \"@smithy/fetch-http-handler\": \"^4.1.1\",\n    \"@smithy/hash-node\": \"^3.0.10\",\n    \"@smithy/invalid-dependency\": \"^3.0.10\",\n    \"@smithy/middleware-content-length\": \"^3.0.12\",\n    \"@smithy/middleware-endpoint\": \"^3.2.3\",\n    \"@smithy/middleware-retry\": \"^3.0.27\",\n    \"@smithy/middleware-serde\": \"^3.0.10\",\n    \"@smithy/middleware-stack\": \"^3.0.10\",\n    \"@smithy/node-config-provider\": \"^3.1.11\",\n    \"@smithy/node-http-handler\": \"^3.3.1\",\n    \"@smithy/protocol-http\": \"^4.1.7\",\n    \"@smithy/smithy-client\": \"^3.4.4\",\n    \"@smithy/types\": \"^3.7.1\",\n    \"@smithy/url-parser\": \"^3.0.10\",\n    \"@smithy/util-base64\": \"^3.0.0\",\n    \"@smithy/util-body-length-browser\": \"^3.0.0\",\n    \"@smithy/util-body-length-node\": \"^3.0.0\",\n    \"@smithy/util-defaults-mode-browser\": \"^3.0.27\",\n    \"@smithy/util-defaults-mode-node\": \"^3.0.27\",\n    \"@smithy/util-endpoints\": \"^2.1.6\",\n    \"@smithy/util-middleware\": \"^3.0.10\",\n    \"@smithy/util-retry\": \"^3.0.10\",\n    \"@smithy/util-utf8\": \"^3.0.0\",\n    \"tslib\": \"^2.6.2\"\n  },\n  \"devDependencies\": {\n    \"@aws-sdk/client-iam\": \"3.699.0\",\n    \"@tsconfig/node16\": \"16.1.3\",\n    \"@types/chai\": \"^4.2.11\",\n    \"@types/node\": \"^16.18.96\",\n    \"concurrently\": \"7.0.0\",\n    \"downlevel-dts\": \"0.10.1\",\n    \"rimraf\": \"3.0.2\",\n    \"typescript\": \"~4.9.5\"\n  },\n  \"engines\": {\n    \"node\": \">=16.0.0\"\n  },\n  \"typesVersions\": {\n    \"<4.0\": {\n      \"dist-types/*\": [\n        \"dist-types/ts3.4/*\"\n      ]\n    }\n  },\n  \"files\": [\n    \"dist-*/**\"\n  ],\n  \"author\": {\n    \"name\": \"AWS SDK for JavaScript Team\",\n    \"url\": \"https://aws.amazon.com/javascript/\"\n  },\n  \"license\": \"Apache-2.0\",\n  \"browser\": {\n    \"./dist-es/runtimeConfig\": \"./dist-es/runtimeConfig.browser\"\n  },\n  \"react-native\": {\n    \"./dist-es/runtimeConfig\": \"./dist-es/runtimeConfig.native\"\n  },\n  \"homepage\": \"https://github.com/aws/aws-sdk-js-v3/tree/main/clients/client-cognito-identity\",\n  \"repository\": {\n    \"type\": \"git\",\n    \"url\": \"https://github.com/aws/aws-sdk-js-v3.git\",\n    \"directory\": \"clients/client-cognito-identity\"\n  }\n}\n", "const s = \"required\", t = \"fn\", u = \"argv\", v = \"ref\";\nconst a = true, b = \"isSet\", c = \"booleanEquals\", d = \"error\", e = \"endpoint\", f = \"tree\", g = \"PartitionResult\", h = { [s]: false, \"type\": \"String\" }, i = { [s]: true, \"default\": false, \"type\": \"Boolean\" }, j = { [v]: \"Endpoint\" }, k = { [t]: c, [u]: [{ [v]: \"UseFIPS\" }, true] }, l = { [t]: c, [u]: [{ [v]: \"UseDualStack\" }, true] }, m = {}, n = { [t]: \"getAttr\", [u]: [{ [v]: g }, \"supportsFIPS\"] }, o = { [t]: c, [u]: [true, { [t]: \"getAttr\", [u]: [{ [v]: g }, \"supportsDualStack\"] }] }, p = [k], q = [l], r = [{ [v]: \"Region\" }];\nconst _data = { version: \"1.0\", parameters: { Region: h, UseDualStack: i, UseFIPS: i, Endpoint: h }, rules: [{ conditions: [{ [t]: b, [u]: [j] }], rules: [{ conditions: p, error: \"Invalid Configuration: FIPS and custom endpoint are not supported\", type: d }, { conditions: q, error: \"Invalid Configuration: Dualstack and custom endpoint are not supported\", type: d }, { endpoint: { url: j, properties: m, headers: m }, type: e }], type: f }, { conditions: [{ [t]: b, [u]: r }], rules: [{ conditions: [{ [t]: \"aws.partition\", [u]: r, assign: g }], rules: [{ conditions: [k, l], rules: [{ conditions: [{ [t]: c, [u]: [a, n] }, o], rules: [{ endpoint: { url: \"https://cognito-identity-fips.{Region}.{PartitionResult#dualStackDnsSuffix}\", properties: m, headers: m }, type: e }], type: f }, { error: \"FIPS and DualStack are enabled, but this partition does not support one or both\", type: d }], type: f }, { conditions: p, rules: [{ conditions: [{ [t]: c, [u]: [n, a] }], rules: [{ endpoint: { url: \"https://cognito-identity-fips.{Region}.{PartitionResult#dnsSuffix}\", properties: m, headers: m }, type: e }], type: f }, { error: \"FIPS is enabled but this partition does not support FIPS\", type: d }], type: f }, { conditions: q, rules: [{ conditions: [o], rules: [{ endpoint: { url: \"https://cognito-identity.{Region}.{PartitionResult#dualStackDnsSuffix}\", properties: m, headers: m }, type: e }], type: f }, { error: \"DualStack is enabled but this partition does not support DualStack\", type: d }], type: f }, { endpoint: { url: \"https://cognito-identity.{Region}.{PartitionResult#dnsSuffix}\", properties: m, headers: m }, type: e }], type: f }], type: f }, { error: \"Invalid Configuration: Missing Region\", type: d }] };\nexport const ruleSet = _data;\n", "import { awsEndpointFunctions } from \"@aws-sdk/util-endpoints\";\nimport { customEndpointFunctions, EndpointCache, resolveEndpoint } from \"@smithy/util-endpoints\";\nimport { ruleSet } from \"./ruleset\";\nconst cache = new EndpointCache({\n    size: 50,\n    params: [\"Endpoint\", \"Region\", \"UseDualStack\", \"UseFIPS\"],\n});\nexport const defaultEndpointResolver = (endpointParams, context = {}) => {\n    return cache.get(endpointParams, () => resolveEndpoint(ruleSet, {\n        endpointParams: endpointParams,\n        logger: context.logger,\n    }));\n};\ncustomEndpointFunctions.aws = awsEndpointFunctions;\n", "import { AwsSdkSigV4Signer } from \"@aws-sdk/core\";\nimport { NoAuthSigner } from \"@smithy/core\";\nimport { NoOpLogger } from \"@smithy/smithy-client\";\nimport { parseUrl } from \"@smithy/url-parser\";\nimport { fromBase64, toBase64 } from \"@smithy/util-base64\";\nimport { fromUtf8, toUtf8 } from \"@smithy/util-utf8\";\nimport { defaultCognitoIdentityHttpAuthSchemeProvider } from \"./auth/httpAuthSchemeProvider\";\nimport { defaultEndpointResolver } from \"./endpoint/endpointResolver\";\nexport const getRuntimeConfig = (config) => {\n    return {\n        apiVersion: \"2014-06-30\",\n        base64Decoder: config?.base64Decoder ?? fromBase64,\n        base64Encoder: config?.base64Encoder ?? toBase64,\n        disableHostPrefix: config?.disableHostPrefix ?? false,\n        endpointProvider: config?.endpointProvider ?? defaultEndpointResolver,\n        extensions: config?.extensions ?? [],\n        httpAuthSchemeProvider: config?.httpAuthSchemeProvider ?? defaultCognitoIdentityHttpAuthSchemeProvider,\n        httpAuthSchemes: config?.httpAuthSchemes ?? [\n            {\n                schemeId: \"aws.auth#sigv4\",\n                identityProvider: (ipc) => ipc.getIdentityProvider(\"aws.auth#sigv4\"),\n                signer: new AwsSdkSigV4Signer(),\n            },\n            {\n                schemeId: \"smithy.api#noAuth\",\n                identityProvider: (ipc) => ipc.getIdentityProvider(\"smithy.api#noAuth\") || (async () => ({})),\n                signer: new NoAuthSigner(),\n            },\n        ],\n        logger: config?.logger ?? new NoOpLogger(),\n        serviceId: config?.serviceId ?? \"Cognito Identity\",\n        urlParser: config?.urlParser ?? parseUrl,\n        utf8Decoder: config?.utf8Decoder ?? fromUtf8,\n        utf8Encoder: config?.utf8Encoder ?? toUtf8,\n    };\n};\n", "import packageInfo from \"../package.json\";\nimport { Sha256 } from \"@aws-crypto/sha256-browser\";\nimport { createDefaultUserAgentProvider } from \"@aws-sdk/util-user-agent-browser\";\nimport { DEFAULT_USE_DUALSTACK_ENDPOINT, DEFAULT_USE_FIPS_ENDPOINT } from \"@smithy/config-resolver\";\nimport { FetchHttpHandler as RequestHandler, streamCollector } from \"@smithy/fetch-http-handler\";\nimport { invalidProvider } from \"@smithy/invalid-dependency\";\nimport { calculateBodyLength } from \"@smithy/util-body-length-browser\";\nimport { DEFAULT_MAX_ATTEMPTS, DEFAULT_RETRY_MODE } from \"@smithy/util-retry\";\nimport { getRuntimeConfig as getSharedRuntimeConfig } from \"./runtimeConfig.shared\";\nimport { loadConfigsForDefaultMode } from \"@smithy/smithy-client\";\nimport { resolveDefaultsModeConfig } from \"@smithy/util-defaults-mode-browser\";\nexport const getRuntimeConfig = (config) => {\n    const defaultsMode = resolveDefaultsModeConfig(config);\n    const defaultConfigProvider = () => defaultsMode().then(loadConfigsForDefaultMode);\n    const clientSharedValues = getSharedRuntimeConfig(config);\n    return {\n        ...clientSharedValues,\n        ...config,\n        runtime: \"browser\",\n        defaultsMode,\n        bodyLengthChecker: config?.bodyLengthChecker ?? calculateBodyLength,\n        credentialDefaultProvider: config?.credentialDefaultProvider ?? ((_) => () => Promise.reject(new Error(\"Credential is missing\"))),\n        defaultUserAgentProvider: config?.defaultUserAgentProvider ??\n            createDefaultUserAgentProvider({ serviceId: clientSharedValues.serviceId, clientVersion: packageInfo.version }),\n        maxAttempts: config?.maxAttempts ?? DEFAULT_MAX_ATTEMPTS,\n        region: config?.region ?? invalidProvider(\"Region is missing\"),\n        requestHandler: RequestHandler.create(config?.requestHandler ?? defaultConfigProvider),\n        retryMode: config?.retryMode ?? (async () => (await defaultConfigProvider()).retryMode || DEFAULT_RETRY_MODE),\n        sha256: config?.sha256 ?? Sha256,\n        streamCollector: config?.streamCollector ?? streamCollector,\n        useDualstackEndpoint: config?.useDualstackEndpoint ?? (() => Promise.resolve(DEFAULT_USE_DUALSTACK_ENDPOINT)),\n        useFipsEndpoint: config?.useFipsEndpoint ?? (() => Promise.resolve(DEFAULT_USE_FIPS_ENDPOINT)),\n    };\n};\n", "export const getHttpAuthExtensionConfiguration = (runtimeConfig) => {\n    const _httpAuthSchemes = runtimeConfig.httpAuthSchemes;\n    let _httpAuthSchemeProvider = runtimeConfig.httpAuthSchemeProvider;\n    let _credentials = runtimeConfig.credentials;\n    return {\n        setHttpAuthScheme(httpAuthScheme) {\n            const index = _httpAuthSchemes.findIndex((scheme) => scheme.schemeId === httpAuthScheme.schemeId);\n            if (index === -1) {\n                _httpAuthSchemes.push(httpAuthScheme);\n            }\n            else {\n                _httpAuthSchemes.splice(index, 1, httpAuthScheme);\n            }\n        },\n        httpAuthSchemes() {\n            return _httpAuthSchemes;\n        },\n        setHttpAuthSchemeProvider(httpAuthSchemeProvider) {\n            _httpAuthSchemeProvider = httpAuthSchemeProvider;\n        },\n        httpAuthSchemeProvider() {\n            return _httpAuthSchemeProvider;\n        },\n        setCredentials(credentials) {\n            _credentials = credentials;\n        },\n        credentials() {\n            return _credentials;\n        },\n    };\n};\nexport const resolveHttpAuthRuntimeConfig = (config) => {\n    return {\n        httpAuthSchemes: config.httpAuthSchemes(),\n        httpAuthSchemeProvider: config.httpAuthSchemeProvider(),\n        credentials: config.credentials(),\n    };\n};\n", "import { getAwsRegionExtensionConfiguration, resolveAwsRegionExtensionConfiguration, } from \"@aws-sdk/region-config-resolver\";\nimport { getHttpHandlerExtensionConfiguration, resolveHttpHandlerRuntimeConfig } from \"@smithy/protocol-http\";\nimport { getDefaultExtensionConfiguration, resolveDefaultRuntimeConfig } from \"@smithy/smithy-client\";\nimport { getHttpAuthExtensionConfiguration, resolveHttpAuthRuntimeConfig } from \"./auth/httpAuthExtensionConfiguration\";\nconst asPartial = (t) => t;\nexport const resolveRuntimeExtensions = (runtimeConfig, extensions) => {\n    const extensionConfiguration = {\n        ...asPartial(getAwsRegionExtensionConfiguration(runtimeConfig)),\n        ...asPartial(getDefaultExtensionConfiguration(runtimeConfig)),\n        ...asPartial(getHttpHandlerExtensionConfiguration(runtimeConfig)),\n        ...asPartial(getHttpAuthExtensionConfiguration(runtimeConfig)),\n    };\n    extensions.forEach((extension) => extension.configure(extensionConfiguration));\n    return {\n        ...runtimeConfig,\n        ...resolveAwsRegionExtensionConfiguration(extensionConfiguration),\n        ...resolveDefaultRuntimeConfig(extensionConfiguration),\n        ...resolveHttpHandlerRuntimeConfig(extensionConfiguration),\n        ...resolveHttpAuthRuntimeConfig(extensionConfiguration),\n    };\n};\n", "import { getHostHeaderPlugin, resolveHostHeaderConfig, } from \"@aws-sdk/middleware-host-header\";\nimport { getLoggerPlugin } from \"@aws-sdk/middleware-logger\";\nimport { getRecursionDetectionPlugin } from \"@aws-sdk/middleware-recursion-detection\";\nimport { getUserAgentPlugin, resolveUserAgentConfig, } from \"@aws-sdk/middleware-user-agent\";\nimport { resolveRegionConfig } from \"@smithy/config-resolver\";\nimport { DefaultIdentityProviderConfig, getHttpAuthSchemeEndpointRuleSetPlugin, getHttpSigningPlugin, } from \"@smithy/core\";\nimport { getContentLengthPlugin } from \"@smithy/middleware-content-length\";\nimport { resolveEndpointConfig } from \"@smithy/middleware-endpoint\";\nimport { getRetryPlugin, resolveRetryConfig } from \"@smithy/middleware-retry\";\nimport { Client as __Client, } from \"@smithy/smithy-client\";\nimport { defaultCognitoIdentityHttpAuthSchemeParametersProvider, resolveHttpAuthSchemeConfig, } from \"./auth/httpAuthSchemeProvider\";\nimport { resolveClientEndpointParameters, } from \"./endpoint/EndpointParameters\";\nimport { getRuntimeConfig as __getRuntimeConfig } from \"./runtimeConfig\";\nimport { resolveRuntimeExtensions } from \"./runtimeExtensions\";\nexport { __Client };\nexport class CognitoIdentityClient extends __Client {\n    constructor(...[configuration]) {\n        const _config_0 = __getRuntimeConfig(configuration || {});\n        const _config_1 = resolveClientEndpointParameters(_config_0);\n        const _config_2 = resolveUserAgentConfig(_config_1);\n        const _config_3 = resolveRetryConfig(_config_2);\n        const _config_4 = resolveRegionConfig(_config_3);\n        const _config_5 = resolveHostHeaderConfig(_config_4);\n        const _config_6 = resolveEndpointConfig(_config_5);\n        const _config_7 = resolveHttpAuthSchemeConfig(_config_6);\n        const _config_8 = resolveRuntimeExtensions(_config_7, configuration?.extensions || []);\n        super(_config_8);\n        this.config = _config_8;\n        this.middlewareStack.use(getUserAgentPlugin(this.config));\n        this.middlewareStack.use(getRetryPlugin(this.config));\n        this.middlewareStack.use(getContentLengthPlugin(this.config));\n        this.middlewareStack.use(getHostHeaderPlugin(this.config));\n        this.middlewareStack.use(getLoggerPlugin(this.config));\n        this.middlewareStack.use(getRecursionDetectionPlugin(this.config));\n        this.middlewareStack.use(getHttpAuthSchemeEndpointRuleSetPlugin(this.config, {\n            httpAuthSchemeParametersProvider: defaultCognitoIdentityHttpAuthSchemeParametersProvider,\n            identityProviderConfigProvider: async (config) => new DefaultIdentityProviderConfig({\n                \"aws.auth#sigv4\": config.credentials,\n            }),\n        }));\n        this.middlewareStack.use(getHttpSigningPlugin(this.config));\n    }\n    destroy() {\n        super.destroy();\n    }\n}\n", "import { ServiceException as __ServiceException, } from \"@smithy/smithy-client\";\nexport { __ServiceException };\nexport class CognitoIdentityServiceException extends __ServiceException {\n    constructor(options) {\n        super(options);\n        Object.setPrototypeOf(this, CognitoIdentityServiceException.prototype);\n    }\n}\n", "import { SENSITIVE_STRING } from \"@smithy/smithy-client\";\nimport { CognitoIdentityServiceException as __BaseException } from \"./CognitoIdentityServiceException\";\nexport const AmbiguousRoleResolutionType = {\n    AUTHENTICATED_ROLE: \"AuthenticatedRole\",\n    DENY: \"Deny\",\n};\nexport class InternalErrorException extends __BaseException {\n    constructor(opts) {\n        super({\n            name: \"InternalErrorException\",\n            $fault: \"server\",\n            ...opts,\n        });\n        this.name = \"InternalErrorException\";\n        this.$fault = \"server\";\n        Object.setPrototypeOf(this, InternalErrorException.prototype);\n    }\n}\nexport class InvalidParameterException extends __BaseException {\n    constructor(opts) {\n        super({\n            name: \"InvalidParameterException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        this.name = \"InvalidParameterException\";\n        this.$fault = \"client\";\n        Object.setPrototypeOf(this, InvalidParameterException.prototype);\n    }\n}\nexport class LimitExceededException extends __BaseException {\n    constructor(opts) {\n        super({\n            name: \"LimitExceededException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        this.name = \"LimitExceededException\";\n        this.$fault = \"client\";\n        Object.setPrototypeOf(this, LimitExceededException.prototype);\n    }\n}\nexport class NotAuthorizedException extends __BaseException {\n    constructor(opts) {\n        super({\n            name: \"NotAuthorizedException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        this.name = \"NotAuthorizedException\";\n        this.$fault = \"client\";\n        Object.setPrototypeOf(this, NotAuthorizedException.prototype);\n    }\n}\nexport class ResourceConflictException extends __BaseException {\n    constructor(opts) {\n        super({\n            name: \"ResourceConflictException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        this.name = \"ResourceConflictException\";\n        this.$fault = \"client\";\n        Object.setPrototypeOf(this, ResourceConflictException.prototype);\n    }\n}\nexport class TooManyRequestsException extends __BaseException {\n    constructor(opts) {\n        super({\n            name: \"TooManyRequestsException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        this.name = \"TooManyRequestsException\";\n        this.$fault = \"client\";\n        Object.setPrototypeOf(this, TooManyRequestsException.prototype);\n    }\n}\nexport const ErrorCode = {\n    ACCESS_DENIED: \"AccessDenied\",\n    INTERNAL_SERVER_ERROR: \"InternalServerError\",\n};\nexport class ResourceNotFoundException extends __BaseException {\n    constructor(opts) {\n        super({\n            name: \"ResourceNotFoundException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        this.name = \"ResourceNotFoundException\";\n        this.$fault = \"client\";\n        Object.setPrototypeOf(this, ResourceNotFoundException.prototype);\n    }\n}\nexport class ExternalServiceException extends __BaseException {\n    constructor(opts) {\n        super({\n            name: \"ExternalServiceException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        this.name = \"ExternalServiceException\";\n        this.$fault = \"client\";\n        Object.setPrototypeOf(this, ExternalServiceException.prototype);\n    }\n}\nexport class InvalidIdentityPoolConfigurationException extends __BaseException {\n    constructor(opts) {\n        super({\n            name: \"InvalidIdentityPoolConfigurationException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        this.name = \"InvalidIdentityPoolConfigurationException\";\n        this.$fault = \"client\";\n        Object.setPrototypeOf(this, InvalidIdentityPoolConfigurationException.prototype);\n    }\n}\nexport const MappingRuleMatchType = {\n    CONTAINS: \"Contains\",\n    EQUALS: \"Equals\",\n    NOT_EQUAL: \"NotEqual\",\n    STARTS_WITH: \"StartsWith\",\n};\nexport const RoleMappingType = {\n    RULES: \"Rules\",\n    TOKEN: \"Token\",\n};\nexport class DeveloperUserAlreadyRegisteredException extends __BaseException {\n    constructor(opts) {\n        super({\n            name: \"DeveloperUserAlreadyRegisteredException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        this.name = \"DeveloperUserAlreadyRegisteredException\";\n        this.$fault = \"client\";\n        Object.setPrototypeOf(this, DeveloperUserAlreadyRegisteredException.prototype);\n    }\n}\nexport class ConcurrentModificationException extends __BaseException {\n    constructor(opts) {\n        super({\n            name: \"ConcurrentModificationException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        this.name = \"ConcurrentModificationException\";\n        this.$fault = \"client\";\n        Object.setPrototypeOf(this, ConcurrentModificationException.prototype);\n    }\n}\nexport const GetCredentialsForIdentityInputFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.Logins && { Logins: SENSITIVE_STRING }),\n});\nexport const CredentialsFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.SecretKey && { SecretKey: SENSITIVE_STRING }),\n});\nexport const GetCredentialsForIdentityResponseFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.Credentials && { Credentials: CredentialsFilterSensitiveLog(obj.Credentials) }),\n});\nexport const GetIdInputFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.Logins && { Logins: SENSITIVE_STRING }),\n});\nexport const GetOpenIdTokenInputFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.Logins && { Logins: SENSITIVE_STRING }),\n});\nexport const GetOpenIdTokenResponseFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.Token && { Token: SENSITIVE_STRING }),\n});\nexport const GetOpenIdTokenForDeveloperIdentityInputFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.Logins && { Logins: SENSITIVE_STRING }),\n});\nexport const GetOpenIdTokenForDeveloperIdentityResponseFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.Token && { Token: SENSITIVE_STRING }),\n});\nexport const UnlinkIdentityInputFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.Logins && { Logins: SENSITIVE_STRING }),\n});\n", "import { loadRestJsonErrorCode, parseJsonBody as parseBody, parseJsonErrorBody as parseErrorBody } from \"@aws-sdk/core\";\nimport { HttpRequest as __HttpRequest } from \"@smithy/protocol-http\";\nimport { _json, collectBody, decorateServiceException as __decorateServiceException, expectNonNull as __expectNonNull, expectNumber as __expectNumber, expectString as __expectString, parseEpochTimestamp as __parseEpochTimestamp, take, withBaseException, } from \"@smithy/smithy-client\";\nimport { CognitoIdentityServiceException as __BaseException } from \"../models/CognitoIdentityServiceException\";\nimport { ConcurrentModificationException, DeveloperUserAlreadyRegisteredException, ExternalServiceException, InternalErrorException, InvalidIdentityPoolConfigurationException, InvalidParameterException, LimitExceededException, NotAuthorizedException, ResourceConflictException, ResourceNotFoundException, TooManyRequestsException, } from \"../models/models_0\";\nexport const se_CreateIdentityPoolCommand = async (input, context) => {\n    const headers = sharedHeaders(\"CreateIdentityPool\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const se_DeleteIdentitiesCommand = async (input, context) => {\n    const headers = sharedHeaders(\"DeleteIdentities\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const se_DeleteIdentityPoolCommand = async (input, context) => {\n    const headers = sharedHeaders(\"DeleteIdentityPool\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const se_DescribeIdentityCommand = async (input, context) => {\n    const headers = sharedHeaders(\"DescribeIdentity\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const se_DescribeIdentityPoolCommand = async (input, context) => {\n    const headers = sharedHeaders(\"DescribeIdentityPool\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const se_GetCredentialsForIdentityCommand = async (input, context) => {\n    const headers = sharedHeaders(\"GetCredentialsForIdentity\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const se_GetIdCommand = async (input, context) => {\n    const headers = sharedHeaders(\"GetId\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const se_GetIdentityPoolRolesCommand = async (input, context) => {\n    const headers = sharedHeaders(\"GetIdentityPoolRoles\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const se_GetOpenIdTokenCommand = async (input, context) => {\n    const headers = sharedHeaders(\"GetOpenIdToken\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const se_GetOpenIdTokenForDeveloperIdentityCommand = async (input, context) => {\n    const headers = sharedHeaders(\"GetOpenIdTokenForDeveloperIdentity\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const se_GetPrincipalTagAttributeMapCommand = async (input, context) => {\n    const headers = sharedHeaders(\"GetPrincipalTagAttributeMap\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const se_ListIdentitiesCommand = async (input, context) => {\n    const headers = sharedHeaders(\"ListIdentities\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const se_ListIdentityPoolsCommand = async (input, context) => {\n    const headers = sharedHeaders(\"ListIdentityPools\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const se_ListTagsForResourceCommand = async (input, context) => {\n    const headers = sharedHeaders(\"ListTagsForResource\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const se_LookupDeveloperIdentityCommand = async (input, context) => {\n    const headers = sharedHeaders(\"LookupDeveloperIdentity\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const se_MergeDeveloperIdentitiesCommand = async (input, context) => {\n    const headers = sharedHeaders(\"MergeDeveloperIdentities\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const se_SetIdentityPoolRolesCommand = async (input, context) => {\n    const headers = sharedHeaders(\"SetIdentityPoolRoles\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const se_SetPrincipalTagAttributeMapCommand = async (input, context) => {\n    const headers = sharedHeaders(\"SetPrincipalTagAttributeMap\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const se_TagResourceCommand = async (input, context) => {\n    const headers = sharedHeaders(\"TagResource\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const se_UnlinkDeveloperIdentityCommand = async (input, context) => {\n    const headers = sharedHeaders(\"UnlinkDeveloperIdentity\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const se_UnlinkIdentityCommand = async (input, context) => {\n    const headers = sharedHeaders(\"UnlinkIdentity\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const se_UntagResourceCommand = async (input, context) => {\n    const headers = sharedHeaders(\"UntagResource\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const se_UpdateIdentityPoolCommand = async (input, context) => {\n    const headers = sharedHeaders(\"UpdateIdentityPool\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const de_CreateIdentityPoolCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const data = await parseBody(output.body, context);\n    let contents = {};\n    contents = _json(data);\n    const response = {\n        $metadata: deserializeMetadata(output),\n        ...contents,\n    };\n    return response;\n};\nexport const de_DeleteIdentitiesCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const data = await parseBody(output.body, context);\n    let contents = {};\n    contents = _json(data);\n    const response = {\n        $metadata: deserializeMetadata(output),\n        ...contents,\n    };\n    return response;\n};\nexport const de_DeleteIdentityPoolCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    await collectBody(output.body, context);\n    const response = {\n        $metadata: deserializeMetadata(output),\n    };\n    return response;\n};\nexport const de_DescribeIdentityCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const data = await parseBody(output.body, context);\n    let contents = {};\n    contents = de_IdentityDescription(data, context);\n    const response = {\n        $metadata: deserializeMetadata(output),\n        ...contents,\n    };\n    return response;\n};\nexport const de_DescribeIdentityPoolCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const data = await parseBody(output.body, context);\n    let contents = {};\n    contents = _json(data);\n    const response = {\n        $metadata: deserializeMetadata(output),\n        ...contents,\n    };\n    return response;\n};\nexport const de_GetCredentialsForIdentityCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const data = await parseBody(output.body, context);\n    let contents = {};\n    contents = de_GetCredentialsForIdentityResponse(data, context);\n    const response = {\n        $metadata: deserializeMetadata(output),\n        ...contents,\n    };\n    return response;\n};\nexport const de_GetIdCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const data = await parseBody(output.body, context);\n    let contents = {};\n    contents = _json(data);\n    const response = {\n        $metadata: deserializeMetadata(output),\n        ...contents,\n    };\n    return response;\n};\nexport const de_GetIdentityPoolRolesCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const data = await parseBody(output.body, context);\n    let contents = {};\n    contents = _json(data);\n    const response = {\n        $metadata: deserializeMetadata(output),\n        ...contents,\n    };\n    return response;\n};\nexport const de_GetOpenIdTokenCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const data = await parseBody(output.body, context);\n    let contents = {};\n    contents = _json(data);\n    const response = {\n        $metadata: deserializeMetadata(output),\n        ...contents,\n    };\n    return response;\n};\nexport const de_GetOpenIdTokenForDeveloperIdentityCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const data = await parseBody(output.body, context);\n    let contents = {};\n    contents = _json(data);\n    const response = {\n        $metadata: deserializeMetadata(output),\n        ...contents,\n    };\n    return response;\n};\nexport const de_GetPrincipalTagAttributeMapCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const data = await parseBody(output.body, context);\n    let contents = {};\n    contents = _json(data);\n    const response = {\n        $metadata: deserializeMetadata(output),\n        ...contents,\n    };\n    return response;\n};\nexport const de_ListIdentitiesCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const data = await parseBody(output.body, context);\n    let contents = {};\n    contents = de_ListIdentitiesResponse(data, context);\n    const response = {\n        $metadata: deserializeMetadata(output),\n        ...contents,\n    };\n    return response;\n};\nexport const de_ListIdentityPoolsCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const data = await parseBody(output.body, context);\n    let contents = {};\n    contents = _json(data);\n    const response = {\n        $metadata: deserializeMetadata(output),\n        ...contents,\n    };\n    return response;\n};\nexport const de_ListTagsForResourceCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const data = await parseBody(output.body, context);\n    let contents = {};\n    contents = _json(data);\n    const response = {\n        $metadata: deserializeMetadata(output),\n        ...contents,\n    };\n    return response;\n};\nexport const de_LookupDeveloperIdentityCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const data = await parseBody(output.body, context);\n    let contents = {};\n    contents = _json(data);\n    const response = {\n        $metadata: deserializeMetadata(output),\n        ...contents,\n    };\n    return response;\n};\nexport const de_MergeDeveloperIdentitiesCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const data = await parseBody(output.body, context);\n    let contents = {};\n    contents = _json(data);\n    const response = {\n        $metadata: deserializeMetadata(output),\n        ...contents,\n    };\n    return response;\n};\nexport const de_SetIdentityPoolRolesCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    await collectBody(output.body, context);\n    const response = {\n        $metadata: deserializeMetadata(output),\n    };\n    return response;\n};\nexport const de_SetPrincipalTagAttributeMapCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const data = await parseBody(output.body, context);\n    let contents = {};\n    contents = _json(data);\n    const response = {\n        $metadata: deserializeMetadata(output),\n        ...contents,\n    };\n    return response;\n};\nexport const de_TagResourceCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const data = await parseBody(output.body, context);\n    let contents = {};\n    contents = _json(data);\n    const response = {\n        $metadata: deserializeMetadata(output),\n        ...contents,\n    };\n    return response;\n};\nexport const de_UnlinkDeveloperIdentityCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    await collectBody(output.body, context);\n    const response = {\n        $metadata: deserializeMetadata(output),\n    };\n    return response;\n};\nexport const de_UnlinkIdentityCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    await collectBody(output.body, context);\n    const response = {\n        $metadata: deserializeMetadata(output),\n    };\n    return response;\n};\nexport const de_UntagResourceCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const data = await parseBody(output.body, context);\n    let contents = {};\n    contents = _json(data);\n    const response = {\n        $metadata: deserializeMetadata(output),\n        ...contents,\n    };\n    return response;\n};\nexport const de_UpdateIdentityPoolCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const data = await parseBody(output.body, context);\n    let contents = {};\n    contents = _json(data);\n    const response = {\n        $metadata: deserializeMetadata(output),\n        ...contents,\n    };\n    return response;\n};\nconst de_CommandError = async (output, context) => {\n    const parsedOutput = {\n        ...output,\n        body: await parseErrorBody(output.body, context),\n    };\n    const errorCode = loadRestJsonErrorCode(output, parsedOutput.body);\n    switch (errorCode) {\n        case \"InternalErrorException\":\n        case \"com.amazonaws.cognitoidentity#InternalErrorException\":\n            throw await de_InternalErrorExceptionRes(parsedOutput, context);\n        case \"InvalidParameterException\":\n        case \"com.amazonaws.cognitoidentity#InvalidParameterException\":\n            throw await de_InvalidParameterExceptionRes(parsedOutput, context);\n        case \"LimitExceededException\":\n        case \"com.amazonaws.cognitoidentity#LimitExceededException\":\n            throw await de_LimitExceededExceptionRes(parsedOutput, context);\n        case \"NotAuthorizedException\":\n        case \"com.amazonaws.cognitoidentity#NotAuthorizedException\":\n            throw await de_NotAuthorizedExceptionRes(parsedOutput, context);\n        case \"ResourceConflictException\":\n        case \"com.amazonaws.cognitoidentity#ResourceConflictException\":\n            throw await de_ResourceConflictExceptionRes(parsedOutput, context);\n        case \"TooManyRequestsException\":\n        case \"com.amazonaws.cognitoidentity#TooManyRequestsException\":\n            throw await de_TooManyRequestsExceptionRes(parsedOutput, context);\n        case \"ResourceNotFoundException\":\n        case \"com.amazonaws.cognitoidentity#ResourceNotFoundException\":\n            throw await de_ResourceNotFoundExceptionRes(parsedOutput, context);\n        case \"ExternalServiceException\":\n        case \"com.amazonaws.cognitoidentity#ExternalServiceException\":\n            throw await de_ExternalServiceExceptionRes(parsedOutput, context);\n        case \"InvalidIdentityPoolConfigurationException\":\n        case \"com.amazonaws.cognitoidentity#InvalidIdentityPoolConfigurationException\":\n            throw await de_InvalidIdentityPoolConfigurationExceptionRes(parsedOutput, context);\n        case \"DeveloperUserAlreadyRegisteredException\":\n        case \"com.amazonaws.cognitoidentity#DeveloperUserAlreadyRegisteredException\":\n            throw await de_DeveloperUserAlreadyRegisteredExceptionRes(parsedOutput, context);\n        case \"ConcurrentModificationException\":\n        case \"com.amazonaws.cognitoidentity#ConcurrentModificationException\":\n            throw await de_ConcurrentModificationExceptionRes(parsedOutput, context);\n        default:\n            const parsedBody = parsedOutput.body;\n            return throwDefaultError({\n                output,\n                parsedBody,\n                errorCode,\n            });\n    }\n};\nconst de_ConcurrentModificationExceptionRes = async (parsedOutput, context) => {\n    const body = parsedOutput.body;\n    const deserialized = _json(body);\n    const exception = new ConcurrentModificationException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...deserialized,\n    });\n    return __decorateServiceException(exception, body);\n};\nconst de_DeveloperUserAlreadyRegisteredExceptionRes = async (parsedOutput, context) => {\n    const body = parsedOutput.body;\n    const deserialized = _json(body);\n    const exception = new DeveloperUserAlreadyRegisteredException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...deserialized,\n    });\n    return __decorateServiceException(exception, body);\n};\nconst de_ExternalServiceExceptionRes = async (parsedOutput, context) => {\n    const body = parsedOutput.body;\n    const deserialized = _json(body);\n    const exception = new ExternalServiceException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...deserialized,\n    });\n    return __decorateServiceException(exception, body);\n};\nconst de_InternalErrorExceptionRes = async (parsedOutput, context) => {\n    const body = parsedOutput.body;\n    const deserialized = _json(body);\n    const exception = new InternalErrorException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...deserialized,\n    });\n    return __decorateServiceException(exception, body);\n};\nconst de_InvalidIdentityPoolConfigurationExceptionRes = async (parsedOutput, context) => {\n    const body = parsedOutput.body;\n    const deserialized = _json(body);\n    const exception = new InvalidIdentityPoolConfigurationException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...deserialized,\n    });\n    return __decorateServiceException(exception, body);\n};\nconst de_InvalidParameterExceptionRes = async (parsedOutput, context) => {\n    const body = parsedOutput.body;\n    const deserialized = _json(body);\n    const exception = new InvalidParameterException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...deserialized,\n    });\n    return __decorateServiceException(exception, body);\n};\nconst de_LimitExceededExceptionRes = async (parsedOutput, context) => {\n    const body = parsedOutput.body;\n    const deserialized = _json(body);\n    const exception = new LimitExceededException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...deserialized,\n    });\n    return __decorateServiceException(exception, body);\n};\nconst de_NotAuthorizedExceptionRes = async (parsedOutput, context) => {\n    const body = parsedOutput.body;\n    const deserialized = _json(body);\n    const exception = new NotAuthorizedException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...deserialized,\n    });\n    return __decorateServiceException(exception, body);\n};\nconst de_ResourceConflictExceptionRes = async (parsedOutput, context) => {\n    const body = parsedOutput.body;\n    const deserialized = _json(body);\n    const exception = new ResourceConflictException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...deserialized,\n    });\n    return __decorateServiceException(exception, body);\n};\nconst de_ResourceNotFoundExceptionRes = async (parsedOutput, context) => {\n    const body = parsedOutput.body;\n    const deserialized = _json(body);\n    const exception = new ResourceNotFoundException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...deserialized,\n    });\n    return __decorateServiceException(exception, body);\n};\nconst de_TooManyRequestsExceptionRes = async (parsedOutput, context) => {\n    const body = parsedOutput.body;\n    const deserialized = _json(body);\n    const exception = new TooManyRequestsException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...deserialized,\n    });\n    return __decorateServiceException(exception, body);\n};\nconst de_Credentials = (output, context) => {\n    return take(output, {\n        AccessKeyId: __expectString,\n        Expiration: (_) => __expectNonNull(__parseEpochTimestamp(__expectNumber(_))),\n        SecretKey: __expectString,\n        SessionToken: __expectString,\n    });\n};\nconst de_GetCredentialsForIdentityResponse = (output, context) => {\n    return take(output, {\n        Credentials: (_) => de_Credentials(_, context),\n        IdentityId: __expectString,\n    });\n};\nconst de_IdentitiesList = (output, context) => {\n    const retVal = (output || [])\n        .filter((e) => e != null)\n        .map((entry) => {\n        return de_IdentityDescription(entry, context);\n    });\n    return retVal;\n};\nconst de_IdentityDescription = (output, context) => {\n    return take(output, {\n        CreationDate: (_) => __expectNonNull(__parseEpochTimestamp(__expectNumber(_))),\n        IdentityId: __expectString,\n        LastModifiedDate: (_) => __expectNonNull(__parseEpochTimestamp(__expectNumber(_))),\n        Logins: _json,\n    });\n};\nconst de_ListIdentitiesResponse = (output, context) => {\n    return take(output, {\n        Identities: (_) => de_IdentitiesList(_, context),\n        IdentityPoolId: __expectString,\n        NextToken: __expectString,\n    });\n};\nconst deserializeMetadata = (output) => ({\n    httpStatusCode: output.statusCode,\n    requestId: output.headers[\"x-amzn-requestid\"] ?? output.headers[\"x-amzn-request-id\"] ?? output.headers[\"x-amz-request-id\"],\n    extendedRequestId: output.headers[\"x-amz-id-2\"],\n    cfId: output.headers[\"x-amz-cf-id\"],\n});\nconst collectBodyString = (streamBody, context) => collectBody(streamBody, context).then((body) => context.utf8Encoder(body));\nconst throwDefaultError = withBaseException(__BaseException);\nconst buildHttpRpcRequest = async (context, headers, path, resolvedHostname, body) => {\n    const { hostname, protocol = \"https\", port, path: basePath } = await context.endpoint();\n    const contents = {\n        protocol,\n        hostname,\n        port,\n        method: \"POST\",\n        path: basePath.endsWith(\"/\") ? basePath.slice(0, -1) + path : basePath + path,\n        headers,\n    };\n    if (resolvedHostname !== undefined) {\n        contents.hostname = resolvedHostname;\n    }\n    if (body !== undefined) {\n        contents.body = body;\n    }\n    return new __HttpRequest(contents);\n};\nfunction sharedHeaders(operation) {\n    return {\n        \"content-type\": \"application/x-amz-json-1.1\",\n        \"x-amz-target\": `AWSCognitoIdentityService.${operation}`,\n    };\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { de_CreateIdentityPoolCommand, se_CreateIdentityPoolCommand } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class CreateIdentityPoolCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"CreateIdentityPool\", {})\n    .n(\"CognitoIdentityClient\", \"CreateIdentityPoolCommand\")\n    .f(void 0, void 0)\n    .ser(se_CreateIdentityPoolCommand)\n    .de(de_CreateIdentityPoolCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { de_DeleteIdentitiesCommand, se_DeleteIdentitiesCommand } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class DeleteIdentitiesCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"DeleteIdentities\", {})\n    .n(\"CognitoIdentityClient\", \"DeleteIdentitiesCommand\")\n    .f(void 0, void 0)\n    .ser(se_DeleteIdentitiesCommand)\n    .de(de_DeleteIdentitiesCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { de_DeleteIdentityPoolCommand, se_DeleteIdentityPoolCommand } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class DeleteIdentityPoolCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"DeleteIdentityPool\", {})\n    .n(\"CognitoIdentityClient\", \"DeleteIdentityPoolCommand\")\n    .f(void 0, void 0)\n    .ser(se_DeleteIdentityPoolCommand)\n    .de(de_DeleteIdentityPoolCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { de_DescribeIdentityCommand, se_DescribeIdentityCommand } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class DescribeIdentityCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"DescribeIdentity\", {})\n    .n(\"CognitoIdentityClient\", \"DescribeIdentityCommand\")\n    .f(void 0, void 0)\n    .ser(se_DescribeIdentityCommand)\n    .de(de_DescribeIdentityCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { de_DescribeIdentityPoolCommand, se_DescribeIdentityPoolCommand } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class DescribeIdentityPoolCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"DescribeIdentityPool\", {})\n    .n(\"CognitoIdentityClient\", \"DescribeIdentityPoolCommand\")\n    .f(void 0, void 0)\n    .ser(se_DescribeIdentityPoolCommand)\n    .de(de_DescribeIdentityPoolCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { GetCredentialsForIdentityInputFilterSensitiveLog, GetCredentialsForIdentityResponseFilterSensitiveLog, } from \"../models/models_0\";\nimport { de_GetCredentialsForIdentityCommand, se_GetCredentialsForIdentityCommand } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class GetCredentialsForIdentityCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"GetCredentialsForIdentity\", {})\n    .n(\"CognitoIdentityClient\", \"GetCredentialsForIdentityCommand\")\n    .f(GetCredentialsForIdentityInputFilterSensitiveLog, GetCredentialsForIdentityResponseFilterSensitiveLog)\n    .ser(se_GetCredentialsForIdentityCommand)\n    .de(de_GetCredentialsForIdentityCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { GetIdInputFilterSensitiveLog } from \"../models/models_0\";\nimport { de_GetIdCommand, se_GetIdCommand } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class GetIdCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"GetId\", {})\n    .n(\"CognitoIdentityClient\", \"GetIdCommand\")\n    .f(GetIdInputFilterSensitiveLog, void 0)\n    .ser(se_GetIdCommand)\n    .de(de_GetIdCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { de_GetIdentityPoolRolesCommand, se_GetIdentityPoolRolesCommand } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class GetIdentityPoolRolesCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"GetIdentityPoolRoles\", {})\n    .n(\"CognitoIdentityClient\", \"GetIdentityPoolRolesCommand\")\n    .f(void 0, void 0)\n    .ser(se_GetIdentityPoolRolesCommand)\n    .de(de_GetIdentityPoolRolesCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { GetOpenIdTokenInputFilterSensitiveLog, GetOpenIdTokenResponseFilterSensitiveLog, } from \"../models/models_0\";\nimport { de_GetOpenIdTokenCommand, se_GetOpenIdTokenCommand } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class GetOpenIdTokenCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"GetOpenIdToken\", {})\n    .n(\"CognitoIdentityClient\", \"GetOpenIdTokenCommand\")\n    .f(GetOpenIdTokenInputFilterSensitiveLog, GetOpenIdTokenResponseFilterSensitiveLog)\n    .ser(se_GetOpenIdTokenCommand)\n    .de(de_GetOpenIdTokenCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { GetOpenIdTokenForDeveloperIdentityInputFilterSensitiveLog, GetOpenIdTokenForDeveloperIdentityResponseFilterSensitiveLog, } from \"../models/models_0\";\nimport { de_GetOpenIdTokenForDeveloperIdentityCommand, se_GetOpenIdTokenForDeveloperIdentityCommand, } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class GetOpenIdTokenForDeveloperIdentityCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"GetOpenIdTokenForDeveloperIdentity\", {})\n    .n(\"CognitoIdentityClient\", \"GetOpenIdTokenForDeveloperIdentityCommand\")\n    .f(GetOpenIdTokenForDeveloperIdentityInputFilterSensitiveLog, GetOpenIdTokenForDeveloperIdentityResponseFilterSensitiveLog)\n    .ser(se_GetOpenIdTokenForDeveloperIdentityCommand)\n    .de(de_GetOpenIdTokenForDeveloperIdentityCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { de_GetPrincipalTagAttributeMapCommand, se_GetPrincipalTagAttributeMapCommand } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class GetPrincipalTagAttributeMapCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"GetPrincipalTagAttributeMap\", {})\n    .n(\"CognitoIdentityClient\", \"GetPrincipalTagAttributeMapCommand\")\n    .f(void 0, void 0)\n    .ser(se_GetPrincipalTagAttributeMapCommand)\n    .de(de_GetPrincipalTagAttributeMapCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { de_ListIdentitiesCommand, se_ListIdentitiesCommand } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class ListIdentitiesCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"ListIdentities\", {})\n    .n(\"CognitoIdentityClient\", \"ListIdentitiesCommand\")\n    .f(void 0, void 0)\n    .ser(se_ListIdentitiesCommand)\n    .de(de_ListIdentitiesCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { de_ListIdentityPoolsCommand, se_ListIdentityPoolsCommand } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class ListIdentityPoolsCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"ListIdentityPools\", {})\n    .n(\"CognitoIdentityClient\", \"ListIdentityPoolsCommand\")\n    .f(void 0, void 0)\n    .ser(se_ListIdentityPoolsCommand)\n    .de(de_ListIdentityPoolsCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { de_ListTagsForResourceCommand, se_ListTagsForResourceCommand } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class ListTagsForResourceCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"ListTagsForResource\", {})\n    .n(\"CognitoIdentityClient\", \"ListTagsForResourceCommand\")\n    .f(void 0, void 0)\n    .ser(se_ListTagsForResourceCommand)\n    .de(de_ListTagsForResourceCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { de_LookupDeveloperIdentityCommand, se_LookupDeveloperIdentityCommand } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class LookupDeveloperIdentityCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"LookupDeveloperIdentity\", {})\n    .n(\"CognitoIdentityClient\", \"LookupDeveloperIdentityCommand\")\n    .f(void 0, void 0)\n    .ser(se_LookupDeveloperIdentityCommand)\n    .de(de_LookupDeveloperIdentityCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { de_MergeDeveloperIdentitiesCommand, se_MergeDeveloperIdentitiesCommand } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class MergeDeveloperIdentitiesCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"MergeDeveloperIdentities\", {})\n    .n(\"CognitoIdentityClient\", \"MergeDeveloperIdentitiesCommand\")\n    .f(void 0, void 0)\n    .ser(se_MergeDeveloperIdentitiesCommand)\n    .de(de_MergeDeveloperIdentitiesCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { de_SetIdentityPoolRolesCommand, se_SetIdentityPoolRolesCommand } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class SetIdentityPoolRolesCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"SetIdentityPoolRoles\", {})\n    .n(\"CognitoIdentityClient\", \"SetIdentityPoolRolesCommand\")\n    .f(void 0, void 0)\n    .ser(se_SetIdentityPoolRolesCommand)\n    .de(de_SetIdentityPoolRolesCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { de_SetPrincipalTagAttributeMapCommand, se_SetPrincipalTagAttributeMapCommand } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class SetPrincipalTagAttributeMapCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"SetPrincipalTagAttributeMap\", {})\n    .n(\"CognitoIdentityClient\", \"SetPrincipalTagAttributeMapCommand\")\n    .f(void 0, void 0)\n    .ser(se_SetPrincipalTagAttributeMapCommand)\n    .de(de_SetPrincipalTagAttributeMapCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { de_TagResourceCommand, se_TagResourceCommand } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class TagResourceCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"TagResource\", {})\n    .n(\"CognitoIdentityClient\", \"TagResourceCommand\")\n    .f(void 0, void 0)\n    .ser(se_TagResourceCommand)\n    .de(de_TagResourceCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { de_UnlinkDeveloperIdentityCommand, se_UnlinkDeveloperIdentityCommand } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class UnlinkDeveloperIdentityCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"UnlinkDeveloperIdentity\", {})\n    .n(\"CognitoIdentityClient\", \"UnlinkDeveloperIdentityCommand\")\n    .f(void 0, void 0)\n    .ser(se_UnlinkDeveloperIdentityCommand)\n    .de(de_UnlinkDeveloperIdentityCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { UnlinkIdentityInputFilterSensitiveLog } from \"../models/models_0\";\nimport { de_UnlinkIdentityCommand, se_UnlinkIdentityCommand } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class UnlinkIdentityCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"UnlinkIdentity\", {})\n    .n(\"CognitoIdentityClient\", \"UnlinkIdentityCommand\")\n    .f(UnlinkIdentityInputFilterSensitiveLog, void 0)\n    .ser(se_UnlinkIdentityCommand)\n    .de(de_UnlinkIdentityCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { de_UntagResourceCommand, se_UntagResourceCommand } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class UntagResourceCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"UntagResource\", {})\n    .n(\"CognitoIdentityClient\", \"UntagResourceCommand\")\n    .f(void 0, void 0)\n    .ser(se_UntagResourceCommand)\n    .de(de_UntagResourceCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { de_UpdateIdentityPoolCommand, se_UpdateIdentityPoolCommand } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class UpdateIdentityPoolCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"UpdateIdentityPool\", {})\n    .n(\"CognitoIdentityClient\", \"UpdateIdentityPoolCommand\")\n    .f(void 0, void 0)\n    .ser(se_UpdateIdentityPoolCommand)\n    .de(de_UpdateIdentityPoolCommand)\n    .build() {\n}\n", "import { createAggregatedClient } from \"@smithy/smithy-client\";\nimport { CognitoIdentityClient } from \"./CognitoIdentityClient\";\nimport { CreateIdentityPoolCommand, } from \"./commands/CreateIdentityPoolCommand\";\nimport { DeleteIdentitiesCommand, } from \"./commands/DeleteIdentitiesCommand\";\nimport { DeleteIdentityPoolCommand, } from \"./commands/DeleteIdentityPoolCommand\";\nimport { DescribeIdentityCommand, } from \"./commands/DescribeIdentityCommand\";\nimport { DescribeIdentityPoolCommand, } from \"./commands/DescribeIdentityPoolCommand\";\nimport { GetCredentialsForIdentityCommand, } from \"./commands/GetCredentialsForIdentityCommand\";\nimport { GetIdCommand } from \"./commands/GetIdCommand\";\nimport { GetIdentityPoolRolesCommand, } from \"./commands/GetIdentityPoolRolesCommand\";\nimport { GetOpenIdTokenCommand, } from \"./commands/GetOpenIdTokenCommand\";\nimport { GetOpenIdTokenForDeveloperIdentityCommand, } from \"./commands/GetOpenIdTokenForDeveloperIdentityCommand\";\nimport { GetPrincipalTagAttributeMapCommand, } from \"./commands/GetPrincipalTagAttributeMapCommand\";\nimport { ListIdentitiesCommand, } from \"./commands/ListIdentitiesCommand\";\nimport { ListIdentityPoolsCommand, } from \"./commands/ListIdentityPoolsCommand\";\nimport { ListTagsForResourceCommand, } from \"./commands/ListTagsForResourceCommand\";\nimport { LookupDeveloperIdentityCommand, } from \"./commands/LookupDeveloperIdentityCommand\";\nimport { MergeDeveloperIdentitiesCommand, } from \"./commands/MergeDeveloperIdentitiesCommand\";\nimport { SetIdentityPoolRolesCommand, } from \"./commands/SetIdentityPoolRolesCommand\";\nimport { SetPrincipalTagAttributeMapCommand, } from \"./commands/SetPrincipalTagAttributeMapCommand\";\nimport { TagResourceCommand } from \"./commands/TagResourceCommand\";\nimport { UnlinkDeveloperIdentityCommand, } from \"./commands/UnlinkDeveloperIdentityCommand\";\nimport { UnlinkIdentityCommand, } from \"./commands/UnlinkIdentityCommand\";\nimport { UntagResourceCommand, } from \"./commands/UntagResourceCommand\";\nimport { UpdateIdentityPoolCommand, } from \"./commands/UpdateIdentityPoolCommand\";\nconst commands = {\n    CreateIdentityPoolCommand,\n    DeleteIdentitiesCommand,\n    DeleteIdentityPoolCommand,\n    DescribeIdentityCommand,\n    DescribeIdentityPoolCommand,\n    GetCredentialsForIdentityCommand,\n    GetIdCommand,\n    GetIdentityPoolRolesCommand,\n    GetOpenIdTokenCommand,\n    GetOpenIdTokenForDeveloperIdentityCommand,\n    GetPrincipalTagAttributeMapCommand,\n    ListIdentitiesCommand,\n    ListIdentityPoolsCommand,\n    ListTagsForResourceCommand,\n    LookupDeveloperIdentityCommand,\n    MergeDeveloperIdentitiesCommand,\n    SetIdentityPoolRolesCommand,\n    SetPrincipalTagAttributeMapCommand,\n    TagResourceCommand,\n    UnlinkDeveloperIdentityCommand,\n    UnlinkIdentityCommand,\n    UntagResourceCommand,\n    UpdateIdentityPoolCommand,\n};\nexport class CognitoIdentity extends CognitoIdentityClient {\n}\ncreateAggregatedClient(commands, CognitoIdentity);\n", "import { createPaginator } from \"@smithy/core\";\nimport { CognitoIdentityClient } from \"../CognitoIdentityClient\";\nimport { ListIdentityPoolsCommand, } from \"../commands/ListIdentityPoolsCommand\";\nexport const paginateListIdentityPools = createPaginator(CognitoIdentityClient, ListIdentityPoolsCommand, \"NextToken\", \"NextToken\", \"MaxResults\");\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEO,IAAM,yDAAyD,OAAO,QAAQ,SAAS,UAAU;AACpG,SAAO;AAAA,IACH,WAAW,iBAAiB,OAAO,EAAE;AAAA,IACrC,QAAS,MAAM,kBAAkB,OAAO,MAAM,EAAE,MAC3C,MAAM;AACH,YAAM,IAAI,MAAM,yDAAyD;AAAA,IAC7E,GAAG;AAAA,EACX;AACJ;AACA,SAAS,iCAAiC,gBAAgB;AACtD,SAAO;AAAA,IACH,UAAU;AAAA,IACV,mBAAmB;AAAA,MACf,MAAM;AAAA,MACN,QAAQ,eAAe;AAAA,IAC3B;AAAA,IACA,qBAAqB,CAAC,QAAQ,aAAa;AAAA,MACvC,mBAAmB;AAAA,QACf;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACJ;AACA,SAAS,oCAAoC,gBAAgB;AACzD,SAAO;AAAA,IACH,UAAU;AAAA,EACd;AACJ;AACO,IAAM,+CAA+C,CAAC,mBAAmB;AAC5E,QAAM,UAAU,CAAC;AACjB,UAAQ,eAAe,WAAW;AAAA,IAC9B,KAAK,6BAA6B;AAC9B,cAAQ,KAAK,oCAAoC,cAAc,CAAC;AAChE;AAAA,IACJ;AAAA,IACA,KAAK,SAAS;AACV,cAAQ,KAAK,oCAAoC,cAAc,CAAC;AAChE;AAAA,IACJ;AAAA,IACA,KAAK,kBAAkB;AACnB,cAAQ,KAAK,oCAAoC,cAAc,CAAC;AAChE;AAAA,IACJ;AAAA,IACA,KAAK,kBAAkB;AACnB,cAAQ,KAAK,oCAAoC,cAAc,CAAC;AAChE;AAAA,IACJ;AAAA,IACA,SAAS;AACL,cAAQ,KAAK,iCAAiC,cAAc,CAAC;AAAA,IACjE;AAAA,EACJ;AACA,SAAO;AACX;AACO,IAAM,8BAA8B,CAAC,WAAW;AACnD,QAAM,WAAW,yBAAyB,MAAM;AAChD,SAAO;AAAA,IACH,GAAG;AAAA,EACP;AACJ;;;AC7DO,IAAM,kCAAkC,CAAC,YAAY;AACxD,SAAO;AAAA,IACH,GAAG;AAAA,IACH,sBAAsB,QAAQ,wBAAwB;AAAA,IACtD,iBAAiB,QAAQ,mBAAmB;AAAA,IAC5C,oBAAoB;AAAA,EACxB;AACJ;AACO,IAAM,eAAe;AAAA,EACxB,SAAS,EAAE,MAAM,iBAAiB,MAAM,kBAAkB;AAAA,EAC1D,UAAU,EAAE,MAAM,iBAAiB,MAAM,WAAW;AAAA,EACpD,QAAQ,EAAE,MAAM,iBAAiB,MAAM,SAAS;AAAA,EAChD,cAAc,EAAE,MAAM,iBAAiB,MAAM,uBAAuB;AACxE;;;ACbA;AAAA,EACE,MAAQ;AAAA,EACR,aAAe;AAAA,EACf,SAAW;AAAA,EACX,SAAW;AAAA,IACT,OAAS;AAAA,IACT,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,sBAAsB;AAAA,IACtB,eAAe;AAAA,IACf,yBAAyB;AAAA,IACzB,OAAS;AAAA,IACT,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,YAAY;AAAA,IACZ,kBAAkB;AAAA,EACpB;AAAA,EACA,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,aAAe;AAAA,EACf,cAAgB;AAAA,IACd,8BAA8B;AAAA,IAC9B,yBAAyB;AAAA,IACzB,4BAA4B;AAAA,IAC5B,uBAAuB;AAAA,IACvB,iBAAiB;AAAA,IACjB,qCAAqC;AAAA,IACrC,mCAAmC;AAAA,IACnC,8BAA8B;AAAA,IAC9B,2CAA2C;AAAA,IAC3C,kCAAkC;AAAA,IAClC,mCAAmC;AAAA,IACnC,kBAAkB;AAAA,IAClB,2BAA2B;AAAA,IAC3B,oCAAoC;AAAA,IACpC,iCAAiC;AAAA,IACjC,2BAA2B;AAAA,IAC3B,gBAAgB;AAAA,IAChB,8BAA8B;AAAA,IAC9B,qBAAqB;AAAA,IACrB,8BAA8B;AAAA,IAC9B,qCAAqC;AAAA,IACrC,+BAA+B;AAAA,IAC/B,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,gCAAgC;AAAA,IAChC,6BAA6B;AAAA,IAC7B,yBAAyB;AAAA,IACzB,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,sBAAsB;AAAA,IACtB,uBAAuB;AAAA,IACvB,oCAAoC;AAAA,IACpC,iCAAiC;AAAA,IACjC,sCAAsC;AAAA,IACtC,mCAAmC;AAAA,IACnC,0BAA0B;AAAA,IAC1B,2BAA2B;AAAA,IAC3B,sBAAsB;AAAA,IACtB,qBAAqB;AAAA,IACrB,OAAS;AAAA,EACX;AAAA,EACA,iBAAmB;AAAA,IACjB,uBAAuB;AAAA,IACvB,oBAAoB;AAAA,IACpB,eAAe;AAAA,IACf,eAAe;AAAA,IACf,cAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,QAAU;AAAA,IACV,YAAc;AAAA,EAChB;AAAA,EACA,SAAW;AAAA,IACT,MAAQ;AAAA,EACV;AAAA,EACA,eAAiB;AAAA,IACf,QAAQ;AAAA,MACN,gBAAgB;AAAA,QACd;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAS;AAAA,IACP;AAAA,EACF;AAAA,EACA,QAAU;AAAA,IACR,MAAQ;AAAA,IACR,KAAO;AAAA,EACT;AAAA,EACA,SAAW;AAAA,EACX,SAAW;AAAA,IACT,2BAA2B;AAAA,EAC7B;AAAA,EACA,gBAAgB;AAAA,IACd,2BAA2B;AAAA,EAC7B;AAAA,EACA,UAAY;AAAA,EACZ,YAAc;AAAA,IACZ,MAAQ;AAAA,IACR,KAAO;AAAA,IACP,WAAa;AAAA,EACf;AACF;;;ACxGA,IAAM,IAAI;AAAV,IAAsB,IAAI;AAA1B,IAAgC,IAAI;AAApC,IAA4C,IAAI;AAChD,IAAM,IAAI;AAAV,IAAgB,IAAI;AAApB,IAA6B,IAAI;AAAjC,IAAkD,IAAI;AAAtD,IAA+D,IAAI;AAAnE,IAA+E,IAAI;AAAnF,IAA2F,IAAI;AAA/F,IAAkH,IAAI,EAAE,CAAC,CAAC,GAAG,OAAO,QAAQ,SAAS;AAArJ,IAAwJ,IAAI,EAAE,CAAC,CAAC,GAAG,MAAM,WAAW,OAAO,QAAQ,UAAU;AAA7M,IAAgN,IAAI,EAAE,CAAC,CAAC,GAAG,WAAW;AAAtO,IAAyO,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,UAAU,GAAG,IAAI,EAAE;AAAvR,IAA0R,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,eAAe,GAAG,IAAI,EAAE;AAA7U,IAAgV,IAAI,CAAC;AAArV,IAAwV,IAAI,EAAE,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,cAAc,EAAE;AAAhZ,IAAmZ,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,mBAAmB,EAAE,CAAC,EAAE;AAAze,IAA4e,IAAI,CAAC,CAAC;AAAlf,IAAqf,IAAI,CAAC,CAAC;AAA3f,IAA8f,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,SAAS,CAAC;AACphB,IAAM,QAAQ,EAAE,SAAS,OAAO,YAAY,EAAE,QAAQ,GAAG,cAAc,GAAG,SAAS,GAAG,UAAU,EAAE,GAAG,OAAO,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,EAAE,YAAY,GAAG,OAAO,qEAAqE,MAAM,EAAE,GAAG,EAAE,YAAY,GAAG,OAAO,0EAA0E,MAAM,EAAE,GAAG,EAAE,UAAU,EAAE,KAAK,GAAG,YAAY,GAAG,SAAS,EAAE,GAAG,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,GAAG,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,OAAO,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,GAAG,iBAAiB,CAAC,CAAC,GAAG,GAAG,QAAQ,EAAE,CAAC,GAAG,OAAO,CAAC,EAAE,YAAY,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,GAAG,OAAO,CAAC,EAAE,UAAU,EAAE,KAAK,+EAA+E,YAAY,GAAG,SAAS,EAAE,GAAG,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,GAAG,EAAE,OAAO,mFAAmF,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,GAAG,EAAE,YAAY,GAAG,OAAO,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,EAAE,UAAU,EAAE,KAAK,sEAAsE,YAAY,GAAG,SAAS,EAAE,GAAG,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,GAAG,EAAE,OAAO,4DAA4D,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,GAAG,EAAE,YAAY,GAAG,OAAO,CAAC,EAAE,YAAY,CAAC,CAAC,GAAG,OAAO,CAAC,EAAE,UAAU,EAAE,KAAK,0EAA0E,YAAY,GAAG,SAAS,EAAE,GAAG,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,GAAG,EAAE,OAAO,sEAAsE,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,GAAG,EAAE,UAAU,EAAE,KAAK,iEAAiE,YAAY,GAAG,SAAS,EAAE,GAAG,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,GAAG,EAAE,OAAO,yCAAyC,MAAM,EAAE,CAAC,EAAE;AAC9qD,IAAM,UAAU;;;ACAvB,IAAM,QAAQ,IAAI,cAAc;AAAA,EAC5B,MAAM;AAAA,EACN,QAAQ,CAAC,YAAY,UAAU,gBAAgB,SAAS;AAC5D,CAAC;AACM,IAAM,0BAA0B,CAAC,gBAAgB,UAAU,CAAC,MAAM;AACrE,SAAO,MAAM,IAAI,gBAAgB,MAAM,gBAAgB,SAAS;AAAA,IAC5D;AAAA,IACA,QAAQ,QAAQ;AAAA,EACpB,CAAC,CAAC;AACN;AACA,wBAAwB,MAAM;;;ACLvB,IAAM,mBAAmB,CAAC,WAAW;AACxC,SAAO;AAAA,IACH,YAAY;AAAA,IACZ,gBAAe,iCAAQ,kBAAiB;AAAA,IACxC,gBAAe,iCAAQ,kBAAiB;AAAA,IACxC,oBAAmB,iCAAQ,sBAAqB;AAAA,IAChD,mBAAkB,iCAAQ,qBAAoB;AAAA,IAC9C,aAAY,iCAAQ,eAAc,CAAC;AAAA,IACnC,yBAAwB,iCAAQ,2BAA0B;AAAA,IAC1D,kBAAiB,iCAAQ,oBAAmB;AAAA,MACxC;AAAA,QACI,UAAU;AAAA,QACV,kBAAkB,CAAC,QAAQ,IAAI,oBAAoB,gBAAgB;AAAA,QACnE,QAAQ,IAAI,kBAAkB;AAAA,MAClC;AAAA,MACA;AAAA,QACI,UAAU;AAAA,QACV,kBAAkB,CAAC,QAAQ,IAAI,oBAAoB,mBAAmB,MAAM,aAAa,CAAC;AAAA,QAC1F,QAAQ,IAAI,aAAa;AAAA,MAC7B;AAAA,IACJ;AAAA,IACA,SAAQ,iCAAQ,WAAU,IAAI,WAAW;AAAA,IACzC,YAAW,iCAAQ,cAAa;AAAA,IAChC,YAAW,iCAAQ,cAAa;AAAA,IAChC,cAAa,iCAAQ,gBAAe;AAAA,IACpC,cAAa,iCAAQ,gBAAe;AAAA,EACxC;AACJ;;;ACxBO,IAAMA,oBAAmB,CAAC,WAAW;AACxC,QAAM,eAAe,0BAA0B,MAAM;AACrD,QAAM,wBAAwB,MAAM,aAAa,EAAE,KAAK,yBAAyB;AACjF,QAAM,qBAAqB,iBAAuB,MAAM;AACxD,SAAO;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,SAAS;AAAA,IACT;AAAA,IACA,oBAAmB,iCAAQ,sBAAqB;AAAA,IAChD,4BAA2B,iCAAQ,+BAA8B,CAAC,MAAM,MAAM,QAAQ,OAAO,IAAI,MAAM,uBAAuB,CAAC;AAAA,IAC/H,2BAA0B,iCAAQ,6BAC9B,+BAA+B,EAAE,WAAW,mBAAmB,WAAW,eAAe,gBAAY,QAAQ,CAAC;AAAA,IAClH,cAAa,iCAAQ,gBAAe;AAAA,IACpC,SAAQ,iCAAQ,WAAU,gBAAgB,mBAAmB;AAAA,IAC7D,gBAAgB,iBAAe,QAAO,iCAAQ,mBAAkB,qBAAqB;AAAA,IACrF,YAAW,iCAAQ,eAAc,aAAa,MAAM,sBAAsB,GAAG,aAAa;AAAA,IAC1F,SAAQ,iCAAQ,WAAU;AAAA,IAC1B,kBAAiB,iCAAQ,oBAAmB;AAAA,IAC5C,uBAAsB,iCAAQ,0BAAyB,MAAM,QAAQ,QAAQ,8BAA8B;AAAA,IAC3G,kBAAiB,iCAAQ,qBAAoB,MAAM,QAAQ,QAAQ,yBAAyB;AAAA,EAChG;AACJ;;;ACjCO,IAAM,oCAAoC,CAAC,kBAAkB;AAChE,QAAM,mBAAmB,cAAc;AACvC,MAAI,0BAA0B,cAAc;AAC5C,MAAI,eAAe,cAAc;AACjC,SAAO;AAAA,IACH,kBAAkB,gBAAgB;AAC9B,YAAM,QAAQ,iBAAiB,UAAU,CAAC,WAAW,OAAO,aAAa,eAAe,QAAQ;AAChG,UAAI,UAAU,IAAI;AACd,yBAAiB,KAAK,cAAc;AAAA,MACxC,OACK;AACD,yBAAiB,OAAO,OAAO,GAAG,cAAc;AAAA,MACpD;AAAA,IACJ;AAAA,IACA,kBAAkB;AACd,aAAO;AAAA,IACX;AAAA,IACA,0BAA0B,wBAAwB;AAC9C,gCAA0B;AAAA,IAC9B;AAAA,IACA,yBAAyB;AACrB,aAAO;AAAA,IACX;AAAA,IACA,eAAe,aAAa;AACxB,qBAAe;AAAA,IACnB;AAAA,IACA,cAAc;AACV,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;AACO,IAAM,+BAA+B,CAAC,WAAW;AACpD,SAAO;AAAA,IACH,iBAAiB,OAAO,gBAAgB;AAAA,IACxC,wBAAwB,OAAO,uBAAuB;AAAA,IACtD,aAAa,OAAO,YAAY;AAAA,EACpC;AACJ;;;ACjCA,IAAM,YAAY,CAACC,OAAMA;AAClB,IAAM,2BAA2B,CAAC,eAAe,eAAe;AACnE,QAAM,yBAAyB;AAAA,IAC3B,GAAG,UAAU,mCAAmC,aAAa,CAAC;AAAA,IAC9D,GAAG,UAAU,iCAAiC,aAAa,CAAC;AAAA,IAC5D,GAAG,UAAU,qCAAqC,aAAa,CAAC;AAAA,IAChE,GAAG,UAAU,kCAAkC,aAAa,CAAC;AAAA,EACjE;AACA,aAAW,QAAQ,CAAC,cAAc,UAAU,UAAU,sBAAsB,CAAC;AAC7E,SAAO;AAAA,IACH,GAAG;AAAA,IACH,GAAG,uCAAuC,sBAAsB;AAAA,IAChE,GAAG,4BAA4B,sBAAsB;AAAA,IACrD,GAAG,gCAAgC,sBAAsB;AAAA,IACzD,GAAG,6BAA6B,sBAAsB;AAAA,EAC1D;AACJ;;;ACLO,IAAM,wBAAN,cAAoC,OAAS;AAAA,EAChD,eAAe,CAAC,aAAa,GAAG;AAC5B,UAAM,YAAYC,kBAAmB,iBAAiB,CAAC,CAAC;AACxD,UAAM,YAAY,gCAAgC,SAAS;AAC3D,UAAM,YAAY,uBAAuB,SAAS;AAClD,UAAM,YAAY,mBAAmB,SAAS;AAC9C,UAAM,YAAY,oBAAoB,SAAS;AAC/C,UAAM,YAAY,wBAAwB,SAAS;AACnD,UAAM,YAAY,sBAAsB,SAAS;AACjD,UAAM,YAAY,4BAA4B,SAAS;AACvD,UAAM,YAAY,yBAAyB,YAAW,+CAAe,eAAc,CAAC,CAAC;AACrF,UAAM,SAAS;AACf,SAAK,SAAS;AACd,SAAK,gBAAgB,IAAI,mBAAmB,KAAK,MAAM,CAAC;AACxD,SAAK,gBAAgB,IAAI,eAAe,KAAK,MAAM,CAAC;AACpD,SAAK,gBAAgB,IAAI,uBAAuB,KAAK,MAAM,CAAC;AAC5D,SAAK,gBAAgB,IAAI,oBAAoB,KAAK,MAAM,CAAC;AACzD,SAAK,gBAAgB,IAAI,gBAAgB,KAAK,MAAM,CAAC;AACrD,SAAK,gBAAgB,IAAI,4BAA4B,KAAK,MAAM,CAAC;AACjE,SAAK,gBAAgB,IAAI,uCAAuC,KAAK,QAAQ;AAAA,MACzE,kCAAkC;AAAA,MAClC,gCAAgC,OAAO,WAAW,IAAI,8BAA8B;AAAA,QAChF,kBAAkB,OAAO;AAAA,MAC7B,CAAC;AAAA,IACL,CAAC,CAAC;AACF,SAAK,gBAAgB,IAAI,qBAAqB,KAAK,MAAM,CAAC;AAAA,EAC9D;AAAA,EACA,UAAU;AACN,UAAM,QAAQ;AAAA,EAClB;AACJ;;;AC3CO,IAAM,kCAAN,MAAM,yCAAwC,iBAAmB;AAAA,EACpE,YAAY,SAAS;AACjB,UAAM,OAAO;AACb,WAAO,eAAe,MAAM,iCAAgC,SAAS;AAAA,EACzE;AACJ;;;ACDO,IAAM,yBAAN,MAAM,gCAA+B,gCAAgB;AAAA,EACxD,YAAY,MAAM;AACd,UAAM;AAAA,MACF,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,GAAG;AAAA,IACP,CAAC;AACD,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,WAAO,eAAe,MAAM,wBAAuB,SAAS;AAAA,EAChE;AACJ;AACO,IAAM,4BAAN,MAAM,mCAAkC,gCAAgB;AAAA,EAC3D,YAAY,MAAM;AACd,UAAM;AAAA,MACF,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,GAAG;AAAA,IACP,CAAC;AACD,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,WAAO,eAAe,MAAM,2BAA0B,SAAS;AAAA,EACnE;AACJ;AACO,IAAM,yBAAN,MAAM,gCAA+B,gCAAgB;AAAA,EACxD,YAAY,MAAM;AACd,UAAM;AAAA,MACF,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,GAAG;AAAA,IACP,CAAC;AACD,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,WAAO,eAAe,MAAM,wBAAuB,SAAS;AAAA,EAChE;AACJ;AACO,IAAM,yBAAN,MAAM,gCAA+B,gCAAgB;AAAA,EACxD,YAAY,MAAM;AACd,UAAM;AAAA,MACF,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,GAAG;AAAA,IACP,CAAC;AACD,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,WAAO,eAAe,MAAM,wBAAuB,SAAS;AAAA,EAChE;AACJ;AACO,IAAM,4BAAN,MAAM,mCAAkC,gCAAgB;AAAA,EAC3D,YAAY,MAAM;AACd,UAAM;AAAA,MACF,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,GAAG;AAAA,IACP,CAAC;AACD,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,WAAO,eAAe,MAAM,2BAA0B,SAAS;AAAA,EACnE;AACJ;AACO,IAAM,2BAAN,MAAM,kCAAiC,gCAAgB;AAAA,EAC1D,YAAY,MAAM;AACd,UAAM;AAAA,MACF,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,GAAG;AAAA,IACP,CAAC;AACD,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,WAAO,eAAe,MAAM,0BAAyB,SAAS;AAAA,EAClE;AACJ;AAKO,IAAM,4BAAN,MAAM,mCAAkC,gCAAgB;AAAA,EAC3D,YAAY,MAAM;AACd,UAAM;AAAA,MACF,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,GAAG;AAAA,IACP,CAAC;AACD,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,WAAO,eAAe,MAAM,2BAA0B,SAAS;AAAA,EACnE;AACJ;AACO,IAAM,2BAAN,MAAM,kCAAiC,gCAAgB;AAAA,EAC1D,YAAY,MAAM;AACd,UAAM;AAAA,MACF,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,GAAG;AAAA,IACP,CAAC;AACD,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,WAAO,eAAe,MAAM,0BAAyB,SAAS;AAAA,EAClE;AACJ;AACO,IAAM,4CAAN,MAAM,mDAAkD,gCAAgB;AAAA,EAC3E,YAAY,MAAM;AACd,UAAM;AAAA,MACF,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,GAAG;AAAA,IACP,CAAC;AACD,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,WAAO,eAAe,MAAM,2CAA0C,SAAS;AAAA,EACnF;AACJ;AAWO,IAAM,0CAAN,MAAM,iDAAgD,gCAAgB;AAAA,EACzE,YAAY,MAAM;AACd,UAAM;AAAA,MACF,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,GAAG;AAAA,IACP,CAAC;AACD,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,WAAO,eAAe,MAAM,yCAAwC,SAAS;AAAA,EACjF;AACJ;AACO,IAAM,kCAAN,MAAM,yCAAwC,gCAAgB;AAAA,EACjE,YAAY,MAAM;AACd,UAAM;AAAA,MACF,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,GAAG;AAAA,IACP,CAAC;AACD,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,WAAO,eAAe,MAAM,iCAAgC,SAAS;AAAA,EACzE;AACJ;AACO,IAAM,mDAAmD,CAAC,SAAS;AAAA,EACtE,GAAG;AAAA,EACH,GAAI,IAAI,UAAU,EAAE,QAAQ,iBAAiB;AACjD;AACO,IAAM,gCAAgC,CAAC,SAAS;AAAA,EACnD,GAAG;AAAA,EACH,GAAI,IAAI,aAAa,EAAE,WAAW,iBAAiB;AACvD;AACO,IAAM,sDAAsD,CAAC,SAAS;AAAA,EACzE,GAAG;AAAA,EACH,GAAI,IAAI,eAAe,EAAE,aAAa,8BAA8B,IAAI,WAAW,EAAE;AACzF;AACO,IAAM,+BAA+B,CAAC,SAAS;AAAA,EAClD,GAAG;AAAA,EACH,GAAI,IAAI,UAAU,EAAE,QAAQ,iBAAiB;AACjD;AACO,IAAM,wCAAwC,CAAC,SAAS;AAAA,EAC3D,GAAG;AAAA,EACH,GAAI,IAAI,UAAU,EAAE,QAAQ,iBAAiB;AACjD;AACO,IAAM,2CAA2C,CAAC,SAAS;AAAA,EAC9D,GAAG;AAAA,EACH,GAAI,IAAI,SAAS,EAAE,OAAO,iBAAiB;AAC/C;AACO,IAAM,4DAA4D,CAAC,SAAS;AAAA,EAC/E,GAAG;AAAA,EACH,GAAI,IAAI,UAAU,EAAE,QAAQ,iBAAiB;AACjD;AACO,IAAM,+DAA+D,CAAC,SAAS;AAAA,EAClF,GAAG;AAAA,EACH,GAAI,IAAI,SAAS,EAAE,OAAO,iBAAiB;AAC/C;AACO,IAAM,wCAAwC,CAAC,SAAS;AAAA,EAC3D,GAAG;AAAA,EACH,GAAI,IAAI,UAAU,EAAE,QAAQ,iBAAiB;AACjD;;;ACtLO,IAAM,+BAA+B,OAAO,OAAO,YAAY;AAClE,QAAM,UAAU,cAAc,oBAAoB;AAClD,MAAI;AACJ,SAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,SAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AACrE;AACO,IAAM,6BAA6B,OAAO,OAAO,YAAY;AAChE,QAAM,UAAU,cAAc,kBAAkB;AAChD,MAAI;AACJ,SAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,SAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AACrE;AACO,IAAM,+BAA+B,OAAO,OAAO,YAAY;AAClE,QAAM,UAAU,cAAc,oBAAoB;AAClD,MAAI;AACJ,SAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,SAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AACrE;AACO,IAAM,6BAA6B,OAAO,OAAO,YAAY;AAChE,QAAM,UAAU,cAAc,kBAAkB;AAChD,MAAI;AACJ,SAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,SAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AACrE;AACO,IAAM,iCAAiC,OAAO,OAAO,YAAY;AACpE,QAAM,UAAU,cAAc,sBAAsB;AACpD,MAAI;AACJ,SAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,SAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AACrE;AACO,IAAM,sCAAsC,OAAO,OAAO,YAAY;AACzE,QAAM,UAAU,cAAc,2BAA2B;AACzD,MAAI;AACJ,SAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,SAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AACrE;AACO,IAAM,kBAAkB,OAAO,OAAO,YAAY;AACrD,QAAM,UAAU,cAAc,OAAO;AACrC,MAAI;AACJ,SAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,SAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AACrE;AACO,IAAM,iCAAiC,OAAO,OAAO,YAAY;AACpE,QAAM,UAAU,cAAc,sBAAsB;AACpD,MAAI;AACJ,SAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,SAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AACrE;AACO,IAAM,2BAA2B,OAAO,OAAO,YAAY;AAC9D,QAAM,UAAU,cAAc,gBAAgB;AAC9C,MAAI;AACJ,SAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,SAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AACrE;AACO,IAAM,+CAA+C,OAAO,OAAO,YAAY;AAClF,QAAM,UAAU,cAAc,oCAAoC;AAClE,MAAI;AACJ,SAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,SAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AACrE;AACO,IAAM,wCAAwC,OAAO,OAAO,YAAY;AAC3E,QAAM,UAAU,cAAc,6BAA6B;AAC3D,MAAI;AACJ,SAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,SAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AACrE;AACO,IAAM,2BAA2B,OAAO,OAAO,YAAY;AAC9D,QAAM,UAAU,cAAc,gBAAgB;AAC9C,MAAI;AACJ,SAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,SAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AACrE;AACO,IAAM,8BAA8B,OAAO,OAAO,YAAY;AACjE,QAAM,UAAU,cAAc,mBAAmB;AACjD,MAAI;AACJ,SAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,SAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AACrE;AACO,IAAM,gCAAgC,OAAO,OAAO,YAAY;AACnE,QAAM,UAAU,cAAc,qBAAqB;AACnD,MAAI;AACJ,SAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,SAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AACrE;AACO,IAAM,oCAAoC,OAAO,OAAO,YAAY;AACvE,QAAM,UAAU,cAAc,yBAAyB;AACvD,MAAI;AACJ,SAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,SAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AACrE;AACO,IAAM,qCAAqC,OAAO,OAAO,YAAY;AACxE,QAAM,UAAU,cAAc,0BAA0B;AACxD,MAAI;AACJ,SAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,SAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AACrE;AACO,IAAM,iCAAiC,OAAO,OAAO,YAAY;AACpE,QAAM,UAAU,cAAc,sBAAsB;AACpD,MAAI;AACJ,SAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,SAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AACrE;AACO,IAAM,wCAAwC,OAAO,OAAO,YAAY;AAC3E,QAAM,UAAU,cAAc,6BAA6B;AAC3D,MAAI;AACJ,SAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,SAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AACrE;AACO,IAAM,wBAAwB,OAAO,OAAO,YAAY;AAC3D,QAAM,UAAU,cAAc,aAAa;AAC3C,MAAI;AACJ,SAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,SAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AACrE;AACO,IAAM,oCAAoC,OAAO,OAAO,YAAY;AACvE,QAAM,UAAU,cAAc,yBAAyB;AACvD,MAAI;AACJ,SAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,SAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AACrE;AACO,IAAM,2BAA2B,OAAO,OAAO,YAAY;AAC9D,QAAM,UAAU,cAAc,gBAAgB;AAC9C,MAAI;AACJ,SAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,SAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AACrE;AACO,IAAM,0BAA0B,OAAO,OAAO,YAAY;AAC7D,QAAM,UAAU,cAAc,eAAe;AAC7C,MAAI;AACJ,SAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,SAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AACrE;AACO,IAAM,+BAA+B,OAAO,OAAO,YAAY;AAClE,QAAM,UAAU,cAAc,oBAAoB;AAClD,MAAI;AACJ,SAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,SAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AACrE;AACO,IAAM,+BAA+B,OAAO,QAAQ,YAAY;AACnE,MAAI,OAAO,cAAc,KAAK;AAC1B,WAAO,gBAAgB,QAAQ,OAAO;AAAA,EAC1C;AACA,QAAM,OAAO,MAAM,cAAU,OAAO,MAAM,OAAO;AACjD,MAAI,WAAW,CAAC;AAChB,aAAW,MAAM,IAAI;AACrB,QAAM,WAAW;AAAA,IACb,WAAW,oBAAoB,MAAM;AAAA,IACrC,GAAG;AAAA,EACP;AACA,SAAO;AACX;AACO,IAAM,6BAA6B,OAAO,QAAQ,YAAY;AACjE,MAAI,OAAO,cAAc,KAAK;AAC1B,WAAO,gBAAgB,QAAQ,OAAO;AAAA,EAC1C;AACA,QAAM,OAAO,MAAM,cAAU,OAAO,MAAM,OAAO;AACjD,MAAI,WAAW,CAAC;AAChB,aAAW,MAAM,IAAI;AACrB,QAAM,WAAW;AAAA,IACb,WAAW,oBAAoB,MAAM;AAAA,IACrC,GAAG;AAAA,EACP;AACA,SAAO;AACX;AACO,IAAM,+BAA+B,OAAO,QAAQ,YAAY;AACnE,MAAI,OAAO,cAAc,KAAK;AAC1B,WAAO,gBAAgB,QAAQ,OAAO;AAAA,EAC1C;AACA,QAAM,YAAY,OAAO,MAAM,OAAO;AACtC,QAAM,WAAW;AAAA,IACb,WAAW,oBAAoB,MAAM;AAAA,EACzC;AACA,SAAO;AACX;AACO,IAAM,6BAA6B,OAAO,QAAQ,YAAY;AACjE,MAAI,OAAO,cAAc,KAAK;AAC1B,WAAO,gBAAgB,QAAQ,OAAO;AAAA,EAC1C;AACA,QAAM,OAAO,MAAM,cAAU,OAAO,MAAM,OAAO;AACjD,MAAI,WAAW,CAAC;AAChB,aAAW,uBAAuB,MAAM,OAAO;AAC/C,QAAM,WAAW;AAAA,IACb,WAAW,oBAAoB,MAAM;AAAA,IACrC,GAAG;AAAA,EACP;AACA,SAAO;AACX;AACO,IAAM,iCAAiC,OAAO,QAAQ,YAAY;AACrE,MAAI,OAAO,cAAc,KAAK;AAC1B,WAAO,gBAAgB,QAAQ,OAAO;AAAA,EAC1C;AACA,QAAM,OAAO,MAAM,cAAU,OAAO,MAAM,OAAO;AACjD,MAAI,WAAW,CAAC;AAChB,aAAW,MAAM,IAAI;AACrB,QAAM,WAAW;AAAA,IACb,WAAW,oBAAoB,MAAM;AAAA,IACrC,GAAG;AAAA,EACP;AACA,SAAO;AACX;AACO,IAAM,sCAAsC,OAAO,QAAQ,YAAY;AAC1E,MAAI,OAAO,cAAc,KAAK;AAC1B,WAAO,gBAAgB,QAAQ,OAAO;AAAA,EAC1C;AACA,QAAM,OAAO,MAAM,cAAU,OAAO,MAAM,OAAO;AACjD,MAAI,WAAW,CAAC;AAChB,aAAW,qCAAqC,MAAM,OAAO;AAC7D,QAAM,WAAW;AAAA,IACb,WAAW,oBAAoB,MAAM;AAAA,IACrC,GAAG;AAAA,EACP;AACA,SAAO;AACX;AACO,IAAM,kBAAkB,OAAO,QAAQ,YAAY;AACtD,MAAI,OAAO,cAAc,KAAK;AAC1B,WAAO,gBAAgB,QAAQ,OAAO;AAAA,EAC1C;AACA,QAAM,OAAO,MAAM,cAAU,OAAO,MAAM,OAAO;AACjD,MAAI,WAAW,CAAC;AAChB,aAAW,MAAM,IAAI;AACrB,QAAM,WAAW;AAAA,IACb,WAAW,oBAAoB,MAAM;AAAA,IACrC,GAAG;AAAA,EACP;AACA,SAAO;AACX;AACO,IAAM,iCAAiC,OAAO,QAAQ,YAAY;AACrE,MAAI,OAAO,cAAc,KAAK;AAC1B,WAAO,gBAAgB,QAAQ,OAAO;AAAA,EAC1C;AACA,QAAM,OAAO,MAAM,cAAU,OAAO,MAAM,OAAO;AACjD,MAAI,WAAW,CAAC;AAChB,aAAW,MAAM,IAAI;AACrB,QAAM,WAAW;AAAA,IACb,WAAW,oBAAoB,MAAM;AAAA,IACrC,GAAG;AAAA,EACP;AACA,SAAO;AACX;AACO,IAAM,2BAA2B,OAAO,QAAQ,YAAY;AAC/D,MAAI,OAAO,cAAc,KAAK;AAC1B,WAAO,gBAAgB,QAAQ,OAAO;AAAA,EAC1C;AACA,QAAM,OAAO,MAAM,cAAU,OAAO,MAAM,OAAO;AACjD,MAAI,WAAW,CAAC;AAChB,aAAW,MAAM,IAAI;AACrB,QAAM,WAAW;AAAA,IACb,WAAW,oBAAoB,MAAM;AAAA,IACrC,GAAG;AAAA,EACP;AACA,SAAO;AACX;AACO,IAAM,+CAA+C,OAAO,QAAQ,YAAY;AACnF,MAAI,OAAO,cAAc,KAAK;AAC1B,WAAO,gBAAgB,QAAQ,OAAO;AAAA,EAC1C;AACA,QAAM,OAAO,MAAM,cAAU,OAAO,MAAM,OAAO;AACjD,MAAI,WAAW,CAAC;AAChB,aAAW,MAAM,IAAI;AACrB,QAAM,WAAW;AAAA,IACb,WAAW,oBAAoB,MAAM;AAAA,IACrC,GAAG;AAAA,EACP;AACA,SAAO;AACX;AACO,IAAM,wCAAwC,OAAO,QAAQ,YAAY;AAC5E,MAAI,OAAO,cAAc,KAAK;AAC1B,WAAO,gBAAgB,QAAQ,OAAO;AAAA,EAC1C;AACA,QAAM,OAAO,MAAM,cAAU,OAAO,MAAM,OAAO;AACjD,MAAI,WAAW,CAAC;AAChB,aAAW,MAAM,IAAI;AACrB,QAAM,WAAW;AAAA,IACb,WAAW,oBAAoB,MAAM;AAAA,IACrC,GAAG;AAAA,EACP;AACA,SAAO;AACX;AACO,IAAM,2BAA2B,OAAO,QAAQ,YAAY;AAC/D,MAAI,OAAO,cAAc,KAAK;AAC1B,WAAO,gBAAgB,QAAQ,OAAO;AAAA,EAC1C;AACA,QAAM,OAAO,MAAM,cAAU,OAAO,MAAM,OAAO;AACjD,MAAI,WAAW,CAAC;AAChB,aAAW,0BAA0B,MAAM,OAAO;AAClD,QAAM,WAAW;AAAA,IACb,WAAW,oBAAoB,MAAM;AAAA,IACrC,GAAG;AAAA,EACP;AACA,SAAO;AACX;AACO,IAAM,8BAA8B,OAAO,QAAQ,YAAY;AAClE,MAAI,OAAO,cAAc,KAAK;AAC1B,WAAO,gBAAgB,QAAQ,OAAO;AAAA,EAC1C;AACA,QAAM,OAAO,MAAM,cAAU,OAAO,MAAM,OAAO;AACjD,MAAI,WAAW,CAAC;AAChB,aAAW,MAAM,IAAI;AACrB,QAAM,WAAW;AAAA,IACb,WAAW,oBAAoB,MAAM;AAAA,IACrC,GAAG;AAAA,EACP;AACA,SAAO;AACX;AACO,IAAM,gCAAgC,OAAO,QAAQ,YAAY;AACpE,MAAI,OAAO,cAAc,KAAK;AAC1B,WAAO,gBAAgB,QAAQ,OAAO;AAAA,EAC1C;AACA,QAAM,OAAO,MAAM,cAAU,OAAO,MAAM,OAAO;AACjD,MAAI,WAAW,CAAC;AAChB,aAAW,MAAM,IAAI;AACrB,QAAM,WAAW;AAAA,IACb,WAAW,oBAAoB,MAAM;AAAA,IACrC,GAAG;AAAA,EACP;AACA,SAAO;AACX;AACO,IAAM,oCAAoC,OAAO,QAAQ,YAAY;AACxE,MAAI,OAAO,cAAc,KAAK;AAC1B,WAAO,gBAAgB,QAAQ,OAAO;AAAA,EAC1C;AACA,QAAM,OAAO,MAAM,cAAU,OAAO,MAAM,OAAO;AACjD,MAAI,WAAW,CAAC;AAChB,aAAW,MAAM,IAAI;AACrB,QAAM,WAAW;AAAA,IACb,WAAW,oBAAoB,MAAM;AAAA,IACrC,GAAG;AAAA,EACP;AACA,SAAO;AACX;AACO,IAAM,qCAAqC,OAAO,QAAQ,YAAY;AACzE,MAAI,OAAO,cAAc,KAAK;AAC1B,WAAO,gBAAgB,QAAQ,OAAO;AAAA,EAC1C;AACA,QAAM,OAAO,MAAM,cAAU,OAAO,MAAM,OAAO;AACjD,MAAI,WAAW,CAAC;AAChB,aAAW,MAAM,IAAI;AACrB,QAAM,WAAW;AAAA,IACb,WAAW,oBAAoB,MAAM;AAAA,IACrC,GAAG;AAAA,EACP;AACA,SAAO;AACX;AACO,IAAM,iCAAiC,OAAO,QAAQ,YAAY;AACrE,MAAI,OAAO,cAAc,KAAK;AAC1B,WAAO,gBAAgB,QAAQ,OAAO;AAAA,EAC1C;AACA,QAAM,YAAY,OAAO,MAAM,OAAO;AACtC,QAAM,WAAW;AAAA,IACb,WAAW,oBAAoB,MAAM;AAAA,EACzC;AACA,SAAO;AACX;AACO,IAAM,wCAAwC,OAAO,QAAQ,YAAY;AAC5E,MAAI,OAAO,cAAc,KAAK;AAC1B,WAAO,gBAAgB,QAAQ,OAAO;AAAA,EAC1C;AACA,QAAM,OAAO,MAAM,cAAU,OAAO,MAAM,OAAO;AACjD,MAAI,WAAW,CAAC;AAChB,aAAW,MAAM,IAAI;AACrB,QAAM,WAAW;AAAA,IACb,WAAW,oBAAoB,MAAM;AAAA,IACrC,GAAG;AAAA,EACP;AACA,SAAO;AACX;AACO,IAAM,wBAAwB,OAAO,QAAQ,YAAY;AAC5D,MAAI,OAAO,cAAc,KAAK;AAC1B,WAAO,gBAAgB,QAAQ,OAAO;AAAA,EAC1C;AACA,QAAM,OAAO,MAAM,cAAU,OAAO,MAAM,OAAO;AACjD,MAAI,WAAW,CAAC;AAChB,aAAW,MAAM,IAAI;AACrB,QAAM,WAAW;AAAA,IACb,WAAW,oBAAoB,MAAM;AAAA,IACrC,GAAG;AAAA,EACP;AACA,SAAO;AACX;AACO,IAAM,oCAAoC,OAAO,QAAQ,YAAY;AACxE,MAAI,OAAO,cAAc,KAAK;AAC1B,WAAO,gBAAgB,QAAQ,OAAO;AAAA,EAC1C;AACA,QAAM,YAAY,OAAO,MAAM,OAAO;AACtC,QAAM,WAAW;AAAA,IACb,WAAW,oBAAoB,MAAM;AAAA,EACzC;AACA,SAAO;AACX;AACO,IAAM,2BAA2B,OAAO,QAAQ,YAAY;AAC/D,MAAI,OAAO,cAAc,KAAK;AAC1B,WAAO,gBAAgB,QAAQ,OAAO;AAAA,EAC1C;AACA,QAAM,YAAY,OAAO,MAAM,OAAO;AACtC,QAAM,WAAW;AAAA,IACb,WAAW,oBAAoB,MAAM;AAAA,EACzC;AACA,SAAO;AACX;AACO,IAAM,0BAA0B,OAAO,QAAQ,YAAY;AAC9D,MAAI,OAAO,cAAc,KAAK;AAC1B,WAAO,gBAAgB,QAAQ,OAAO;AAAA,EAC1C;AACA,QAAM,OAAO,MAAM,cAAU,OAAO,MAAM,OAAO;AACjD,MAAI,WAAW,CAAC;AAChB,aAAW,MAAM,IAAI;AACrB,QAAM,WAAW;AAAA,IACb,WAAW,oBAAoB,MAAM;AAAA,IACrC,GAAG;AAAA,EACP;AACA,SAAO;AACX;AACO,IAAM,+BAA+B,OAAO,QAAQ,YAAY;AACnE,MAAI,OAAO,cAAc,KAAK;AAC1B,WAAO,gBAAgB,QAAQ,OAAO;AAAA,EAC1C;AACA,QAAM,OAAO,MAAM,cAAU,OAAO,MAAM,OAAO;AACjD,MAAI,WAAW,CAAC;AAChB,aAAW,MAAM,IAAI;AACrB,QAAM,WAAW;AAAA,IACb,WAAW,oBAAoB,MAAM;AAAA,IACrC,GAAG;AAAA,EACP;AACA,SAAO;AACX;AACA,IAAM,kBAAkB,OAAO,QAAQ,YAAY;AAC/C,QAAM,eAAe;AAAA,IACjB,GAAG;AAAA,IACH,MAAM,MAAM,mBAAe,OAAO,MAAM,OAAO;AAAA,EACnD;AACA,QAAM,YAAY,sBAAsB,QAAQ,aAAa,IAAI;AACjE,UAAQ,WAAW;AAAA,IACf,KAAK;AAAA,IACL,KAAK;AACD,YAAM,MAAM,6BAA6B,cAAc,OAAO;AAAA,IAClE,KAAK;AAAA,IACL,KAAK;AACD,YAAM,MAAM,gCAAgC,cAAc,OAAO;AAAA,IACrE,KAAK;AAAA,IACL,KAAK;AACD,YAAM,MAAM,6BAA6B,cAAc,OAAO;AAAA,IAClE,KAAK;AAAA,IACL,KAAK;AACD,YAAM,MAAM,6BAA6B,cAAc,OAAO;AAAA,IAClE,KAAK;AAAA,IACL,KAAK;AACD,YAAM,MAAM,gCAAgC,cAAc,OAAO;AAAA,IACrE,KAAK;AAAA,IACL,KAAK;AACD,YAAM,MAAM,+BAA+B,cAAc,OAAO;AAAA,IACpE,KAAK;AAAA,IACL,KAAK;AACD,YAAM,MAAM,gCAAgC,cAAc,OAAO;AAAA,IACrE,KAAK;AAAA,IACL,KAAK;AACD,YAAM,MAAM,+BAA+B,cAAc,OAAO;AAAA,IACpE,KAAK;AAAA,IACL,KAAK;AACD,YAAM,MAAM,gDAAgD,cAAc,OAAO;AAAA,IACrF,KAAK;AAAA,IACL,KAAK;AACD,YAAM,MAAM,8CAA8C,cAAc,OAAO;AAAA,IACnF,KAAK;AAAA,IACL,KAAK;AACD,YAAM,MAAM,sCAAsC,cAAc,OAAO;AAAA,IAC3E;AACI,YAAM,aAAa,aAAa;AAChC,aAAO,kBAAkB;AAAA,QACrB;AAAA,QACA;AAAA,QACA;AAAA,MACJ,CAAC;AAAA,EACT;AACJ;AACA,IAAM,wCAAwC,OAAO,cAAc,YAAY;AAC3E,QAAM,OAAO,aAAa;AAC1B,QAAM,eAAe,MAAM,IAAI;AAC/B,QAAM,YAAY,IAAI,gCAAgC;AAAA,IAClD,WAAW,oBAAoB,YAAY;AAAA,IAC3C,GAAG;AAAA,EACP,CAAC;AACD,SAAO,yBAA2B,WAAW,IAAI;AACrD;AACA,IAAM,gDAAgD,OAAO,cAAc,YAAY;AACnF,QAAM,OAAO,aAAa;AAC1B,QAAM,eAAe,MAAM,IAAI;AAC/B,QAAM,YAAY,IAAI,wCAAwC;AAAA,IAC1D,WAAW,oBAAoB,YAAY;AAAA,IAC3C,GAAG;AAAA,EACP,CAAC;AACD,SAAO,yBAA2B,WAAW,IAAI;AACrD;AACA,IAAM,iCAAiC,OAAO,cAAc,YAAY;AACpE,QAAM,OAAO,aAAa;AAC1B,QAAM,eAAe,MAAM,IAAI;AAC/B,QAAM,YAAY,IAAI,yBAAyB;AAAA,IAC3C,WAAW,oBAAoB,YAAY;AAAA,IAC3C,GAAG;AAAA,EACP,CAAC;AACD,SAAO,yBAA2B,WAAW,IAAI;AACrD;AACA,IAAM,+BAA+B,OAAO,cAAc,YAAY;AAClE,QAAM,OAAO,aAAa;AAC1B,QAAM,eAAe,MAAM,IAAI;AAC/B,QAAM,YAAY,IAAI,uBAAuB;AAAA,IACzC,WAAW,oBAAoB,YAAY;AAAA,IAC3C,GAAG;AAAA,EACP,CAAC;AACD,SAAO,yBAA2B,WAAW,IAAI;AACrD;AACA,IAAM,kDAAkD,OAAO,cAAc,YAAY;AACrF,QAAM,OAAO,aAAa;AAC1B,QAAM,eAAe,MAAM,IAAI;AAC/B,QAAM,YAAY,IAAI,0CAA0C;AAAA,IAC5D,WAAW,oBAAoB,YAAY;AAAA,IAC3C,GAAG;AAAA,EACP,CAAC;AACD,SAAO,yBAA2B,WAAW,IAAI;AACrD;AACA,IAAM,kCAAkC,OAAO,cAAc,YAAY;AACrE,QAAM,OAAO,aAAa;AAC1B,QAAM,eAAe,MAAM,IAAI;AAC/B,QAAM,YAAY,IAAI,0BAA0B;AAAA,IAC5C,WAAW,oBAAoB,YAAY;AAAA,IAC3C,GAAG;AAAA,EACP,CAAC;AACD,SAAO,yBAA2B,WAAW,IAAI;AACrD;AACA,IAAM,+BAA+B,OAAO,cAAc,YAAY;AAClE,QAAM,OAAO,aAAa;AAC1B,QAAM,eAAe,MAAM,IAAI;AAC/B,QAAM,YAAY,IAAI,uBAAuB;AAAA,IACzC,WAAW,oBAAoB,YAAY;AAAA,IAC3C,GAAG;AAAA,EACP,CAAC;AACD,SAAO,yBAA2B,WAAW,IAAI;AACrD;AACA,IAAM,+BAA+B,OAAO,cAAc,YAAY;AAClE,QAAM,OAAO,aAAa;AAC1B,QAAM,eAAe,MAAM,IAAI;AAC/B,QAAM,YAAY,IAAI,uBAAuB;AAAA,IACzC,WAAW,oBAAoB,YAAY;AAAA,IAC3C,GAAG;AAAA,EACP,CAAC;AACD,SAAO,yBAA2B,WAAW,IAAI;AACrD;AACA,IAAM,kCAAkC,OAAO,cAAc,YAAY;AACrE,QAAM,OAAO,aAAa;AAC1B,QAAM,eAAe,MAAM,IAAI;AAC/B,QAAM,YAAY,IAAI,0BAA0B;AAAA,IAC5C,WAAW,oBAAoB,YAAY;AAAA,IAC3C,GAAG;AAAA,EACP,CAAC;AACD,SAAO,yBAA2B,WAAW,IAAI;AACrD;AACA,IAAM,kCAAkC,OAAO,cAAc,YAAY;AACrE,QAAM,OAAO,aAAa;AAC1B,QAAM,eAAe,MAAM,IAAI;AAC/B,QAAM,YAAY,IAAI,0BAA0B;AAAA,IAC5C,WAAW,oBAAoB,YAAY;AAAA,IAC3C,GAAG;AAAA,EACP,CAAC;AACD,SAAO,yBAA2B,WAAW,IAAI;AACrD;AACA,IAAM,iCAAiC,OAAO,cAAc,YAAY;AACpE,QAAM,OAAO,aAAa;AAC1B,QAAM,eAAe,MAAM,IAAI;AAC/B,QAAM,YAAY,IAAI,yBAAyB;AAAA,IAC3C,WAAW,oBAAoB,YAAY;AAAA,IAC3C,GAAG;AAAA,EACP,CAAC;AACD,SAAO,yBAA2B,WAAW,IAAI;AACrD;AACA,IAAM,iBAAiB,CAAC,QAAQ,YAAY;AACxC,SAAO,KAAK,QAAQ;AAAA,IAChB,aAAa;AAAA,IACb,YAAY,CAAC,MAAM,cAAgB,oBAAsB,aAAe,CAAC,CAAC,CAAC;AAAA,IAC3E,WAAW;AAAA,IACX,cAAc;AAAA,EAClB,CAAC;AACL;AACA,IAAM,uCAAuC,CAAC,QAAQ,YAAY;AAC9D,SAAO,KAAK,QAAQ;AAAA,IAChB,aAAa,CAAC,MAAM,eAAe,GAAG,OAAO;AAAA,IAC7C,YAAY;AAAA,EAChB,CAAC;AACL;AACA,IAAM,oBAAoB,CAAC,QAAQ,YAAY;AAC3C,QAAM,UAAU,UAAU,CAAC,GACtB,OAAO,CAACC,OAAMA,MAAK,IAAI,EACvB,IAAI,CAAC,UAAU;AAChB,WAAO,uBAAuB,OAAO,OAAO;AAAA,EAChD,CAAC;AACD,SAAO;AACX;AACA,IAAM,yBAAyB,CAAC,QAAQ,YAAY;AAChD,SAAO,KAAK,QAAQ;AAAA,IAChB,cAAc,CAAC,MAAM,cAAgB,oBAAsB,aAAe,CAAC,CAAC,CAAC;AAAA,IAC7E,YAAY;AAAA,IACZ,kBAAkB,CAAC,MAAM,cAAgB,oBAAsB,aAAe,CAAC,CAAC,CAAC;AAAA,IACjF,QAAQ;AAAA,EACZ,CAAC;AACL;AACA,IAAM,4BAA4B,CAAC,QAAQ,YAAY;AACnD,SAAO,KAAK,QAAQ;AAAA,IAChB,YAAY,CAAC,MAAM,kBAAkB,GAAG,OAAO;AAAA,IAC/C,gBAAgB;AAAA,IAChB,WAAW;AAAA,EACf,CAAC;AACL;AACA,IAAM,sBAAsB,CAAC,YAAY;AAAA,EACrC,gBAAgB,OAAO;AAAA,EACvB,WAAW,OAAO,QAAQ,kBAAkB,KAAK,OAAO,QAAQ,mBAAmB,KAAK,OAAO,QAAQ,kBAAkB;AAAA,EACzH,mBAAmB,OAAO,QAAQ,YAAY;AAAA,EAC9C,MAAM,OAAO,QAAQ,aAAa;AACtC;AAEA,IAAM,oBAAoB,kBAAkB,+BAAe;AAC3D,IAAM,sBAAsB,OAAO,SAAS,SAAS,MAAM,kBAAkB,SAAS;AAClF,QAAM,EAAE,UAAU,WAAW,SAAS,MAAM,MAAM,SAAS,IAAI,MAAM,QAAQ,SAAS;AACtF,QAAM,WAAW;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,IACR,MAAM,SAAS,SAAS,GAAG,IAAI,SAAS,MAAM,GAAG,EAAE,IAAI,OAAO,WAAW;AAAA,IACzE;AAAA,EACJ;AACA,MAAI,qBAAqB,QAAW;AAChC,aAAS,WAAW;AAAA,EACxB;AACA,MAAI,SAAS,QAAW;AACpB,aAAS,OAAO;AAAA,EACpB;AACA,SAAO,IAAI,YAAc,QAAQ;AACrC;AACA,SAAS,cAAc,WAAW;AAC9B,SAAO;AAAA,IACH,gBAAgB;AAAA,IAChB,gBAAgB,6BAA6B,SAAS;AAAA,EAC1D;AACJ;;;AChoBO,IAAM,4BAAN,cAAwC,QAC1C,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUC,UAAS,IAAI,QAAQC,IAAG;AACrC,SAAO;AAAA,IACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,IACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,EACxE;AACJ,CAAC,EACI,EAAE,6BAA6B,sBAAsB,CAAC,CAAC,EACvD,EAAE,yBAAyB,2BAA2B,EACtD,EAAE,QAAQ,MAAM,EAChB,IAAI,4BAA4B,EAChC,GAAG,4BAA4B,EAC/B,MAAM,EAAE;AACb;;;ACfO,IAAM,0BAAN,cAAsC,QACxC,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUE,UAAS,IAAI,QAAQC,IAAG;AACrC,SAAO;AAAA,IACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,IACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,EACxE;AACJ,CAAC,EACI,EAAE,6BAA6B,oBAAoB,CAAC,CAAC,EACrD,EAAE,yBAAyB,yBAAyB,EACpD,EAAE,QAAQ,MAAM,EAChB,IAAI,0BAA0B,EAC9B,GAAG,0BAA0B,EAC7B,MAAM,EAAE;AACb;;;ACfO,IAAM,4BAAN,cAAwC,QAC1C,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUE,UAAS,IAAI,QAAQC,IAAG;AACrC,SAAO;AAAA,IACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,IACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,EACxE;AACJ,CAAC,EACI,EAAE,6BAA6B,sBAAsB,CAAC,CAAC,EACvD,EAAE,yBAAyB,2BAA2B,EACtD,EAAE,QAAQ,MAAM,EAChB,IAAI,4BAA4B,EAChC,GAAG,4BAA4B,EAC/B,MAAM,EAAE;AACb;;;ACfO,IAAM,0BAAN,cAAsC,QACxC,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUE,UAAS,IAAI,QAAQC,IAAG;AACrC,SAAO;AAAA,IACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,IACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,EACxE;AACJ,CAAC,EACI,EAAE,6BAA6B,oBAAoB,CAAC,CAAC,EACrD,EAAE,yBAAyB,yBAAyB,EACpD,EAAE,QAAQ,MAAM,EAChB,IAAI,0BAA0B,EAC9B,GAAG,0BAA0B,EAC7B,MAAM,EAAE;AACb;;;ACfO,IAAM,8BAAN,cAA0C,QAC5C,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUE,UAAS,IAAI,QAAQC,IAAG;AACrC,SAAO;AAAA,IACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,IACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,EACxE;AACJ,CAAC,EACI,EAAE,6BAA6B,wBAAwB,CAAC,CAAC,EACzD,EAAE,yBAAyB,6BAA6B,EACxD,EAAE,QAAQ,MAAM,EAChB,IAAI,8BAA8B,EAClC,GAAG,8BAA8B,EACjC,MAAM,EAAE;AACb;;;ACdO,IAAM,mCAAN,cAA+C,QACjD,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUE,UAAS,IAAI,QAAQC,IAAG;AACrC,SAAO;AAAA,IACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,IACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,EACxE;AACJ,CAAC,EACI,EAAE,6BAA6B,6BAA6B,CAAC,CAAC,EAC9D,EAAE,yBAAyB,kCAAkC,EAC7D,EAAE,kDAAkD,mDAAmD,EACvG,IAAI,mCAAmC,EACvC,GAAG,mCAAmC,EACtC,MAAM,EAAE;AACb;;;ACfO,IAAM,eAAN,cAA2B,QAC7B,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUE,UAAS,IAAI,QAAQC,IAAG;AACrC,SAAO;AAAA,IACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,IACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,EACxE;AACJ,CAAC,EACI,EAAE,6BAA6B,SAAS,CAAC,CAAC,EAC1C,EAAE,yBAAyB,cAAc,EACzC,EAAE,8BAA8B,MAAM,EACtC,IAAI,eAAe,EACnB,GAAG,eAAe,EAClB,MAAM,EAAE;AACb;;;AChBO,IAAM,8BAAN,cAA0C,QAC5C,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUE,UAAS,IAAI,QAAQC,IAAG;AACrC,SAAO;AAAA,IACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,IACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,EACxE;AACJ,CAAC,EACI,EAAE,6BAA6B,wBAAwB,CAAC,CAAC,EACzD,EAAE,yBAAyB,6BAA6B,EACxD,EAAE,QAAQ,MAAM,EAChB,IAAI,8BAA8B,EAClC,GAAG,8BAA8B,EACjC,MAAM,EAAE;AACb;;;ACdO,IAAM,wBAAN,cAAoC,QACtC,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUE,UAAS,IAAI,QAAQC,IAAG;AACrC,SAAO;AAAA,IACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,IACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,EACxE;AACJ,CAAC,EACI,EAAE,6BAA6B,kBAAkB,CAAC,CAAC,EACnD,EAAE,yBAAyB,uBAAuB,EAClD,EAAE,uCAAuC,wCAAwC,EACjF,IAAI,wBAAwB,EAC5B,GAAG,wBAAwB,EAC3B,MAAM,EAAE;AACb;;;ACfO,IAAM,4CAAN,cAAwD,QAC1D,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUE,UAAS,IAAI,QAAQC,IAAG;AACrC,SAAO;AAAA,IACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,IACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,EACxE;AACJ,CAAC,EACI,EAAE,6BAA6B,sCAAsC,CAAC,CAAC,EACvE,EAAE,yBAAyB,2CAA2C,EACtE,EAAE,2DAA2D,4DAA4D,EACzH,IAAI,4CAA4C,EAChD,GAAG,4CAA4C,EAC/C,MAAM,EAAE;AACb;;;AChBO,IAAM,qCAAN,cAAiD,QACnD,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUE,UAAS,IAAI,QAAQC,IAAG;AACrC,SAAO;AAAA,IACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,IACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,EACxE;AACJ,CAAC,EACI,EAAE,6BAA6B,+BAA+B,CAAC,CAAC,EAChE,EAAE,yBAAyB,oCAAoC,EAC/D,EAAE,QAAQ,MAAM,EAChB,IAAI,qCAAqC,EACzC,GAAG,qCAAqC,EACxC,MAAM,EAAE;AACb;;;ACfO,IAAM,wBAAN,cAAoC,QACtC,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUE,UAAS,IAAI,QAAQC,IAAG;AACrC,SAAO;AAAA,IACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,IACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,EACxE;AACJ,CAAC,EACI,EAAE,6BAA6B,kBAAkB,CAAC,CAAC,EACnD,EAAE,yBAAyB,uBAAuB,EAClD,EAAE,QAAQ,MAAM,EAChB,IAAI,wBAAwB,EAC5B,GAAG,wBAAwB,EAC3B,MAAM,EAAE;AACb;;;ACfO,IAAM,2BAAN,cAAuC,QACzC,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUE,UAAS,IAAI,QAAQC,IAAG;AACrC,SAAO;AAAA,IACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,IACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,EACxE;AACJ,CAAC,EACI,EAAE,6BAA6B,qBAAqB,CAAC,CAAC,EACtD,EAAE,yBAAyB,0BAA0B,EACrD,EAAE,QAAQ,MAAM,EAChB,IAAI,2BAA2B,EAC/B,GAAG,2BAA2B,EAC9B,MAAM,EAAE;AACb;;;ACfO,IAAM,6BAAN,cAAyC,QAC3C,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUE,UAAS,IAAI,QAAQC,IAAG;AACrC,SAAO;AAAA,IACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,IACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,EACxE;AACJ,CAAC,EACI,EAAE,6BAA6B,uBAAuB,CAAC,CAAC,EACxD,EAAE,yBAAyB,4BAA4B,EACvD,EAAE,QAAQ,MAAM,EAChB,IAAI,6BAA6B,EACjC,GAAG,6BAA6B,EAChC,MAAM,EAAE;AACb;;;ACfO,IAAM,iCAAN,cAA6C,QAC/C,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUE,UAAS,IAAI,QAAQC,IAAG;AACrC,SAAO;AAAA,IACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,IACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,EACxE;AACJ,CAAC,EACI,EAAE,6BAA6B,2BAA2B,CAAC,CAAC,EAC5D,EAAE,yBAAyB,gCAAgC,EAC3D,EAAE,QAAQ,MAAM,EAChB,IAAI,iCAAiC,EACrC,GAAG,iCAAiC,EACpC,MAAM,EAAE;AACb;;;ACfO,IAAM,kCAAN,cAA8C,QAChD,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUE,UAAS,IAAI,QAAQC,IAAG;AACrC,SAAO;AAAA,IACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,IACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,EACxE;AACJ,CAAC,EACI,EAAE,6BAA6B,4BAA4B,CAAC,CAAC,EAC7D,EAAE,yBAAyB,iCAAiC,EAC5D,EAAE,QAAQ,MAAM,EAChB,IAAI,kCAAkC,EACtC,GAAG,kCAAkC,EACrC,MAAM,EAAE;AACb;;;ACfO,IAAM,8BAAN,cAA0C,QAC5C,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUE,UAAS,IAAI,QAAQC,IAAG;AACrC,SAAO;AAAA,IACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,IACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,EACxE;AACJ,CAAC,EACI,EAAE,6BAA6B,wBAAwB,CAAC,CAAC,EACzD,EAAE,yBAAyB,6BAA6B,EACxD,EAAE,QAAQ,MAAM,EAChB,IAAI,8BAA8B,EAClC,GAAG,8BAA8B,EACjC,MAAM,EAAE;AACb;;;ACfO,IAAM,qCAAN,cAAiD,QACnD,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUE,UAAS,IAAI,QAAQC,IAAG;AACrC,SAAO;AAAA,IACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,IACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,EACxE;AACJ,CAAC,EACI,EAAE,6BAA6B,+BAA+B,CAAC,CAAC,EAChE,EAAE,yBAAyB,oCAAoC,EAC/D,EAAE,QAAQ,MAAM,EAChB,IAAI,qCAAqC,EACzC,GAAG,qCAAqC,EACxC,MAAM,EAAE;AACb;;;ACfO,IAAM,qBAAN,cAAiC,QACnC,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUE,UAAS,IAAI,QAAQC,IAAG;AACrC,SAAO;AAAA,IACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,IACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,EACxE;AACJ,CAAC,EACI,EAAE,6BAA6B,eAAe,CAAC,CAAC,EAChD,EAAE,yBAAyB,oBAAoB,EAC/C,EAAE,QAAQ,MAAM,EAChB,IAAI,qBAAqB,EACzB,GAAG,qBAAqB,EACxB,MAAM,EAAE;AACb;;;ACfO,IAAM,iCAAN,cAA6C,QAC/C,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUE,UAAS,IAAI,QAAQC,IAAG;AACrC,SAAO;AAAA,IACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,IACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,EACxE;AACJ,CAAC,EACI,EAAE,6BAA6B,2BAA2B,CAAC,CAAC,EAC5D,EAAE,yBAAyB,gCAAgC,EAC3D,EAAE,QAAQ,MAAM,EAChB,IAAI,iCAAiC,EACrC,GAAG,iCAAiC,EACpC,MAAM,EAAE;AACb;;;ACdO,IAAM,wBAAN,cAAoC,QACtC,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUE,UAAS,IAAI,QAAQC,IAAG;AACrC,SAAO;AAAA,IACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,IACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,EACxE;AACJ,CAAC,EACI,EAAE,6BAA6B,kBAAkB,CAAC,CAAC,EACnD,EAAE,yBAAyB,uBAAuB,EAClD,EAAE,uCAAuC,MAAM,EAC/C,IAAI,wBAAwB,EAC5B,GAAG,wBAAwB,EAC3B,MAAM,EAAE;AACb;;;AChBO,IAAM,uBAAN,cAAmC,QACrC,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUE,UAAS,IAAI,QAAQC,IAAG;AACrC,SAAO;AAAA,IACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,IACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,EACxE;AACJ,CAAC,EACI,EAAE,6BAA6B,iBAAiB,CAAC,CAAC,EAClD,EAAE,yBAAyB,sBAAsB,EACjD,EAAE,QAAQ,MAAM,EAChB,IAAI,uBAAuB,EAC3B,GAAG,uBAAuB,EAC1B,MAAM,EAAE;AACb;;;ACfO,IAAM,4BAAN,cAAwC,QAC1C,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUE,UAAS,IAAI,QAAQC,IAAG;AACrC,SAAO;AAAA,IACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,IACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,EACxE;AACJ,CAAC,EACI,EAAE,6BAA6B,sBAAsB,CAAC,CAAC,EACvD,EAAE,yBAAyB,2BAA2B,EACtD,EAAE,QAAQ,MAAM,EAChB,IAAI,4BAA4B,EAChC,GAAG,4BAA4B,EAC/B,MAAM,EAAE;AACb;;;ACIA,IAAM,WAAW;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AACO,IAAM,kBAAN,cAA8B,sBAAsB;AAC3D;AACA,uBAAuB,UAAU,eAAe;;;ACjDzC,IAAM,4BAA4B,gBAAgB,uBAAuB,0BAA0B,aAAa,aAAa,YAAY;", "names": ["getRuntimeConfig", "t", "getRuntimeConfig", "e", "Command", "o", "Command", "o", "Command", "o", "Command", "o", "Command", "o", "Command", "o", "Command", "o", "Command", "o", "Command", "o", "Command", "o", "Command", "o", "Command", "o", "Command", "o", "Command", "o", "Command", "o", "Command", "o", "Command", "o", "Command", "o", "Command", "o", "Command", "o", "Command", "o", "Command", "o", "Command", "o"]}