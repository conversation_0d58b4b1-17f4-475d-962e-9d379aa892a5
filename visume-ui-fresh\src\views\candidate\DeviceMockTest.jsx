import React, { useEffect, useState, useRef, useCallback } from "react";
import { useNavigate, useParams } from "react-router-dom";
import toast from "react-hot-toast";

// Components
import DeviceTestingSection from "./components/DeviceTestingSection";
import TermsAndConditionsSection from "./components/TermsAndConditionsSection";
import InterviewSection from "./components/InterviewSection";
import EndInterviewSection from "./components/EndInterviewSection";
import ReviewMockInterview from "./components/ReviewMockInterview";
import Loader from "components/Loader";

// Custom Hooks
import { useMediaStreams } from "./hooks/useMediaStreams";
import { useRecording } from "./hooks/useRecording";
import { useQuestions } from "./hooks/useQuestions";

export default function DeviceMockTest() {
  const navigate = useNavigate();
  const { vpid } = useParams();

  const { localCamStream, startWebcam, stopAllStreams } = useMediaStreams();
  const { startRecording, stopRecording, recordedChunks } = useRecording(localCamStream);
  const {
    questions,
    currentIndex,
    nextQuestion,
    startAnswering,
    updateAnswer,
    error: questionError,
    isLoading: questionsLoading,
    initializeQuestions // Add this new function
  } = useQuestions();

  const [currentSection, setCurrentSection] = useState("loading");
  const [isLoading, setIsLoading] = useState(false);
  const [loadingText, setLoadingText] = useState("Loading...");

  useEffect(() => {
    if (currentSection === "interview" && (!questions || questions.length === 0)) {
      console.error("No questions available for interview");
      toast.error("Failed to load interview questions");
      setCurrentSection("deviceTesting");
    }
  }, [currentSection, questions]);

  // Removed unused localStorage effect since useQuestions handles it

  const [score, setScore] = useState(undefined);
  const [videoUrl, setVideoUrl] = useState("");
  const [status, setStatus] = useState("inactive");

  const [isCompleted, setIsCompleted] = useState(false);
  const [recordingFinished, setRecordingFinished] = useState(false);
  const isRecordingComplete = useRef(false);
  const recordingTimeout = useRef(null);

  useEffect(() => {
    return () => {
      if (recordingTimeout.current) clearTimeout(recordingTimeout.current);
    };
  }, []);

  const handleStartInterview = () => {
    if (localCamStream) {
      setCurrentSection("termsAndConditions");
    } else {
      toast.error("Please allow access to your webcam and microphone");
    }
  };

  const handleFullScreen = () => {
    const element = document.documentElement;
    if (element.requestFullscreen) element.requestFullscreen();
    else if (element.mozRequestFullScreen) element.mozRequestFullScreen();
    else if (element.webkitRequestFullscreen) element.webkitRequestFullscreen();
    else if (element.msRequestFullscreen) element.msRequestFullscreen();
  };

  const handleAcceptTerms = async () => {
    try {
      setIsCompleted(false);
      setRecordingFinished(false);
      isRecordingComplete.current = false;
      setIsLoading(true);

      if (!questions || questions.length === 0) {
        await fetchData();
        if (!questions || questions.length === 0) throw new Error("Failed to load interview questions");
      }

      await startRecording();
      await handleFullScreen();
      setCurrentSection("interview");
    } catch (error) {
      toast.error(error.message || "Failed to start interview");
      setCurrentSection("deviceTesting");
    } finally {
      setIsLoading(false);
    }
  };

  const exitFullScreen = async () => {
    try {
      const isFullscreen = document.fullscreenElement || document.webkitFullscreenElement || document.mozFullScreenElement || document.msFullscreenElement;
      if (!isFullscreen) return;

      await new Promise((resolve, reject) => {
        const onFullscreenChange = () => {
          document.removeEventListener('fullscreenchange', onFullscreenChange);
          resolve();
        };
        document.addEventListener('fullscreenchange', onFullscreenChange);
        if (document.exitFullscreen) document.exitFullscreen();
        else if (document.mozCancelFullScreen) document.mozCancelFullScreen();
        else if (document.webkitExitFullscreen) document.webkitExitFullscreen();
        else if (document.msExitFullscreen) document.msExitFullscreen();
        else reject(new Error('No fullscreen exit method'));
      });
    } catch (error) {
      console.warn("Error exiting fullscreen:", error.message);
    }
  };

  const handleEndInterview = async () => {
    if (isLoading) return;
    try {
      setIsLoading(true);
      setLoadingText("Finalizing recording...");
      window.speechSynthesis.cancel();
      await stopAllStreams();
      await exitFullScreen();

      if (!recordedChunks?.length) throw new Error("No recording data available");

      // Split video data into chunks to avoid localStorage quota issues
      // Store video in IndexedDB
      const videoBlob = new Blob(recordedChunks, { type: "video/webm" });
      const dbName = "mockInterviewDB";
      const storeName = "videoStorage";
      
      await new Promise((resolve, reject) => {
        const request = indexedDB.open(dbName, 1);
        
        request.onerror = () => reject(new Error("Failed to open IndexedDB"));
        
        request.onupgradeneeded = (event) => {
          const db = event.target.result;
          if (!db.objectStoreNames.contains(storeName)) {
            db.createObjectStore(storeName);
          }
        };
        
        request.onsuccess = (event) => {
          const db = event.target.result;
          const transaction = db.transaction([storeName], "readwrite");
          const store = transaction.objectStore(storeName);
          
          const storeReq = store.put(videoBlob, `video_${vpid}`);
          
          storeReq.onsuccess = () => resolve();
          storeReq.onerror = () => reject(new Error("Failed to store video"));
        };
      });

      // Get signed S3 URL
      const fileName = `mock_interview_${vpid}_${Date.now()}.webm`;
      const s3UrlResponse = await fetch(`${import.meta.env.VITE_APP_HOST}/api/v1/get-s3-url/${fileName}?contentType=video/webm`);
      if (!s3UrlResponse.ok) throw new Error("Failed to get upload URL");
      const { url: uploadUrl } = await s3UrlResponse.json();

      // Upload video to S3
      const uploadResponse = await fetch(uploadUrl, {
        method: 'PUT',
        body: videoBlob,
        headers: { 'Content-Type': 'video/webm' }
      });
      if (!uploadResponse.ok) throw new Error("Failed to upload video");

      // Get final video URL by removing query parameters
      const videoUrl = uploadUrl.split('?')[0];
      setVideoUrl(videoUrl);

      // Ensure we use the latest questions from state
      console.log("Current questions state:", questions);

      // Format questions for final evaluation
      const evaluation = questions.map((q, index) => {
        const answer = q.answer;
        console.log("Processing answer for evaluation:", {
          question: q.question,
          originalAnswer: answer,
          type: q.type
        });

        return {
          Question: q.question,
          Your_Answer: answer,  // Keep original answer without modification
          Expected_Answer: q.follow_up || "",
          type: q.type || 'technical'
        };
      });

      // Process questions for scoring API
// Process questions directly from state
const processedQuestions = questions.map(q => ({
  ...q,
  answer: q.answer,
  type: String(q.type || 'general').toLowerCase(),
  keywords: Array.isArray(q.keywords) ? q.keywords : []
}));


      let scoreData = {
        score: {
          Skill_Score: 0,
          Communication_Score: 0,
          Overall_Score: 0
        },
        evaluation: evaluation,  // Store evaluation with original answers
        Suggestions: ""
      };

      console.log("Initial evaluation data:", evaluation);

      setLoadingText("Generating score...");
      
      try {
        // Score calculation with multiple retries
        let retryCount = 0;
        const maxRetries = 3;
        
        while (retryCount < maxRetries) {
          try {
            const scoreResponse = await fetch(`${import.meta.env.VITE_APP_HOST}/api/v1/generate-mock-score`, {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({ InterviewObject: processedQuestions })
            });

            if (!scoreResponse.ok) {
              throw new Error(`Score API error: ${scoreResponse.status}`);
            }

            const contentType = scoreResponse.headers.get("content-type");
            if (!contentType || !contentType.includes("application/json")) {
              throw new Error("Invalid response format from scoring API");
            }

            scoreData = await scoreResponse.json();
            break; // Success, exit retry loop
          } catch (scoreErr) {
            retryCount++;
            console.error(`Score API attempt ${retryCount} failed:`, scoreErr);
            
            if (retryCount === maxRetries) {
              console.error("All score API retries failed, using default score");
              scoreData = {
                score: {
                  Skill_Score: 5,
                  Communication_Score: 5,
                  Overall_Score: 5
                },
                Suggestions: "Score calculation unavailable - using default scores"
              };
              toast.error("Using default scores - scoring service unavailable");
            } else {
              await new Promise(resolve => setTimeout(resolve, 1000 * retryCount)); // Exponential backoff
            }
          }
        }
      } catch (err) {
        scoreData = {
          score: {
            Skill_Score: 5,
            Communication_Score: 5,
            Overall_Score: 5
          },
          Suggestions: `Error: ${err.message}`
        };
        toast.error("Default score used due to error");
      }

      setScore(scoreData);
      // Check for valid answers with logging
      console.log("Checking answers before status update:", processedQuestions.map(q => ({
        question: q.question,
        answer: q.answer
      })));

      const interviewStatus = processedQuestions.some(q =>
        q.answer !== null && q.answer !== undefined &&
        (typeof q.answer !== 'string' || q.answer.trim() !== "")
      ) ? "active" : "inactive";

      console.log("Final interview status:", interviewStatus);

      localStorage.setItem("questions", JSON.stringify(processedQuestions));
      localStorage.setItem(`ReviewMockProfile${vpid}`, JSON.stringify({
        score: scoreData,
        status: interviewStatus,
        timestamp: new Date().toISOString(),
        videoUrl: videoUrl
      }));

      setLoadingText("Uploading interview data...");
      const updateRes = await fetch(`${import.meta.env.VITE_APP_HOST}/api/v1/update-mock-resume`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          mock_interview_id: vpid,
          score: scoreData,
          questions: processedQuestions,
          status: interviewStatus,
          videoUrl: videoUrl
        })
      });

      const updateJson = await updateRes.json();
      if (!updateRes.ok) throw new Error(updateJson.message);

      toast.success("Interview completed and saved");
      setCurrentSection("reviewVideoProfile");
    } catch (error) {
      toast.error(error.message || "Interview saving failed");
    } finally {
      setIsLoading(false);
      localStorage.removeItem("formData");
    }
  };

  const finishVideoProfile = async () => {
    try {
      // Check for valid answers in questions
      const questions = JSON.parse(localStorage.getItem("questions") || "[]");
      const hasValidAnswers = questions.some(q => {
        if (!q.answer) return false;
        if (typeof q.answer !== 'string') return true;
        return q.answer.trim() !== "";
      });

      // Extract and validate score
      const scoreObj = typeof score === 'string' ? JSON.parse(score) : score;
      const overallScore = scoreObj?.score?.Overall_Score || 0;

      const formData = {
        mock_interview_id: vpid,
        score: overallScore,
        status: hasValidAnswers ? "active" : "inactive"
      };

      // Define the API URL
      const Finish_videoprofile_url = `${
        import.meta.env.VITE_APP_HOST
      }/api/v1/finish-mock-resume`;

      // Make an API call using fetch
      const response = await fetch(Finish_videoprofile_url, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json", // To Pass JSON if formData is a plain object
        },
        body: JSON.stringify(formData), // Send formData as JSON
      });
      const data = await response.json();
      console.log(data);
      setCurrentSection("endInterview");
      localStorage.removeItem(`questions`);
      localStorage.removeItem(`ReviewVideoProfile${vpid}`);
    } catch (error) {
      setIsLoading(false);
      // Handle any errors that occurred during the fetch
      console.error(
        "Error occurred during interview ending:",
        error.message || error
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Recording handlers
  
  const handleStopRecording = useCallback(async () => {
    // Prevent multiple stop attempts
    if (isRecordingComplete.current) {
      console.log("Recording already stopping/stopped");
      return Promise.resolve();
    }

    // Only stop if we're at the end of the interview
    if (!isCompleted) {
      console.log("Not stopping recording - interview not completed");
      return Promise.resolve();
    }

    console.log("Initiating recording stop...");
    if (recordingTimeout.current) {
      clearTimeout(recordingTimeout.current);
    }
    
    setLoadingText("Finishing interview recording...");
    return new Promise(async (resolve, reject) => {
      try {
        // Set flag and stop recording
        isRecordingComplete.current = true;
        await stopRecording();
        resolve();
      } catch (error) {
        console.error("Error stopping recording:", error);
        isRecordingComplete.current = false; // Reset flag on error
        reject(error);
      }
    });
  }, [stopRecording, recordedChunks, setRecordingFinished]);

  const hasRun = useRef(false);
  // Monitor recording completion
useEffect(() => {
  if (
    isRecordingComplete.current &&
    recordedChunks.length > 0 &&
    !hasRun.current &&
    isCompleted
  ) {
    hasRun.current = true;
    setTimeout(() => {
      setRecordingFinished(true);
      handleEndInterview();
    }, 150); // Delay by 150ms
  }
}, [recordedChunks, isCompleted, handleEndInterview]);


  // Reset recording state
  // Reset recording state when section changes
  useEffect(() => {
    isRecordingComplete.current = false;
    if (recordingTimeout.current) {
      clearTimeout(recordingTimeout.current);
    }
    return () => {
      if (recordingTimeout.current) {
        clearTimeout(recordingTimeout.current);
      }
    };
  }, [currentSection]);

  const fetchData = async () => {
    setIsLoading(true);
    try {
      // First try to restore from review profile
      const reviewData = localStorage.getItem(`ReviewMockProfile${vpid}`);
      if (reviewData) {
        const parsedReview = JSON.parse(reviewData);
        setScore(parsedReview.score);
        setVideoUrl(parsedReview.videoUrl);
        setStatus(parsedReview.status || "inactive");
        setCurrentSection("reviewVideoProfile");
        return;
      }

      // Otherwise fetch new data
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/mock-resume-data/${vpid}`,
        { method: "GET" }
      );

      if (!response.ok) {
        throw new Error("Failed to fetch interview data");
      }

      const { data } = await response.json();
      if (!data) {
        throw new Error("Invalid response format");
      }

      // If we have existing score/data, show review
      if (data.score) {
        setScore(JSON.parse(data.score));
        setStatus(data.status || "inactive");
        setCurrentSection("reviewVideoProfile");
        return;
      }

      // Initialize new interview questions
      let questions;
      try {
        questions = JSON.parse(data.questions || "[]");
        if (!Array.isArray(questions) || questions.length === 0) {
          throw new Error("No valid questions found");
        }
      } catch (error) {
        console.error("Error parsing questions:", error);
        throw new Error("Invalid questions data");
      }

      // Initialize through useQuestions hook
      const initialized = await initializeQuestions(questions);
      if (!initialized) {
        throw new Error("Failed to initialize questions");
      }

      setCurrentSection("deviceTesting");

    } catch (error) {
      console.error("Error fetching data:", error);
      toast.error(error.message || "Failed to load interview");
      setStatus("inactive");
    } finally {
      setIsLoading(false);
    }
  };
  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    const handleBeforeUnload = (event) => {
      if (currentSection === "interview") {
        event.preventDefault();
        event.returnValue =
          "You can't refresh while taking Interview. Otherwise you may loose your progress"; // Some browsers require this for custom messages
        return "You can't refresh while taking Interview. Otherwise you may loose your progress"; // This is necessary for most modern browsers
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);

    // Cleanup event listener when the component unmounts or interview ends
    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, [currentSection]);

  return (
    <div className="min-w-screen flex min-h-screen items-center justify-center bg-[#F4F7FE] text-gray-900">
      {/* Loader */}
      {(isLoading || currentSection === "loading") && (
        <Loader text={loadingText} />
      )}
      {currentSection === "deviceTesting" && (
        <DeviceTestingSection
          localCamStream={localCamStream}
          startWebcam={startWebcam}
          onStartInterview={handleStartInterview}
        />
      )}
      {currentSection === "termsAndConditions" && (
        <TermsAndConditionsSection onAccept={handleAcceptTerms} />
      )}
      {currentSection === "interview" && questions && questions.length > 0 && (
        <InterviewSection
          localCamStream={localCamStream}
          questions={questions}
          currentQuestion={questions[currentIndex]}
          currentIndex={currentIndex}
          onNextQuestion={async () => {
            try {
              setIsLoading(true);
              setLoadingText("Processing answer...");
              
              await nextQuestion();
              setLoadingText("Loading next question...");

            } catch (error) {
              console.error("Error handling next question:", error);
              toast.error("Error loading question");
            } finally {
              setIsLoading(false);
            }
          }}
          onEndInterview={handleEndInterview}
          error={questionError}
          isLoading={isLoading || questionsLoading}
          startAnswering={startAnswering}
          updateAnswer={updateAnswer} // Pass updateAnswer function
        />
      )}
      {currentSection === "interview" && (!questions || questions.length === 0) && (
        <div className="flex h-screen items-center justify-center">
          <div className="max-w-md rounded-lg bg-red-50 p-6 text-center">
            <h2 className="mb-4 text-xl font-semibold text-red-700">Interview Setup Error</h2>
            <p className="text-red-600">Failed to load interview questions</p>
            <div className="mt-6 flex justify-center space-x-4">
              <button
                onClick={() => window.location.reload()}
                className="rounded-md bg-red-600 px-4 py-2 text-white hover:bg-red-700"
              >
                Retry
              </button>
            </div>
          </div>
        </div>
      )}
      {currentSection === "reviewVideoProfile" && (
        <ReviewMockInterview
          status={status}
          finishVideoProfile={finishVideoProfile}
          videoUrl={videoUrl || (async () => {
            try {
              const dbName = "mockInterviewDB";
              const storeName = "videoStorage";
              
              const db = await new Promise((resolve, reject) => {
                const request = indexedDB.open(dbName, 1);
                request.onerror = () => reject(new Error("Failed to open IndexedDB"));
                request.onsuccess = () => resolve(request.result);
              });
              
              const transaction = db.transaction([storeName], "readonly");
              const store = transaction.objectStore(storeName);
              const videoBlob = await new Promise((resolve, reject) => {
                const request = store.get(`video_${vpid}`);
                request.onerror = () => reject(new Error("Failed to get video"));
                request.onsuccess = () => resolve(request.result);
              });
              
              return URL.createObjectURL(videoBlob);
            } catch(e) {
              console.error('Failed to get video from IndexedDB', e);
              return null;
            }
          })()}
          score={score}
          onRetry={() => {
            setCurrentSection("interview");
            setIsCompleted(false);
            setRecordingFinished(false);
            localStorage.removeItem(`ReviewMockProfile${vpid}`);
          }}
        />
      )}
      {currentSection === "endInterview" && <EndInterviewSection />}
    </div>
  );
}

