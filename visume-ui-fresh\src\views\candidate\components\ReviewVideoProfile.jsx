import React, { useState } from "react";
import toast from "react-hot-toast";
import { BsExclamationCircle } from "react-icons/bs";
import { useNavigate, useParams } from "react-router-dom";

function ReviewVideoProfile({
  videoUrl = "",
  score = {
    score: {
      Communication_Score: 0,
      Skill_Score: 0,
      Overall_Score: 0,
    },
    Suggestions: "",
  },
  status = "NotSubmitted",
  finishVideoProfile = () => {},
}) {
  const { vpid } = useParams();
  const [toolTip, showToolTip] = useState(false);
  const navigate = useNavigate();
  const handleDelete = async () => {
    try {
      const videoProfileId = vpid;
      const response = await fetch(
        `${
          import.meta.env.VITE_APP_HOST
        }/api/v1/video-resume/${videoProfileId}`,
        {
          method: "DELETE",
        }
      );

      if (response.ok) {
        toast.success("Video profile discarded successfully");
        navigate("/candidate/dashboard");
        localStorage.removeItem(`questions`);
        localStorage.removeItem(`ReviewVideoProfile${vpid}`);
        window.location.reload(); // Ensure the page is reloaded to reflect changes
      } else {
        const { message } = await response.json();
        toast.error(message);
        console.error("Failed to delete video profile");
      }
    } catch (error) {
      console.error("Error:", error);
    }
  };
  return (
    <div className="flex h-full min-h-screen w-full flex-col items-center justify-start bg-gray-50 py-6 dark:bg-gray-900 lg:px-6">
      {/* Container */}
      <div className="flex h-full w-full flex-col rounded-xl bg-white p-6 shadow-lg dark:bg-gray-800">
        <span className=" absolute text-xl font-bold text-gray-800 dark:text-gray-200 md:mb-0 lg:mb-0">
          Review Your Video Resume
        </span>
        <div className="flex h-full w-full flex-col gap-6 md:mt-10 md:items-start lg:mt-10 lg:flex-row lg:items-start">
          {/* Left Side: Video Preview */}
          <div className="flex w-full flex-col items-start justify-evenly space-y-4 lg:w-3/5">
            <div className="relative h-[60vh] overflow-hidden rounded-xl border-0 border-gray-300 bg-gray-800 shadow-md">
              <video
                src={videoUrl}
                className="h-full w-full object-cover"
                controls
              />
            </div>

            {/* Overview Section (below video preview) */}
            <div className=" rounded-xl bg-white p-4 shadow-md dark:bg-gray-700 lg:mt-10">
              <h3 className="text-md font-semibold text-gray-800 dark:text-gray-200">
                Overview
              </h3>
              <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                This page contains the scores of the interview taken and
                evaluated by AI. The AI has assessed the candidate’s skill,
                communication, and overall performance. Here, users can review
                the video, check their scores, and ensure everything is accurate
                before submitting it into the database.
              </p>
            </div>
          </div>

          {/* Right Side: Score and Suggestions */}
          <div className="flex h-max w-full flex-col space-y-4 rounded-xl bg-white p-6 shadow-md dark:bg-gray-700 md:mt-0 lg:mt-0 lg:w-2/5">
            {/* Scores */}
            <div className="flex flex-col space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
                  Your Scores
                </h3>
                {score.Suggestions?.includes('default score') && (
                  <span className="rounded-full bg-yellow-100 px-3 py-1 text-xs font-medium text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                    Default Scoring
                  </span>
                )}
              </div>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                {/* Skill Score */}
                <div
                  className={`relative flex transform flex-col items-center justify-center rounded-lg p-4 shadow-lg transition-transform duration-300 ease-in-out hover:scale-105 ${
                    score.score.Skill_Score < 5
                      ? "bg-gradient-to-tr from-red-100 to-red-200 dark:from-red-700 dark:to-red-800"
                      : score.score.Skill_Score <= 7
                      ? "bg-gradient-to-tr from-yellow-100 to-yellow-200 dark:from-yellow-700 dark:to-yellow-800"
                      : "bg-gradient-to-tr from-green-100 to-green-200 dark:from-green-700 dark:to-green-800"
                  }`}
                >
                  <span className="text-lg font-bold text-gray-800 dark:text-gray-200">
                    Skill
                  </span>
                  <span
                    className={`text-4xl font-bold ${
                      score.score.Skill_Score < 5
                        ? "text-red-600 dark:text-red-400"
                        : score.score.Skill_Score <= 7
                        ? "text-yellow-600 dark:text-yellow-400"
                        : "text-green-600 dark:text-green-400"
                    }`}
                  >
                    {score.score.Skill_Score || 0}
                  </span>
                  {/* Animated Background Accent */}
                  <div
                    className={`absolute bottom-0 left-0 h-1 w-full ${
                      score.score.Skill_Score < 5
                        ? "bg-red-400 dark:bg-red-500"
                        : score.score.Skill_Score <= 7
                        ? "bg-yellow-400 dark:bg-yellow-500"
                        : "bg-green-400 dark:bg-green-500"
                    } transition-width duration-500 ease-in-out`}
                  ></div>
                </div>

                {/* Communication Score */}
                <div
                  className={`relative flex transform flex-col items-center justify-center rounded-lg p-4 shadow-lg transition-transform duration-300 ease-in-out hover:scale-105 ${
                    score.score.Communication_Score < 5
                      ? "bg-gradient-to-tr from-red-100 to-red-200 dark:from-red-700 dark:to-red-800"
                      : score.score.Communication_Score <= 7
                      ? "bg-gradient-to-tr from-yellow-100 to-yellow-200 dark:from-yellow-700 dark:to-yellow-800"
                      : "bg-gradient-to-tr from-green-100 to-green-200 dark:from-green-700 dark:to-green-800"
                  }`}
                >
                  <span className="text-lg font-bold text-gray-800 dark:text-gray-200">
                    Communication
                  </span>
                  <span
                    className={`text-4xl font-bold ${
                      score.score.Communication_Score < 5
                        ? "text-red-600 dark:text-red-400"
                        : score.score.Communication_Score <= 7
                        ? "text-yellow-600 dark:text-yellow-400"
                        : "text-green-600 dark:text-green-400"
                    }`}
                  >
                    {score.score.Communication_Score || 0}
                  </span>
                  {/* Animated Background Accent */}
                  <div
                    className={`absolute bottom-0 left-0 h-1 w-full ${
                      score.score.Communication_Score < 5
                        ? "bg-red-400 dark:bg-red-500"
                        : score.score.Communication_Score <= 7
                        ? "bg-yellow-400 dark:bg-yellow-500"
                        : "bg-green-400 dark:bg-green-500"
                    } transition-width duration-500 ease-in-out`}
                  ></div>
                </div>

                {/* Overall Score */}
                <div
                  className={`relative flex transform flex-col items-center justify-center rounded-lg p-4 shadow-lg transition-transform duration-300 ease-in-out hover:scale-105 ${
                    score.score.Overall_Score < 5
                      ? "bg-gradient-to-tr from-red-100 to-red-200 dark:from-red-700 dark:to-red-800"
                      : score.score.Overall_Score <= 7
                      ? "bg-gradient-to-tr from-yellow-100 to-yellow-200 dark:from-yellow-700 dark:to-yellow-800"
                      : "bg-gradient-to-tr from-green-100 to-green-200 dark:from-green-700 dark:to-green-800"
                  }`}
                >
                  <span className="text-nowrap px-2 text-lg font-bold text-gray-800 dark:text-gray-200">
                    Overall Score
                  </span>
                  <span
                    className={`text-4xl font-bold ${
                      score.score.Overall_Score < 5
                        ? "text-red-600 dark:text-red-400"
                        : score.score.Overall_Score <= 7
                        ? "text-yellow-600 dark:text-yellow-400"
                        : "text-green-600 dark:text-green-400"
                    }`}
                  >
                    {score.score.Overall_Score || 0}
                  </span>
                  {/* Animated Background Accent */}
                  <div
                    className={`absolute bottom-0 left-0 h-1 w-full ${
                      score.score.Overall_Score < 5
                        ? "bg-red-400 dark:bg-red-500"
                        : score.score.Overall_Score <= 7
                        ? "bg-yellow-400 dark:bg-yellow-500"
                        : "bg-green-400 dark:bg-green-500"
                    } transition-width duration-500 ease-in-out`}
                  ></div>
                </div>
              </div>
            </div>

            {/* Suggestions */}
            <div className="flex flex-col">
              <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
                Suggestions
              </h3>
              {score.Suggestions?.includes('default score') ? (
                <div className="mb-2 rounded-md bg-yellow-100 p-2 dark:bg-yellow-900">
                  <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                    Note: Using default scoring due to processing limitations. These scores may not fully reflect your performance.
                  </p>
                </div>
              ) : null}
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {score.Suggestions}
              </p>
            </div>

            {/* Status */}
            <div className="group relative flex flex-col">
              <h3 className="flex items-center justify-start gap-2 text-lg font-semibold text-gray-800 dark:text-gray-200">
                Status
                <BsExclamationCircle
                  onMouseEnter={() => showToolTip(true)}
                  onMouseLeave={() => showToolTip(false)}
                  className="group-hover:tooltip relative cursor-pointer text-[1rem]"
                />
                {toolTip && (
                  <div className="absolute bottom-full left-0 z-10 mb-2 block w-max rounded-md bg-gray-800 bg-opacity-50 px-3 py-1 text-sm text-white">
                    Status will be Active if score is above 5, otherwise
                    Inactive.
                  </div>
                )}
              </h3>
              <input
                type="text"
                disabled
                value={`${status}`}
                className="mt-2 rounded-lg border p-2 dark:bg-gray-800 dark:text-gray-300"
              />
            </div>

            {/* Finish Video profile (inside card now) */}
            <div className="flex items-center justify-start gap-10">
              <div
                style={{
                  marginTop: "3rem",
                }}
              >
                <a
                  className="inline-flex cursor-pointer items-center rounded-lg bg-red-500 px-3 py-3 font-semibold text-white shadow-md transition-all duration-200 ease-in-out hover:bg-red-600"
                  onClick={handleDelete}
                >
                  <svg
                    className="mr-3 h-6 w-6"
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <path
                      fill="#FFFFFF"
                      d="M17 10.5v-2c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v7c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2v-2l4 2v-7l-4 2Z"
                    />
                  </svg>
                  Discard Video Resume
                </a>
              </div>
              <div className="mt-auto">
                <a
                  className={`inline-flex cursor-pointer items-center rounded-lg px-3 py-3 font-semibold text-white shadow-md transition-all duration-200 ease-in-out ${
                    score.score.Overall_Score < 5
                      ? "bg-gray-500 hover:bg-gray-600"
                      : "bg-brand-500 hover:bg-brand-600"
                  }`}
                  onClick={
                    score.score.Overall_Score < 5
                      ? undefined
                      : finishVideoProfile
                  }
                >
                  <svg
                    className="mr-3 h-6 w-6"
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <path
                      fill="#FFFFFF"
                      d="M17 10.5v-2c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v7c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2v-2l4 2v-7l-4 2Z"
                    />
                  </svg>
                  Submit Video Resume
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ReviewVideoProfile;
