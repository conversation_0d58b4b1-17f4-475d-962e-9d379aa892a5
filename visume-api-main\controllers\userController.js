const bcrypt = require("bcryptjs");
const fs = require('fs');
const jwt = require("../utils/jwt");
const { generateRandomId } = require("../utils/helpers");
const axios = require("axios");
const prisma = require("../config/prisma");
const pdfParse = require('pdf-parse');
const {resumeStripper} = require('../utils/helpers');

// Process resume synchronously
const processResume = async (resumePath, cand_id) => {
  try {
    // Read and parse PDF
    const pdfBuffer = fs.readFileSync(resumePath);
    const pdfData = await pdfParse(pdfBuffer);
    const pdfText = pdfData.text;

    if (!pdfText) {
      throw new Error("Failed to extract text from PDF");
    }

    // Extract information using resumeStripper
    const strippedData = await resumeStripper(pdfText);
    if (!strippedData) {
      throw new Error("Failed to process resume content");
    }

    // Ensure we have valid JSON
    const strippedContent = JSON.stringify(strippedData);
    if (!strippedContent) {
      throw new Error("Failed to stringify resume data");
    }

    // Update database
    await prisma.jobseeker.update({
      where: {
        cand_id: cand_id,
      },
      data: {
        stripped_resume: strippedContent,
      },
    });

    console.log(`Successfully processed resume for candidate ID: ${cand_id}`);
    return true;
  } catch (err) {
    console.error("Error processing resume:", err);
    
    // Update database with error status
    await prisma.jobseeker.update({
      where: {
        cand_id: cand_id,
      },
      data: {
        stripped_resume: JSON.stringify({ error: "Failed to process resume" }),
      },
    });
    
    throw err;
  }
};

// Export all controller functions
const userController = {
  suggestedJobs: async (req, res) => {
    try {
      const jobs = await prisma.suggestedjobs.findMany();
      if (jobs.length === 0) {
        return res.status(404).json({ message: "No suggested jobs found" });
      }
      return res.status(200).json(jobs);
    } catch (err) {
      console.error("Error fetching suggested jobs:", err);
      return res.status(500).json({ error: err.message });
    }
  },

  loginJobseeker: async (req, res) => {
    const { email, password } = req.body;

    try {
      const user = await prisma.user.findUnique({
        where: { email },
        select: {
          email: true,
          password: true,
          role: true,
        },
      });

      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      const isPasswordValid = await bcrypt.compare(password, user.password);
      if (!isPasswordValid) {
        return res.status(401).json({ message: "Invalid password" });
      }

      if (user.role.toLowerCase() === "jobseeker") {
        const jobseeker = await prisma.jobseeker.findFirst({
          where: { cand_email: email },
          select: { cand_id: true },
        });

        if (!jobseeker) {
          return res.status(404).json({ message: "Candidate ID not found" });
        }

        const token = jwt.generateToken(user);
        return res.json({ token, role: user.role, cand_id: jobseeker.cand_id });
      } else {
        return res.status(401).json({
          message: "You are not a Jobseeker, please login with the correct role.",
        });
      }
    } catch (err) {
      console.error("Error during login:", err);
      return res.status(500).json({ error: err.message });
    }
  },

  loginEmployer: async (req, res) => {
    const { email, password } = req.body;

    try {
      const user = await prisma.user.findUnique({
        where: { email },
        select: {
          email: true,
          password: true,
          role: true,
        },
      });

      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      const isPasswordValid = await bcrypt.compare(password, user.password);
      if (!isPasswordValid) {
        return res.status(401).json({ message: "Invalid password" });
      }

      if (user.role.toLowerCase() === "employer") {
        const employer = await prisma.employer.findFirst({
          where: { emp_email: email },
          select: { emp_id: true, id: true },
        });

        if (!employer) {
          return res.status(404).json({ message: "Employer ID not found" });
        }

        const token = jwt.generateToken(user);
        return res.json({
          token,
          role: user.role,
          emp_id: employer.emp_id,
          employerId: employer.id,
        });
      } else {
        return res.status(401).json({
          message: "You are not an Employer, please login with the correct role.",
        });
      }
    } catch (err) {
      console.error("Error during login:", err);
      return res.status(500).json({ error: err.message });
    }
  },

  registerJobseeker: async (req, res) => {
    try {
      const {
        email,
        password,
        cand_name,
        cand_mobile,
        gender,
        languages_known,
        preferred_location,
      } = req.body;

      const resumeFile = req.files?.resume?.[0];
      if (!resumeFile) {
        return res.status(400).json({ message: 'Resume file is required' });
      }

      const existingUser = await prisma.user.findUnique({
        where: { email },
      });
      if (existingUser) {
        return res.status(409).json({ message: 'Email already exists' });
      }

      const hashedPassword = await bcrypt.hash(password, 10);
      const cand_id = generateRandomId();

      await prisma.$transaction([
        prisma.user.create({
          data: {
            email,
            password: hashedPassword,
            role: 'jobseeker',
          },
        }),
        prisma.jobseeker.create({
          data: {
            cand_id,
            cand_name,
            cand_mobile,
            cand_email: email,
            gender,
            languages_known,
            preferred_location,
          },
        }),
      ]);

      // Process resume after registration
      try {
        await processResume(resumeFile.path, cand_id);
      } catch (error) {
        console.error("Error processing resume:", error);
        // Continue with registration even if resume processing fails
        // The error status is already saved in the database by processResume
      }

      const token = jwt.generateToken({ email, role: 'jobseeker', cand_id });

      return res.status(200).json({
        message: 'Jobseeker registered successfully',
        token,
        role: 'jobseeker',
        cand_id,
      });
    } catch (err) {
      console.error('Error during jobseeker registration:', err);
      return res.status(500).json({ error: 'Internal server error' });
    }
  },

  registerEmployeer: async (req, res) => {
    const {
      emp_email,
      password,
      emp_name,
      emp_mobile,
      designation,
      company_id,
    } = req.body;

    try {
      // If company_id is provided, verify it exists first
      if (company_id) {
        const company = await prisma.company.findUnique({
          where: { id: parseInt(company_id) }
        });
        if (!company) {
          return res.status(400).json({ message: "Invalid company ID provided" });
        }
      }

      const existingUser = await prisma.user.findUnique({
        where: { email: emp_email },
      });
      if (existingUser) {
        return res.status(409).json({ message: "Email already exists" });
      }

      const hashedPassword = await bcrypt.hash(password, 10);
      
      // Create or get default employer plan
      let defaultPlan = await prisma.plans.findFirst({
        where: {
          role: 'emp',
          plan_name: 'Basic Employer Plan'
        }
      });

      if (!defaultPlan) {
        defaultPlan = await prisma.plans.create({
          data: {
            plan_name: 'Basic Employer Plan',
            plan_description: 'Default plan for new employers',
            plan_price: 0,
            plan_duration_days: 365,
            role: 'emp',
            credits_assigned: 10,
            features: '["Basic profile access", "Limited credits", "Standard support"]'
          }
        });
      }

      const result = await prisma.$transaction(async (prisma) => {
        const user = await prisma.user.create({
          data: {
            email: emp_email,
            password: hashedPassword,
            role: 'employer',
          },
        });

        const employerData = {
          emp_id: user.id,
          emp_name,
          emp_email,
          emp_mobile: BigInt(emp_mobile),
          designation,
        };

        if (company_id) {
          employerData.company_id = parseInt(company_id);
        }

        const employer = await prisma.employer.create({
          data: employerData
        });

        await prisma.employerplans.create({
          data: {
            emp_id: employer.id,
            plan_id: defaultPlan.id,
            start_date: new Date(),
            end_date: new Date(Date.now() + defaultPlan.plan_duration_days * 24 * 60 * 60 * 1000),
            creditsLeft: defaultPlan.credits_assigned,
          },
        });

        return { emp_id: user.id, employerId: employer.id };
      });

      const token = jwt.generateToken({
        email: emp_email,
        role: "employer",
      });

      return res.status(200).json({
        message: "Employer registered successfully",
        token,
        role: "employer",
        ...result,
      });
    } catch (err) {
      console.error("Error during employer registration:", err);
      return res.status(500).json({ error: err.message });
    }
  },

  getRoles: async (req, res) => {
    try {
      const roles = await prisma.roles.findMany({
        select: {
          id: true,
          role_name: true
        }
      });

      if (roles.length === 0) {
        return res.status(404).json({ message: "No roles found" });
      }

      return res.status(200).json({ roles });
    } catch (err) {
      console.error("Error fetching roles:", err);
      return res.status(500).json({ error: err.message });
    }
  },

  getSkills: async (req, res) => {
    try {
      const skills = await prisma.skills.findMany({
        select: {
          id: true,
          skill_name: true
        }
      });

      if (skills.length === 0) {
        return res.status(404).json({ message: "No skills found" });
      }

      return res.status(200).json({ skills });
    } catch (err) {
      console.error("Error fetching skills:", err);
      return res.status(500).json({ error: err.message });
    }
  },

  changePassword: async (req, res) => {
    const { cand_id, emp_id, password, newPassword } = req.body;

    try {
      let email;
      if (emp_id) {
        const employer = await prisma.employer.findUnique({
          where: { id: emp_id },
          select: { emp_email: true },
        });
        if (!employer) return res.status(404).json({ message: "Employer not found" });
        email = employer.emp_email;
      } else {
        const jobseeker = await prisma.jobseeker.findUnique({
          where: { cand_id },
          select: { cand_email: true },
        });
        if (!jobseeker) return res.status(404).json({ message: "Jobseeker not found" });
        email = jobseeker.cand_email;
      }

      const user = await prisma.user.findUnique({
        where: { email },
        select: { password: true },
      });

      if (!user) return res.status(404).json({ message: "User not found" });

      const isPasswordValid = await bcrypt.compare(password, user.password);
      if (!isPasswordValid) {
        return res.status(401).json({ message: "Invalid password" });
      }

      if (password === newPassword) {
        return res.status(400).json({
          message: "New password cannot be the same as the old password",
        });
      }

      const hashedNewPassword = await bcrypt.hash(newPassword, 10);
      await prisma.user.update({
        where: { email },
        data: { password: hashedNewPassword },
      });

      return res.json({ message: "Password changed successfully" });
    } catch (err) {
      console.error("Error during password change:", err);
      return res.status(500).json({ error: err.message });
    }
  },

clerkLogin: async (req, res) => {
    console.log("Using Clerk Secret Key:", process.env.CLERK_SECRET_KEY);
    console.log("Request body:", req.body);
    console.log("Request headers:", req.headers);

    // Try to get token from various sources
    const sessionToken = req.body.token ||
                       req.headers['x-session-token'] ||
                       (req.headers.authorization && req.headers.authorization.split(' ')[1]);
    
    const { sessionId } = req.body;
    
    if (!sessionId || !sessionToken) {
        return res.status(400).json({
            message: "Session ID and token are required",
            received: {
                sessionId,
                hasToken: !!sessionToken,
                authorization: !!req.headers.authorization,
                xSessionToken: !!req.headers['x-session-token']
            }
        });
    }

    try {
        // Verify the session with both secret key and session token
        const sessionResponse = await axios.get(
            `https://api.clerk.com/v1/sessions/${sessionId}`,
            {
                headers: {
                    Authorization: `Bearer ${process.env.CLERK_SECRET_KEY}`,
                    "Clerk-Session-Token": sessionToken
                }
            }
        );

        if (sessionResponse.status === 200) {
            const sessionData = sessionResponse.data;
            const userId = sessionData.user_id;

            // If session is valid, get user details
            const userResponse = await axios.get(
                `https://api.clerk.com/v1/users/${userId}`,
                {
                    headers: {
                        Authorization: `Bearer ${process.env.CLERK_SECRET_KEY}`,
                        "Clerk-Session-Token": sessionToken
                    }
                }
            );

            if (userResponse.status !== 200) {
                return res.status(404).json({ message: "User information not found" });
            }

            const userData = userResponse.data;
            const email = userData.email_addresses[0].email_address;

            const user = await prisma.user.findUnique({
                where: { email },
                select: { role: true },
            });

            if (!user) {
                return res.status(404).json({ message: "User not found" });
            }

            let responseData;
            const jwtToken = jwt.generateToken({ email, role: user.role });

            if (user.role.toLowerCase() === "jobseeker") {
                const jobseeker = await prisma.jobseeker.findFirst({
                    where: { cand_email: email },
                    select: { cand_id: true },
                });

                if (!jobseeker) {
                    return res.status(404).json({ message: "Candidate ID not found" });
                }

                responseData = { token: jwtToken, role: user.role, cand_id: jobseeker.cand_id };
            } else {
                const employer = await prisma.employer.findFirst({
                    where: { emp_email: email },
                    select: { emp_id: true, id: true },
                });

                if (!employer) {
                    return res.status(404).json({ message: "Employer ID not found" });
                }

                responseData = {
                    token: jwtToken,
                    role: user.role,
                    emp_id: employer.emp_id,
                    employerId: employer.id,
                };
            }

            return res.json(responseData);
        }
    } catch (error) {
        console.error(
            "Session verification error:",
            error.response ? error.response.data : error.message
        );
        return res.status(401).json({ success: false, message: "Invalid session" });
    }
}
};

// Export the controller object
module.exports = userController;