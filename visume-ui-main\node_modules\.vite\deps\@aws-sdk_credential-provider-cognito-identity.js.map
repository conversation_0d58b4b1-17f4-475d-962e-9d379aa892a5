{"version": 3, "sources": ["../../@aws-sdk/credential-provider-cognito-identity/dist-es/resolveLogins.js", "../../@aws-sdk/credential-provider-cognito-identity/dist-es/fromCognitoIdentity.js", "../../@aws-sdk/credential-provider-cognito-identity/dist-es/IndexedDbStorage.js", "../../@aws-sdk/credential-provider-cognito-identity/dist-es/InMemoryStorage.js", "../../@aws-sdk/credential-provider-cognito-identity/dist-es/localStorage.js", "../../@aws-sdk/credential-provider-cognito-identity/dist-es/fromCognitoIdentityPool.js"], "sourcesContent": ["export function resolveLogins(logins) {\n    return Promise.all(Object.keys(logins).reduce((arr, name) => {\n        const tokenOrProvider = logins[name];\n        if (typeof tokenOrProvider === \"string\") {\n            arr.push([name, tokenOrProvider]);\n        }\n        else {\n            arr.push(tokenOrProvider().then((token) => [name, token]));\n        }\n        return arr;\n    }, [])).then((resolvedPairs) => resolvedPairs.reduce((logins, [key, value]) => {\n        logins[key] = value;\n        return logins;\n    }, {}));\n}\n", "import { CredentialsProviderError } from \"@smithy/property-provider\";\nimport { resolveLogins } from \"./resolveLogins\";\nexport function fromCognitoIdentity(parameters) {\n    return async () => {\n        parameters.logger?.debug(\"@aws-sdk/credential-provider-cognito-identity - fromCognitoIdentity\");\n        const { GetCredentialsForIdentityCommand, CognitoIdentityClient } = await import(\"./loadCognitoIdentity\");\n        const { Credentials: { AccessKeyId = throwOnMissingAccessKeyId(parameters.logger), Expiration, SecretKey = throwOnMissingSecretKey(parameters.logger), SessionToken, } = throwOnMissingCredentials(parameters.logger), } = await (parameters.client ??\n            new CognitoIdentityClient(Object.assign({}, parameters.clientConfig ?? {}, {\n                region: parameters.clientConfig?.region ?? parameters.parentClientConfig?.region,\n            }))).send(new GetCredentialsForIdentityCommand({\n            CustomRoleArn: parameters.customRoleArn,\n            IdentityId: parameters.identityId,\n            Logins: parameters.logins ? await resolveLogins(parameters.logins) : undefined,\n        }));\n        return {\n            identityId: parameters.identityId,\n            accessKeyId: AccessKeyId,\n            secretAccessKey: SecretKey,\n            sessionToken: SessionToken,\n            expiration: Expiration,\n        };\n    };\n}\nfunction throwOnMissingAccessKeyId(logger) {\n    throw new CredentialsProviderError(\"Response from Amazon Cognito contained no access key ID\", { logger });\n}\nfunction throwOnMissingCredentials(logger) {\n    throw new CredentialsProviderError(\"Response from Amazon Cognito contained no credentials\", { logger });\n}\nfunction throwOnMissingSecretKey(logger) {\n    throw new CredentialsProviderError(\"Response from Amazon Cognito contained no secret key\", { logger });\n}\n", "const STORE_NAME = \"IdentityIds\";\nexport class IndexedDbStorage {\n    constructor(dbName = \"aws:cognito-identity-ids\") {\n        this.dbName = dbName;\n    }\n    getItem(key) {\n        return this.withObjectStore(\"readonly\", (store) => {\n            const req = store.get(key);\n            return new Promise((resolve) => {\n                req.onerror = () => resolve(null);\n                req.onsuccess = () => resolve(req.result ? req.result.value : null);\n            });\n        }).catch(() => null);\n    }\n    removeItem(key) {\n        return this.withObjectStore(\"readwrite\", (store) => {\n            const req = store.delete(key);\n            return new Promise((resolve, reject) => {\n                req.onerror = () => reject(req.error);\n                req.onsuccess = () => resolve();\n            });\n        });\n    }\n    setItem(id, value) {\n        return this.withObjectStore(\"readwrite\", (store) => {\n            const req = store.put({ id, value });\n            return new Promise((resolve, reject) => {\n                req.onerror = () => reject(req.error);\n                req.onsuccess = () => resolve();\n            });\n        });\n    }\n    getDb() {\n        const openDbRequest = self.indexedDB.open(this.dbName, 1);\n        return new Promise((resolve, reject) => {\n            openDbRequest.onsuccess = () => {\n                resolve(openDbRequest.result);\n            };\n            openDbRequest.onerror = () => {\n                reject(openDbRequest.error);\n            };\n            openDbRequest.onblocked = () => {\n                reject(new Error(\"Unable to access DB\"));\n            };\n            openDbRequest.onupgradeneeded = () => {\n                const db = openDbRequest.result;\n                db.onerror = () => {\n                    reject(new Error(\"Failed to create object store\"));\n                };\n                db.createObjectStore(STORE_NAME, { keyPath: \"id\" });\n            };\n        });\n    }\n    withObjectStore(mode, action) {\n        return this.getDb().then((db) => {\n            const tx = db.transaction(STORE_NAME, mode);\n            tx.oncomplete = () => db.close();\n            return new Promise((resolve, reject) => {\n                tx.onerror = () => reject(tx.error);\n                resolve(action(tx.objectStore(STORE_NAME)));\n            }).catch((err) => {\n                db.close();\n                throw err;\n            });\n        });\n    }\n}\n", "export class InMemoryStorage {\n    constructor(store = {}) {\n        this.store = store;\n    }\n    getItem(key) {\n        if (key in this.store) {\n            return this.store[key];\n        }\n        return null;\n    }\n    removeItem(key) {\n        delete this.store[key];\n    }\n    setItem(key, value) {\n        this.store[key] = value;\n    }\n}\n", "import { IndexedDbStorage } from \"./IndexedDbStorage\";\nimport { InMemoryStorage } from \"./InMemoryStorage\";\nconst inMemoryStorage = new InMemoryStorage();\nexport function localStorage() {\n    if (typeof self === \"object\" && self.indexedDB) {\n        return new IndexedDbStorage();\n    }\n    if (typeof window === \"object\" && window.localStorage) {\n        return window.localStorage;\n    }\n    return inMemoryStorage;\n}\n", "import { CredentialsProviderError } from \"@smithy/property-provider\";\nimport { fromCognitoIdentity } from \"./fromCognitoIdentity\";\nimport { localStorage } from \"./localStorage\";\nimport { resolveLogins } from \"./resolveLogins\";\nexport function fromCognitoIdentityPool({ accountId, cache = localStorage(), client, clientConfig, customRoleArn, identityPoolId, logins, userIdentifier = !logins || Object.keys(logins).length === 0 ? \"ANONYMOUS\" : undefined, logger, parentClientConfig, }) {\n    logger?.debug(\"@aws-sdk/credential-provider-cognito-identity - fromCognitoIdentity\");\n    const cacheKey = userIdentifier\n        ? `aws:cognito-identity-credentials:${identityPoolId}:${userIdentifier}`\n        : undefined;\n    let provider = async () => {\n        const { GetIdCommand, CognitoIdentityClient } = await import(\"./loadCognitoIdentity\");\n        const _client = client ??\n            new CognitoIdentityClient(Object.assign({}, clientConfig ?? {}, { region: clientConfig?.region ?? parentClientConfig?.region }));\n        let identityId = (cacheKey && (await cache.getItem(cacheKey)));\n        if (!identityId) {\n            const { IdentityId = throwOnMissingId(logger) } = await _client.send(new GetIdCommand({\n                AccountId: accountId,\n                IdentityPoolId: identityPoolId,\n                Logins: logins ? await resolveLogins(logins) : undefined,\n            }));\n            identityId = IdentityId;\n            if (cacheKey) {\n                Promise.resolve(cache.setItem(cacheKey, identityId)).catch(() => { });\n            }\n        }\n        provider = fromCognitoIdentity({\n            client: _client,\n            customRoleArn,\n            logins,\n            identityId,\n        });\n        return provider();\n    };\n    return () => provider().catch(async (err) => {\n        if (cacheKey) {\n            Promise.resolve(cache.removeItem(cacheKey)).catch(() => { });\n        }\n        throw err;\n    });\n}\nfunction throwOnMissingId(logger) {\n    throw new CredentialsProviderError(\"Response from Amazon Cognito contained no identity ID\", { logger });\n}\n"], "mappings": ";;;;;;AAAO,SAAS,cAAc,QAAQ;AAClC,SAAO,QAAQ,IAAI,OAAO,KAAK,MAAM,EAAE,OAAO,CAAC,KAAK,SAAS;AACzD,UAAM,kBAAkB,OAAO,IAAI;AACnC,QAAI,OAAO,oBAAoB,UAAU;AACrC,UAAI,KAAK,CAAC,MAAM,eAAe,CAAC;AAAA,IACpC,OACK;AACD,UAAI,KAAK,gBAAgB,EAAE,KAAK,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,CAAC;AAAA,IAC7D;AACA,WAAO;AAAA,EACX,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,kBAAkB,cAAc,OAAO,CAACA,SAAQ,CAAC,KAAK,KAAK,MAAM;AAC3E,IAAAA,QAAO,GAAG,IAAI;AACd,WAAOA;AAAA,EACX,GAAG,CAAC,CAAC,CAAC;AACV;;;ACZO,SAAS,oBAAoB,YAAY;AAC5C,SAAO,YAAY;AAHvB;AAIQ,qBAAW,WAAX,mBAAmB,MAAM;AACzB,UAAM,EAAE,kCAAkC,sBAAsB,IAAI,MAAM,OAAO,mCAAuB;AACxG,UAAM,EAAE,aAAa,EAAE,cAAc,0BAA0B,WAAW,MAAM,GAAG,YAAY,YAAY,wBAAwB,WAAW,MAAM,GAAG,aAAc,IAAI,0BAA0B,WAAW,MAAM,EAAG,IAAI,OAAO,WAAW,UACzO,IAAI,sBAAsB,OAAO,OAAO,CAAC,GAAG,WAAW,gBAAgB,CAAC,GAAG;AAAA,MACvE,UAAQ,gBAAW,iBAAX,mBAAyB,aAAU,gBAAW,uBAAX,mBAA+B;AAAA,IAC9E,CAAC,CAAC,GAAG,KAAK,IAAI,iCAAiC;AAAA,MAC/C,eAAe,WAAW;AAAA,MAC1B,YAAY,WAAW;AAAA,MACvB,QAAQ,WAAW,SAAS,MAAM,cAAc,WAAW,MAAM,IAAI;AAAA,IACzE,CAAC,CAAC;AACF,WAAO;AAAA,MACH,YAAY,WAAW;AAAA,MACvB,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,YAAY;AAAA,IAChB;AAAA,EACJ;AACJ;AACA,SAAS,0BAA0B,QAAQ;AACvC,QAAM,IAAI,yBAAyB,2DAA2D,EAAE,OAAO,CAAC;AAC5G;AACA,SAAS,0BAA0B,QAAQ;AACvC,QAAM,IAAI,yBAAyB,yDAAyD,EAAE,OAAO,CAAC;AAC1G;AACA,SAAS,wBAAwB,QAAQ;AACrC,QAAM,IAAI,yBAAyB,wDAAwD,EAAE,OAAO,CAAC;AACzG;;;AC/BA,IAAM,aAAa;AACZ,IAAM,mBAAN,MAAuB;AAAA,EAC1B,YAAY,SAAS,4BAA4B;AAC7C,SAAK,SAAS;AAAA,EAClB;AAAA,EACA,QAAQ,KAAK;AACT,WAAO,KAAK,gBAAgB,YAAY,CAAC,UAAU;AAC/C,YAAM,MAAM,MAAM,IAAI,GAAG;AACzB,aAAO,IAAI,QAAQ,CAAC,YAAY;AAC5B,YAAI,UAAU,MAAM,QAAQ,IAAI;AAChC,YAAI,YAAY,MAAM,QAAQ,IAAI,SAAS,IAAI,OAAO,QAAQ,IAAI;AAAA,MACtE,CAAC;AAAA,IACL,CAAC,EAAE,MAAM,MAAM,IAAI;AAAA,EACvB;AAAA,EACA,WAAW,KAAK;AACZ,WAAO,KAAK,gBAAgB,aAAa,CAAC,UAAU;AAChD,YAAM,MAAM,MAAM,OAAO,GAAG;AAC5B,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,YAAI,UAAU,MAAM,OAAO,IAAI,KAAK;AACpC,YAAI,YAAY,MAAM,QAAQ;AAAA,MAClC,CAAC;AAAA,IACL,CAAC;AAAA,EACL;AAAA,EACA,QAAQ,IAAI,OAAO;AACf,WAAO,KAAK,gBAAgB,aAAa,CAAC,UAAU;AAChD,YAAM,MAAM,MAAM,IAAI,EAAE,IAAI,MAAM,CAAC;AACnC,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,YAAI,UAAU,MAAM,OAAO,IAAI,KAAK;AACpC,YAAI,YAAY,MAAM,QAAQ;AAAA,MAClC,CAAC;AAAA,IACL,CAAC;AAAA,EACL;AAAA,EACA,QAAQ;AACJ,UAAM,gBAAgB,KAAK,UAAU,KAAK,KAAK,QAAQ,CAAC;AACxD,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,oBAAc,YAAY,MAAM;AAC5B,gBAAQ,cAAc,MAAM;AAAA,MAChC;AACA,oBAAc,UAAU,MAAM;AAC1B,eAAO,cAAc,KAAK;AAAA,MAC9B;AACA,oBAAc,YAAY,MAAM;AAC5B,eAAO,IAAI,MAAM,qBAAqB,CAAC;AAAA,MAC3C;AACA,oBAAc,kBAAkB,MAAM;AAClC,cAAM,KAAK,cAAc;AACzB,WAAG,UAAU,MAAM;AACf,iBAAO,IAAI,MAAM,+BAA+B,CAAC;AAAA,QACrD;AACA,WAAG,kBAAkB,YAAY,EAAE,SAAS,KAAK,CAAC;AAAA,MACtD;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,gBAAgB,MAAM,QAAQ;AAC1B,WAAO,KAAK,MAAM,EAAE,KAAK,CAAC,OAAO;AAC7B,YAAM,KAAK,GAAG,YAAY,YAAY,IAAI;AAC1C,SAAG,aAAa,MAAM,GAAG,MAAM;AAC/B,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,WAAG,UAAU,MAAM,OAAO,GAAG,KAAK;AAClC,gBAAQ,OAAO,GAAG,YAAY,UAAU,CAAC,CAAC;AAAA,MAC9C,CAAC,EAAE,MAAM,CAAC,QAAQ;AACd,WAAG,MAAM;AACT,cAAM;AAAA,MACV,CAAC;AAAA,IACL,CAAC;AAAA,EACL;AACJ;;;AClEO,IAAM,kBAAN,MAAsB;AAAA,EACzB,YAAY,QAAQ,CAAC,GAAG;AACpB,SAAK,QAAQ;AAAA,EACjB;AAAA,EACA,QAAQ,KAAK;AACT,QAAI,OAAO,KAAK,OAAO;AACnB,aAAO,KAAK,MAAM,GAAG;AAAA,IACzB;AACA,WAAO;AAAA,EACX;AAAA,EACA,WAAW,KAAK;AACZ,WAAO,KAAK,MAAM,GAAG;AAAA,EACzB;AAAA,EACA,QAAQ,KAAK,OAAO;AAChB,SAAK,MAAM,GAAG,IAAI;AAAA,EACtB;AACJ;;;ACdA,IAAM,kBAAkB,IAAI,gBAAgB;AACrC,SAAS,eAAe;AAC3B,MAAI,OAAO,SAAS,YAAY,KAAK,WAAW;AAC5C,WAAO,IAAI,iBAAiB;AAAA,EAChC;AACA,MAAI,OAAO,WAAW,YAAY,OAAO,cAAc;AACnD,WAAO,OAAO;AAAA,EAClB;AACA,SAAO;AACX;;;ACPO,SAAS,wBAAwB,EAAE,WAAW,QAAQ,aAAa,GAAG,QAAQ,cAAc,eAAe,gBAAgB,QAAQ,iBAAiB,CAAC,UAAU,OAAO,KAAK,MAAM,EAAE,WAAW,IAAI,cAAc,QAAW,QAAQ,mBAAoB,GAAG;AAC7P,mCAAQ,MAAM;AACd,QAAM,WAAW,iBACX,oCAAoC,cAAc,IAAI,cAAc,KACpE;AACN,MAAI,WAAW,YAAY;AACvB,UAAM,EAAE,cAAc,sBAAsB,IAAI,MAAM,OAAO,mCAAuB;AACpF,UAAM,UAAU,UACZ,IAAI,sBAAsB,OAAO,OAAO,CAAC,GAAG,gBAAgB,CAAC,GAAG,EAAE,SAAQ,6CAAc,YAAU,yDAAoB,QAAO,CAAC,CAAC;AACnI,QAAI,aAAc,YAAa,MAAM,MAAM,QAAQ,QAAQ;AAC3D,QAAI,CAAC,YAAY;AACb,YAAM,EAAE,aAAa,iBAAiB,MAAM,EAAE,IAAI,MAAM,QAAQ,KAAK,IAAI,aAAa;AAAA,QAClF,WAAW;AAAA,QACX,gBAAgB;AAAA,QAChB,QAAQ,SAAS,MAAM,cAAc,MAAM,IAAI;AAAA,MACnD,CAAC,CAAC;AACF,mBAAa;AACb,UAAI,UAAU;AACV,gBAAQ,QAAQ,MAAM,QAAQ,UAAU,UAAU,CAAC,EAAE,MAAM,MAAM;AAAA,QAAE,CAAC;AAAA,MACxE;AAAA,IACJ;AACA,eAAW,oBAAoB;AAAA,MAC3B,QAAQ;AAAA,MACR;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC;AACD,WAAO,SAAS;AAAA,EACpB;AACA,SAAO,MAAM,SAAS,EAAE,MAAM,OAAO,QAAQ;AACzC,QAAI,UAAU;AACV,cAAQ,QAAQ,MAAM,WAAW,QAAQ,CAAC,EAAE,MAAM,MAAM;AAAA,MAAE,CAAC;AAAA,IAC/D;AACA,UAAM;AAAA,EACV,CAAC;AACL;AACA,SAAS,iBAAiB,QAAQ;AAC9B,QAAM,IAAI,yBAAyB,yDAAyD,EAAE,OAAO,CAAC;AAC1G;", "names": ["logins"]}