import React, { useEffect, useState } from "react";
import VideoPreview from "../smaller_comp/VideoPreview";
import StatusIndicator from "../smaller_comp/StatusIndicator";
import Button from "../smaller_comp/Button";
import { FaMicrophoneAlt } from "react-icons/fa";

const DeviceTestingSection = ({
  localCamStream,
  startWebcam,
  onStartInterview,
}) => {
  const [devices, setDevices] = useState({ cameras: [], microphones: [] });
  const [selectedCamera, setSelectedCamera] = useState("");
  const [selectedMicrophone, setSelectedMicrophone] = useState("");
  const [volumeLevel, setVolumeLevel] = useState(0); // Store the current volume level
  const [micColor, setMicColor] = useState("gray"); // Initial color (gray when inactive)

  useEffect(() => {
    // Function to list media devices
    const getMediaDevices = async () => {
      const devices = await navigator.mediaDevices.enumerateDevices();
      const cameras = devices.filter((device) => device.kind === "videoinput");
      const microphones = devices.filter(
        (device) => device.kind === "audioinput"
      );
      setDevices({ cameras, microphones });

      // Set default camera and microphone
      if (cameras.length > 0) setSelectedCamera(cameras[0].deviceId);
      if (microphones.length > 0)
        setSelectedMicrophone(microphones[0].deviceId);
    };

    getMediaDevices();
    startWebcam(); // Automatically starts the webcam when component mounts
  }, []);

  useEffect(() => {
    if (!selectedMicrophone) return;

    const startAudioStream = async () => {
      try {
        // Access the selected microphone
        const stream = await navigator.mediaDevices.getUserMedia({
          audio: { deviceId: selectedMicrophone },
        });

        // Create an audio context and analyser node for volume detection
        const audioContext = new (window.AudioContext ||
          window.webkitAudioContext)();
        const analyser = audioContext.createAnalyser();
        const microphoneSource = audioContext.createMediaStreamSource(stream);

        microphoneSource.connect(analyser);
        analyser.fftSize = 256; // FFT size to define frequency resolution
        const bufferLength = analyser.frequencyBinCount;
        const dataArray = new Uint8Array(bufferLength);

        const updateVolume = () => {
          analyser.getByteFrequencyData(dataArray);

          let sum = 0;
          for (let i = 0; i < dataArray.length; i++) {
            sum += dataArray[i];
          }

          const average = sum / dataArray.length;
          setVolumeLevel(average);

          // Set the microphone icon color based on volume level
          if (average < 30) {
            setMicColor("yellow"); // Low volume
          } else if (average < 60) {
            setMicColor("orange"); // Medium volume
          } else {
            setMicColor("red"); // Very high volume
          }

          // Continue to analyze the audio
          requestAnimationFrame(updateVolume);
        };

        // Start volume monitoring
        updateVolume();
      } catch (error) {
        console.error("Error accessing microphone", error);
      }
    };

    startAudioStream();

  }, [selectedMicrophone]); // Restart when the microphone is selected

  const handleCameraChange = (e) => {
    setSelectedCamera(e.target.value);
    startWebcam(e.target.value); // Pass the selected camera ID to start the webcam
  };

  const handleMicrophoneChange = (e) => {
    setSelectedMicrophone(e.target.value);
    // Implement logic to change microphone input
  };

  return (
    <main className="container mx-auto flex h-full items-start justify-center pt-0">
      <div className="m-auto mt-0 flex w-3/4 rounded-xl p-6">
        <VideoPreview stream={localCamStream} />
        <div className=" -mt-3 flex w-1/3 flex-col justify-center p-6">
          <h2 className="mx-auto mb-4 text-center text-xl font-semibold">
            Device Check
          </h2>

          {/* Camera Selection */}
          <div className="mb-4">
            <label
              htmlFor="camera-select"
              className="block text-sm font-medium text-gray-700"
            >
              Choose Camera
            </label>
            <select
              id="camera-select"
              value={selectedCamera}
              onChange={handleCameraChange}
              className="mt-1 block w-full rounded-md border border-gray-300 bg-gray-50 p-2 text-sm shadow-sm transition-colors duration-200 hover:bg-white focus:border-indigo-500 focus:ring-indigo-500"
            >
              {devices.cameras.map((camera) => (
                <option key={camera.deviceId} value={camera.deviceId}>
                  {camera.label || `Camera ${camera.deviceId}`}
                </option>
              ))}
            </select>
          </div>

          {/* Microphone Selection */}
          <div className="mb-4">
            <label
              htmlFor="microphone-select"
              className="block text-sm font-medium text-gray-700"
            >
              Choose Microphone
            </label>
            <select
              id="microphone-select"
              value={selectedMicrophone}
              onChange={handleMicrophoneChange}
              className="mt-1 block w-full rounded-md border border-gray-300 bg-gray-50 p-2 text-sm shadow-sm transition-colors duration-200 hover:bg-white focus:border-indigo-500 focus:ring-indigo-500"
            >
              {devices.microphones.map((mic) => (
                <option key={mic.deviceId} value={mic.deviceId}>
                  {mic.label || `Microphone ${mic.deviceId}`}
                </option>
              ))}
            </select>
          </div>

          {/* Status indicators for camera/microphone */}
          <div className="flex w-full items-start justify-between">
            <div className="w-full">
              <StatusIndicator
                label="Camera and Microphone"
                status={localCamStream ? "success" : "error"}
              />
            </div>
            <div className="flex items-center">
              <FaMicrophoneAlt
                className={`text-6xl transition-colors duration-300 ${
                  micColor === "gray" ? "text-gray-400" : ""
                } ${micColor === "yellow" ? "text-yellow-400" : ""} ${
                  micColor === "orange" ? "text-orange-400" : ""
                } ${micColor === "red" ? "text-red-500" : ""}`}
              />
            </div>
          </div>
          {/* Start Interview Button */}
          <Button onClick={onStartInterview} disabled={!localCamStream} className="mt-4">
            Start Interview
          </Button>
        </div>
      </div>
    </main>
  );
};

export default DeviceTestingSection;
