const express = require("express");
const router = express.Router();
const userController = require("../controllers/userController");
const videoProfileController = require("../controllers/videoProfileController");
const mockInterviewController = require("../controllers/mockInterviewController");
const jobSeekerPlanController = require("../controllers/jobSeekerPlanController");
const employerProfileController = require("../controllers/employerProfileController");
const companyController = require("../controllers/companyController");
const helper = require("../utils/helpers");
const multer = require("multer");
const path = require("path");

// Multer setup for file uploads (resume and profile pictures)
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const dir =
      file.fieldname === "resume"
        ? "utils/files/resumes/"
        : file.fieldname === "job_description"
        ? "utils/files/job_description/"
        : "utils/files/profile_pics/";
    cb(null, dir);
  },
  filename: (req, file, cb) => {
    const ext = path.extname(file.originalname);
    cb(null, `${Date.now()}_${file.fieldname}${ext}`);
  },
});

const upload = multer({ storage });

// User authentication routes
router.post("/login-jobseeker", userController.loginJobseeker);
router.post("/login-employer", userController.loginEmployer);
router.post("/verify-session", userController.clerkLogin);

// Register job seeker with resume and profile picture uploads
router.post(
  "/register-jobseeker",
  upload.fields([
    { name: "resume", maxCount: 1 },
    { name: "profile_picture", maxCount: 1 },
  ]),
  (req, res, next) => {
    console.log("Files:", req.files);
    console.log("Body:", req.body);
    next(); // Continue to the controller
  },
  userController.registerJobseeker
);

router.post(
  "/upload-job-description/:emp_id",
  upload.fields([{ name: "job_description", maxCount: 1 }]),
  employerProfileController.uploadJobDescription
);

router.post(
  "/create-job-description",
  employerProfileController.createJobDescription
);

router.post(
  "/add-new-company",
  upload.fields([{ name: "company_logo", maxCount: 1 }]),
  companyController.addNewCompany
);

router.get("/get-all-company", companyController.getAllCompany);

// Register job seeker with resume and profile picture uploads

router.post("/register-employeer", userController.registerEmployeer);

// Route for creating a video profile
router.post("/create-video-resume", videoProfileController.createVideoProfile);

// Route for fetching all video profiles
router.get("/video-resume", videoProfileController.listVideoProfiles);

// Route for fetching video profiles by candidate ID
router.get(
  "/video-resume/:cand_id",
  videoProfileController.listVideoProfilesByCandidateId
);

// Route for fetching All candidate profiles
router.get("/getAllCandidates", videoProfileController.listAllCandidates);

router.get(
  "/getSuggestedCandidates",
  videoProfileController.listSuggestedCandidates
);

router.get("/suggestedJobs", userController.suggestedJobs);
// Route for fetching All candidate profiles
router.get("/filterCandidate", videoProfileController.filterCandidate);

// Route for fetching candidate profile and video resumes by candidate ID
router.get(
  "/candidate/:cand_id",
  videoProfileController.listCandidateAndVideoProfilesByCandidateId
);

// Route for deleting video profiles by candidate ID
router.delete(
  "/video-resume/:video_profile_id",
  videoProfileController.deleteVideoProfileById
);

// Route for fetch video profile Data by Video Profile Id
router.get(
  "/video-resume-data/:video_profile_id",
  videoProfileController.fetchVideoProfileById
);

// Route for fetching job seeker plan by candidate ID
router.get(
  "/jobseeker-plan/:cand_id",
  jobSeekerPlanController.getJobSeekerPlanByCandidateId
);

// Route for fetching profiles by employer ID
router.get(
  "/employer-profiles/:emp_id",
  employerProfileController.getProfilesByEmployerId
);

// Route for shortlisting a video profile for an employer
router.post(
  "/employer-profiles/shortlist-candidate",
  employerProfileController.shortlistVideoProfile
);

// Route for unshortlisting a video profile for an employer
router.post(
  "/employer-profiles/unshortlist-candidate",
  employerProfileController.unShortlistVideoProfile
);

// Route for fetching shortlisting candidate by employer ID
router.get(
  "/shortlisted-profiles/:emp_id",
  employerProfileController.getShortlistProfiles
);

// Route for unlocking a video profile for an employer after being shortlisted
router.post(
  "/employer-profiles/unlockVideoProfile",
  employerProfileController.unlockVideoProfile
);

// Route for deleting a video profile for an employer from shortlisted
router.delete(
  "/employer-profiles/remove",
  employerProfileController.removeVideoProfile
);

router.get(
  "/employerProfilesData",
  employerProfileController.getEmployerProfilesData
);


router.get("/get-roles", userController.getRoles);
router.get("/get-skills", userController.getSkills);


router.get("/getEmployerDetails", employerProfileController.getEmployerDetails);

router.post("/candidate/analytics", videoProfileController.addAnalytics);

router.put("/add-video-resume", videoProfileController.addVideoProfileData);
router.put("/finish-video-resume", videoProfileController.finishVideoProfile);
router.put("/changePassword", userController.changePassword);

router.post("/generateScore", videoProfileController.generateScore);

// Mock Interviews
router.post("/create-mock-resume", mockInterviewController.createMockResume);
router.put("/update-mock-resume", mockInterviewController.updateMockResumeData);
router.put("/finish-mock-resume", mockInterviewController.finishMockResume);
router.post("/generate-mock-score", mockInterviewController.generateMockResults);
// Route for deleting video profiles by candidate ID
router.delete(
  "/mock-resume/:mock_interview_id",
  mockInterviewController.deleteMockInterviewById
);
router.get(
  "/mock-resume-data/:mock_resume_id",
  mockInterviewController.fetchMockResumeById
);
router.get(
  "/mock-resume/:cand_id",
  mockInterviewController.listMockProfilesByCandidateId
);
module.exports = router;
