import { button } from "@material-tailwind/react";
import React, { useState } from "react";
import toast from "react-hot-toast";
import {
  HiArrowR<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  HiUserGroup,
} from "react-icons/hi";
import { MdDelete } from "react-icons/md";
import { useNavigate } from "react-router-dom";

const MockResumeCard = ({ profile, toggleVideoProfilePopup }) => {
  const [loader, setLoader] = useState(false);
  console.log(profile)


  const deleteVisume = async (id) => {
    try {
      setLoader(true);
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/mock-resume/${id}`,
        {
          method: "DELETE",
        }
      );

      if (response.ok) {
        toast.success("Mock Interview deleted successfully");
        window.location.reload(); // Ensure the page is reloaded to reflect changes
      } else {
        console.error("Failed to delete Mock Interview");
      }
    } catch (error) {
      console.error("Error:", error);
    } finally {
      setLoader(false);
    }
  };

  const navigate = useNavigate();

  return (
    <div className="col-span-full mb-2 rounded-lg bg-gray-100 p-4 transition-all duration-300 ease-in-out dark:bg-navy-900">
      <div className="flex flex-col gap-4">
        {/* Profile Details */}
        <div className="flex items-start justify-between">
          <div className="flex flex-col">
            <div className="mb-1 flex items-center gap-2">
              <h2 className="text-base font-bold text-gray-800 dark:text-white">
                {profile.role}
              </h2>
            </div>
            <ul className="flex flex-wrap gap-2">
              {profile.skills.map((skill, index) => (
                <li
                  key={index}
                  className="rounded-md bg-gray-800 px-2 py-1 text-[11px] text-white dark:bg-blue-600 dark:text-gray-300"
                >
                  {skill}
                </li>
              ))}
            </ul>
          </div>

          {/* View/Continue Button */}
          {/* <div>
            
            {profile.status.toLowerCase() === "notsubmitted" ||
            profile.status.toLowerCase() === "started" ||
            profile.status.trim() === "" ? (
              <>
                <button
                  onClick={() =>
                    navigate(`/candidate/mock-interview/${profile.vpid}`)
                  }
                  className="flex items-center gap-2 rounded-lg bg-brand-500 px-3 py-1 text-white transition-colors duration-200 hover:bg-blue-600"
                >
                  <HiArrowRight />
                  Continue Mock Inteview
                </button>
                <button
                  onClick={() => deleteVisume(profile.vpid)}
                  className="mt-5 flex items-center gap-2 rounded-lg bg-red-500 px-3 py-1 text-white transition-colors duration-200 hover:bg-red-600"
                >
                  {loader ? (
                    <svg
                      className="h-4 w-4 animate-spin text-white"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                        fill="none"
                      />
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      />
                    </svg>
                  ) : (
                    <>
                      <MdDelete />
                      Delete Video Resume
                    </>
                  )}
                </button>
              </>
            ) : (
              <button
                onClick={() =>
                  navigate(`/candidate/videoResume/${profile.vpid}`)
                }
                className="flex items-center gap-2 rounded-full border-2 border-brand-500 px-3 py-1 font-semibold text-brand-500 shadow-md transition-colors duration-200 ease-in-out hover:bg-brand-500 hover:text-white hover:shadow-lg"
              >
                <HiEye />
                <span className="text-sm">View</span>
              </button>
            )}
          </div> */}
        </div>

        {/* Additional Information */}
        <div className="flex items-center justify-start gap-6 text-sm text-gray-600 dark:text-gray-300">
          <div className="flex items-center gap-2">
            <HiEye className="text-blue-500" />
            {/* <span>{profile.totalViews} Views</span> */}
            <span className="font-semibold">10 Views</span>
          </div>
          <div className="flex items-center gap-2">
            <HiClipboardList className="text-green-500" />
            <span className="font-semibold">3 Shortlists</span>
            {/* <span>{profile.shortlists} Shortlists</span> */}
          </div>
          <div className="flex items-center gap-2">
            <HiUserGroup className="text-purple-500" />
            <span className="font-semibold">1 Interviews</span>
            {/* <span>{profile.interviews} Interviews</span> */}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MockResumeCard;
