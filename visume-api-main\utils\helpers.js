const axios = require("axios");
const pdf = require("pdf-parse");
const { GoogleGenerativeAI } = require("@google/generative-ai");
const path = require("path");
const fs = require("fs");
const crypto = require("crypto");
const dotenv = require("dotenv");


dotenv.config();

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY);
const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash" });


// Generates an 8-character alphanumeric ID
exports.generateRandomId = () => {
  return crypto.randomBytes(4).toString("hex").toUpperCase();
};

// Generates a 10-digit numeric ID
exports.generateVideoRandomId = () => {
  return Array.from({ length: 10 }, (_, i) =>
    i === 0
      ? crypto.randomInt(1, 10) // Ensure the first digit is not zero
      : crypto.randomInt(0, 10)
  ).join("");
};

exports.generateSingleQuestion = async (role, previousQA, skills = ["general"], preferredType = null) => {
  try {
    if (!role) {
      throw new Error("Role is required for question generation");
    }

    // Clean and prepare skills array
    const skillsList = (Array.isArray(skills) ? skills : [skills])
      .filter(skill => skill && typeof skill === 'string')
      .map(skill => skill.trim())
      .filter(skill => skill.length > 0);

    if (skillsList.length === 0) {
      skillsList.push("general");
    }

    // Create chat history from previous Q&A
    const history = previousQA.map(qa => [
      {
        role: "user",
        parts: [{ text: qa.question }]
      },
      {
        role: "model",
        parts: [{ text: qa.answer === null || qa.answer === undefined ? "No response" : qa.answer }]
      }
    ]).flat();

    // Create chat instance with history
    const chat = model.startChat({
      history,
      generationConfig: {
        temperature: 0.7,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 1024,
      },
    });

    const prompt = `Generate the next interview question for role: ${role}

Previous Q&A History:
${previousQA.map(qa =>
  `Question (${qa.type}): ${qa.question}
Answer: ${qa.answer}`
).join('\n\n')}

Return ONLY a JSON object in this format, NO other text:
{
  "question": "Ask a relevant ${preferredType || 'any'} question based on previous answers",
  "type": "${preferredType || 'technical'}",
  "keywords": ["skills", "actually", "used"],
  "difficulty": "medium",
  "follow_up": "A specific follow-up based on their likely answer",
  "time_limit": "2 minutes"
}

Required Skills: ${skillsList.join(", ")}

Guidelines:
1. Generate questions that build on previous answers
2. Use skills from previous responses
3. Keep questions focused and specific
4. Make follow-ups contextual
5. Return ONLY valid JSON, no extra text`;

    try {
      const result = await chat.sendMessage(prompt);
      const text = result.response.text();
      
      // Clean and parse response text
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON object found in response');
      }

      let jsonText = jsonMatch[0];
      jsonText = jsonText
        .replace(/```(?:json)?\s*|\s*```/g, '')  // Remove markdown
        .replace(/\n/g, ' ')  // Convert newlines to spaces
        .replace(/,\s*}/g, '}')  // Remove trailing commas
        .replace(/,\s*]/g, ']'); // Remove trailing commas in arrays

      let parsed = JSON.parse(jsonText);

      // Validate and normalize question data
      const question = {
        question: String(parsed.question || '').trim(),
        type: String(parsed.type || preferredType || 'technical').toLowerCase(),
        keywords: Array.isArray(parsed.keywords) ? parsed.keywords.map(k => String(k).trim()) : [role],
        difficulty: ['easy', 'medium', 'hard'].includes(parsed.difficulty) ? parsed.difficulty : 'medium',
        time_limit: String(parsed.time_limit || '2 minutes'),
        follow_up: parsed.follow_up || null
      };

      // Validate critical fields
      if (!question.question) {
        throw new Error('Generated question text is empty');
      }

      return question;

    } catch (error) {
      console.error("Question generation error:", error);
      
      // Return appropriate fallback question
      return {
        question: preferredType === 'technical'
          ? `What technical challenges have you faced while working with ${skillsList[0]}?`
          : `How do you handle challenging situations in your role as a ${role}?`,
        type: preferredType || 'technical',
        keywords: [skillsList[0], role, 'problem-solving'],
        difficulty: 'medium',
        time_limit: '2 minutes',
        follow_up: null,
        _fallback: true,
        _error: error.message
      };
    }
  } catch (error) {
    console.error("Critical error in generation:", error);
    return {
      question: `Tell me about your experience with ${role}`,
      type: preferredType || 'behavioral',
      keywords: [role, 'experience'],
      difficulty: 'medium',
      time_limit: '2 minutes',
      follow_up: null,
      _fallback: true,
      _error: 'Critical error: ' + error.message
    };
  }
};

exports.generateQuestions = async (role, skills, entry_level, job_type, quantity = 5) => {
  // Validate input parameters
  if (!role || !skills || skills.length === 0) {
    throw new Error("Role and skills are required");
  }

  const prompt = `Create a ${quantity}-question interview set. Return only this JSON structure, no other text:

{
  "questions": [
    {
      "question": "What is your experience with [specific technology]?",
      "type": "technical",
      "keywords": ["relevant", "skills"],
      "difficulty": "medium",
      "time_limit": "2 minutes"
    }
  ]
}

Details:
Role: ${role}
Skills: ${skills.join(", ")}
Level: ${entry_level ? "Entry-Level" : "Experienced"}
Company: ${job_type}

Distribution:
- ${Math.ceil(quantity * 0.6)} Technical questions
- ${Math.floor(quantity * 0.2)} Behavioral questions
- 1 System design question

Rules:
- Use simple punctuation only
- No special characters
- Keep questions brief
- Include relevant keywords
- Focus on ${job_type === "Startup" ? "versatility" : "process"}`;

  try {
    const result = await model.generateContent(prompt);
    const text = result.response.text();
    
    try {
      // Get response and clean it
      const cleaned = cleanJSONResponse(text);
      const parsedData = typeof cleaned === 'string' ? JSON.parse(cleaned) : cleaned;
      
      // Ensure we have a questions array
      if (!parsedData.questions || !Array.isArray(parsedData.questions)) {
        console.error('Invalid response:', parsedData);
        throw new Error('Invalid response format: Missing questions array');
      }

      // Normalize and log each question
      const normalizedQuestions = parsedData.questions.map((q, i) => {
        return {
          question: String(q.question || '').trim() ||
            `${i < Math.ceil(quantity * 0.6) ? 'Technical' : 'Behavioral'} question for ${role}`,
          type: ['technical', 'behavioral', 'system design'].includes(q.type?.toLowerCase())
            ? q.type.toLowerCase()
            : i < Math.ceil(quantity * 0.6) ? 'technical' : 'behavioral',
          keywords: Array.isArray(q.keywords) ?
            q.keywords.map(k => String(k).trim()) :
            [role, ...skills],
          difficulty: ['easy', 'medium', 'hard'].includes(q.difficulty?.toLowerCase())
            ? q.difficulty.toLowerCase() : 'medium',
          time_limit: String(q.time_limit || '2 minutes'),
          follow_up: q.follow_up || null
        };
        
      });

      return {
        questions: normalizedQuestions
      };
    } catch (parseError) {
      console.error('Error parsing questions:', parseError);
      throw new Error('Failed to parse questions data');
    }

  } catch (error) {
    console.error("Error generating questions:", error);
    
    // Generate fallback questions
    const fallbackQuestions = Array.from({ length: quantity }, (_, i) => ({
      question: i < Math.ceil(quantity * 0.6)
        ? `Describe your experience with ${skills[i % skills.length] || 'relevant technologies'}`
        : `How do you handle ${['challenging situations', 'team conflicts', 'deadlines'][i % 3]} in your role?`,
      type: i < Math.ceil(quantity * 0.6) ? 'technical' : 'behavioral',
      keywords: [skills[i % skills.length] || role, 'experience', 'problem-solving'],
      difficulty: 'medium',
      time_limit: '2 minutes'
    }));

    return {
      questions: fallbackQuestions,
      _fallback: true,
      _error: error.message
    };
  }
};

exports.generateScores = async (InterviewObject) => {
  // Validate input
  if (!InterviewObject || !Array.isArray(InterviewObject) || InterviewObject.length === 0) {
    console.warn("Invalid interview data provided");
    return defaultScoreObject("Invalid interview data format");
  }

  if (!process.env.GOOGLE_API_KEY) {
    console.warn("Google API key not found, using default scoring");
    return defaultScoreObject("API configuration issue");
  }

    // Clean and validate interview data
    const cleanedInterviewData = InterviewObject.map(q => {
    return {
      question: String(q.question || '').trim(),
      keywords: Array.isArray(q.keywords) ? q.keywords.join(', ') : 'none provided',
      type: String(q.type || 'general').toLowerCase(),
      answer: q.answer // Keep answer exactly as is, no modification
    };
  });

  const prompt = `You are an AI interview evaluator. Evaluate these responses and scores must be numbers between 0-10.

Return EXACTLY this JSON structure with no other text or formatting:

{
  "score": {
    "Skill_Score": 7,
    "Communication_Score": 7,
    "Overall_Score": 7
  },
  "Suggestions": "Specific feedback on technical and communication skills"
}

Evaluate these responses:
${cleanedInterviewData.map((q, index) =>
  `Question ${index + 1} (${q.type}): ${q.question}
   Expected Keywords: ${q.keywords}
   Answer: ${q.answer === null || q.answer === undefined || q.answer === "null" ? "Not provided" : q.answer}
   
   Evaluate:
   - Technical accuracy
   - Communication clarity
   - Use of keywords`
).join('\n\n')}

Guidelines:
1. Score technical accuracy in Skill_Score
2. Score communication in Communication_Score
3. Overall_Score should reflect both aspects
4. All scores must be 0-10 numbers
5. Provide specific, actionable feedback`;

  try {
    // Generate and process response
    const result = await model.generateContent(prompt);
    const text = result.response.text();
    
    // Clean and parse response
    const cleaned = cleanJSONResponse(text);
    let parsedData = typeof cleaned === 'string' ? JSON.parse(cleaned) : cleaned;

    // Validate score structure
    if (!parsedData || typeof parsedData !== 'object') {
      console.error("Invalid response format:", parsedData);
      return defaultScoreObject("Invalid response format");
    }

    // Ensure we have a valid score object
    const scores = {
      score: {
        Skill_Score: parseFloat(parsedData.score?.Skill_Score) || 5,
        Communication_Score: parseFloat(parsedData.score?.Communication_Score) || 5,
        Overall_Score: parseFloat(parsedData.score?.Overall_Score) || 5
      },
      Suggestions: parsedData.Suggestions || "Score processed. No specific feedback available."
    };

    // Normalize scores to valid range (0-10)
    scores.score = Object.entries(scores.score).reduce((acc, [key, value]) => {
      acc[key] = Math.min(10, Math.max(0, value));
      return acc;
    }, {});

    return scores;

  } catch (error) {
    console.error("Error in score generation:", error);
    console.error("Score generation error details:", {
      error: error.message,
      lastAnswer: cleanedInterviewData[cleanedInterviewData.length - 1]?.answer,
      answerTypes: cleanedInterviewData.map(d => typeof d.answer)
    });
    return defaultScoreObject("Score generation error");
  }
};

function isValidScoreObject(obj) {
  return (
    obj &&
    typeof obj === 'object' &&
    obj.score &&
    typeof obj.score === 'object' &&
    typeof obj.score.Skill_Score === 'number' &&
    typeof obj.score.Communication_Score === 'number' &&
    typeof obj.score.Overall_Score === 'number' &&
    typeof obj.Suggestions === 'string'
  );
}

function defaultScoreObject(reason) {
  return {
    score: {
      Skill_Score: 5,
      Communication_Score: 5,
      Overall_Score: 5
    },
    Suggestions: `Note: Using default scoring due to processing limitations. Reason: ${reason}`,
    _default: true
  };
}

exports.generateMockResult = async (InterviewObject) => {
  try {
    if (!Array.isArray(InterviewObject) || InterviewObject.length === 0) {
      throw new Error("Invalid interview data format");
    }

    const prompt = `Evaluate these interview responses and return a JSON object.

Format your response exactly like this with no other text:
{
  "score": {
    "Skill_Score": 7,
    "Communication_Score": 8,
    "Overall_Score": 7
  },
  "evaluation": [
    {
      "Question": "What is your experience with [skill]?",
      "Your_Answer": "Candidate's actual response",
      "Expected_Answer": "Key points that should have been covered"
    }
  ],
  "Suggestions": "Brief constructive feedback"
}

Responses to evaluate:
${InterviewObject.map((q, idx) =>
  `Question ${idx + 1}: ${q.question}
Type: ${q.type || 'general'}
Expected Keywords: ${Array.isArray(q.keywords) ? q.keywords.join(", ") : 'none'}
Answer: ${typeof q.answer === 'string' ? q.answer.trim() :
        q.answer === null || q.answer === undefined || q.answer === "null" ? "Not provided" :
        String(q.answer)}
Computed Type: ${typeof q.answer}
Validation: isNull=${q.answer === null}, isUndefined=${q.answer === undefined}, isNullString=${q.answer === "null"}`
).join("\n\n")}

Rules:
- Return only pure JSON
- Use simple text in strings
- Scores must be 0-10
- Keep non-null answers verbatim
- Show "Not provided" for null/undefined/empty answers
- Include answer validation in response
- Provide specific, actionable feedback`;

    // Get model response
    const result = await model.generateContent(prompt);
    const text = result.response.text();

    // Extract and clean JSON
    let cleaned = cleanJSONResponse(text);

    // Parse and validate structure
    const data = typeof cleaned === 'string' ? JSON.parse(cleaned) : cleaned;
    if (!data || typeof data !== 'object') {
      throw new Error('Invalid response format: Expected object');
    }

    // Normalize scores and structure
    return {
      score: {
        Skill_Score: Math.min(10, Math.max(0, Number(data.score?.Skill_Score) || 5)),
        Communication_Score: Math.min(10, Math.max(0, Number(data.score?.Communication_Score) || 5)),
        Overall_Score: Math.min(10, Math.max(0, Number(data.score?.Overall_Score) || 5))
      },
      evaluation: Array.isArray(data.evaluation) ? data.evaluation.map((e, idx) => {
        const originalAnswer = InterviewObject[idx]?.answer;
        return {
          Question: String(e.Question || ''),
          Your_Answer: (() => {
            return originalAnswer === null ||
                   originalAnswer === undefined ||
                   originalAnswer === "null" ||
                   (typeof originalAnswer === 'string' && originalAnswer.trim() === "")
              ? null
              : originalAnswer;
          })(),
          Expected_Answer: String(e.Expected_Answer || '')
        };
      }) : [],
      Suggestions: String(data.Suggestions || "Score generated with automatically processed feedback")
    };

  } catch (error) {
    console.error("Error in mock result generation:", error);
    return {
      score: {
        Skill_Score: 5,
        Communication_Score: 5,
        Overall_Score: 5
      },
      evaluation: [],
      Suggestions: `Interview processing encountered an error: ${error.message}. Using default scoring.`
    };
  }
};

exports.resumeStripper = async (pdfText) => {
  try {
    const prompt = `Extract professional information from this resume and return it in the following JSON format:
{
  "personal_info": {
    "name": "",
    "email": "",
    "phone": "",
    "location": ""
  },
  "education": [
    {
      "degree": "",
      "institution": "",
      "year": ""
    }
  ],
  "experience": [
    {
      "title": "",
      "company": "",
      "duration": "",
      "responsibilities": []
    }
  ],
  "skills": [],
  "certifications": []
}

Resume text:`;

    const result = await model.generateContent(prompt + pdfText);
    const response = await result.response;
    const cleaned = cleanJSONResponse(response.text());
    return JSON.parse(cleaned);

  } catch (error) {
    console.error("Error processing resume:", error);
    throw new Error("Failed to process resume");
  }
};

exports.jobDescStripper = async (pdfBuffer) => {
  try {
    const prompt = await fs.promises.readFile(
      "utils/files/jobDescPrompt.txt",
      "utf8"
    );
    if (!prompt) throw new Error("Prompt file is empty");

    const extractedText = await pdf(pdfBuffer);
    const cleanPdfText = extractedText.text.trim();
    if (!cleanPdfText) throw new Error("Extracted text from PDF is empty");

    const result = await model.generateContent(`${prompt}\n\n${cleanPdfText}`);
    const response = await result.response;
    const cleaned = cleanJSONResponse(response.text());
    return JSON.parse(cleaned);

  } catch (error) {
    console.error("Error processing job description:", error);
    throw new Error("Failed to process job description");
  }
};

function cleanJSONResponse(text) {
  try {
    // First try direct parse
    try {
      const parsed = JSON.parse(text);
      return parsed;
    } catch (e) {
    }

    // Extract JSON object
    const jsonMatch = text.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('No JSON object found in response');
    }

    // Clean the text
    let cleanText = jsonMatch[0]
      .replace(/```(?:json)?|```/g, '')  // Remove code blocks
      .replace(/[\n\r]/g, ' ')           // Remove newlines
      .replace(/\s+/g, ' ')              // Normalize spaces
      .replace(/,\s*([}\]])/g, '$1')     // Remove trailing commas
      .trim();

    // Try parsing cleaned text
    try {
      const parsed = JSON.parse(cleanText);
      return parsed;
    } catch (parseError) {
      throw parseError;
    }
  } catch (error) {
    console.error("JSON cleaning error:", error);
    throw error;
  }
}
