import React from 'react'
import CustomNavbar from './components/CustomNavbar';
import { useNavigate } from "react-router-dom";

import { FaTachometerAlt, FaUser, FaCog } from 'react-icons/fa';
import { HiCollection, HiHome, HiOutlineSparkles, HiSearch } from 'react-icons/hi';
import { MdBarChart } from 'react-icons/md';
import ProfileSearchUI from './ProfilesUI';

const links = [
    { text: 'Dashboard', url: '/employer/', icon: <HiHome /> },
    { text: 'Track Candidates', url: '/employer/track-candidates', icon: <MdBarChart className="h-6 w-6" /> },
    {
    text: (
            <span className='flex items-center'>
                Source with AI <span className="bg-orange-400 text-white text-xs font-semibold rounded-full px-2 ml-2">Beta</span>
            </span>
    ), 
    url: '/employer', 
    icon: <HiOutlineSparkles />, 
    className: 'text-orange-400 font-bold hover:text-orange-500' 
},
];


const ProfileSearch = () => {
const [open, setOpen] = React.useState(true);

  return (
    <>
    <CustomNavbar links={links} />
    <ProfileSearchUI />
    </>
    
  )
}

export default ProfileSearch