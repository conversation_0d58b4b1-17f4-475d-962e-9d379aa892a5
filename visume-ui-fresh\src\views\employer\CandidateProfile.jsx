import React from "react";
import { useEffect } from "react";
// import { Home, FileText, CheckCircle, Clock } from "lucide-react";
import { Home, FileText, CheckCircle, Clock, Unlock, Star, Mail, Phone, Cake, DollarSign,MapPin,Briefcase,Download,AlertCircle,CreditCard,Lock, X,
  MessageCircle, BarChart2,} from "lucide-react";
import { useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import Loader from "components/Loader";
import CustomNavbar from "./components/CustomNavbar";
import {HiCollection,HiHome,HiOutlineSparkles,HiSearch,} from "react-icons/hi";
import { MdBarChart } from "react-icons/md";
import { LuIndianRupee } from "react-icons/lu";
import toast from "react-hot-toast";

const CandidateProfile = () => {

  const links = [
    { text: "Dashboard", url: "/employer/", icon: <HiHome /> },
    { text: "Track Candidates", url: "/employer/track-candidates",icon: <MdBarChart className="h-6 w-6" />,},
    { text: (
        <span className="flex items-center">
          Source with AI{" "}
          <span className="ml-2 rounded-full bg-orange-400 px-2 text-xs font-semibold text-white">
            Beta  
          </span>
        </span>
      ),
      url: "/employer", icon: <HiOutlineSparkles />,
      className: "text-orange-400 font-bold hover:text-orange-500",
    },
  ];
  const [subPopup, setSubPopup] = useState(false);

  const handleResumeDownload = () => {
    setSubPopup(true);
  };
  const [detailsBlur, setDetailsBlur] = useState(true);
  const [profileData, setProfileData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  let { vpid } = useParams();
  const [candidateProf, setCandidateProf] = useState(null);
  const [strippedResumeJson, setStrippedResumeJson] = useState(null);
  // let strippedResumeJSON = null;

  useEffect(() => {
    const fetchProfileData = async () => {
      try {
        // setLoading(true);

        // First API call with dynamic `vpid`
        const response = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/video-resume-data/${vpid}`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
          }
        );

        if (!response.ok) {
          throw new Error("Network response was not ok");
        }

        const data = await response.json();
        setProfileData(data.data);

        // Second API call using the `cand_id` from the first response
        const additionalResponse = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/candidate/${
            data.data.cand_id
          }`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
          }
        );

        if (!additionalResponse.ok) {
          throw new Error("Network response for additional data was not ok");
        }

        const additionalData = await additionalResponse.json();
        const candidateProfile = additionalData.candidateProfile[0];
        setCandidateProf(candidateProfile);

        // Handle double-encoded JSON string for `stripped_resume`
        let stripped_resume = candidateProfile.stripped_resume;

        // Step 1: Clean the string
        stripped_resume = stripped_resume?.replace(/^"(.*)"$/, "$1"); // Remove quotes
        stripped_resume = stripped_resume?.replace(/\\"/g, '"'); // Replace escaped quotes
        stripped_resume = stripped_resume?.replace(/\\\\/g, "\\"); // Replace escaped backslashes

        // Step 2: Parse the cleaned string into a valid JSON object
        const stripped_resume_json =
          stripped_resume && JSON.parse(stripped_resume);
        setStrippedResumeJson(stripped_resume_json);
        if (!stripped_resume_json) {
          toast.error("Resume Not Uploaded Properly Details are Missing.");
        }
        // Debug: log the parsed JSON object
        // console.log("Parsed stripped_resume:", stripped_resume_json);

        // Destructure to access the `professionalSummary` and `skills`
        // const { professionalSummary, skills } = stripped_resume_json;
        // console.log("Professional Summary:", professionalSummary);
        // console.log("Skills:", skills);
      } catch (error) {
        console.error("Failed to fetch profile data:", error);
        setError("Failed to fetch profile data");
      } finally {
        setLoading(false);
      }
    };

    // Fetch profile data if `vpid` exists
    if (vpid) {
      fetchProfileData();
    }
  }, [vpid]);



  // Extract data only when profileData is available
  // const { role, skills, salary, score } = profileData || {};
  // let parsedSalary = null;
  // let parsedScore = null;

  // if (salary) {
  //   parsedSalary = JSON.parse(salary);
  // }

  // if (score) {
  //   parsedScore = JSON.parse(score);
  // }

  // const languages = [
  //   { code: "TR", name: "Turkish", level: "Native Language" },
  //   { code: "EN", name: "English", level: "Advanced" },
  //   { code: "DE", name: "German", level: "Intermediate" },
  // ];

  const profiles = [
    {
      name: "John Doe",
      role: "Front-End Developer",
      rating: 4.5,
      image: "https://via.placeholder.com/50",
    },
    {
      name: "Jane Smith",
      role: "UX Designer",
      rating: 4.8,
      image: "https://via.placeholder.com/50",
    },
    {
      name: "Alex Johnson",
      role: "Full-Stack Developer",
      rating: 4.2,
      image: "https://via.placeholder.com/50",
    },
  ];
  const navigate = useNavigate();
  
  return (
    <>
      {loading ? (
        <Loader />
      ) : (
        <div className="min-h-screen bg-gray-100 p-3">

          <CustomNavbar links={links} />
          
          <div className=" mx-auto">
            
            <div className="min-h-screen bg-gray-100  p-4">
              {/* Back Button */}
              <div className="mx-6 mb-2 max-w-3xl">
                <button
                  onClick={() => {
                    const previousUrl = localStorage.getItem("previousUrl"); // Retrieve the stored URL
                    if (previousUrl) {
                      window.location.href = previousUrl; // Navigate back to the previous URL
                    } else {
                      navigate("/profile-search/filterCandidate");
                    }
                  }}
                  className="flex items-center text-sm font-semibold text-brand-600"
                >
                  &larr; Back to Candidates
                </button>
              </div>

              <div className="grid grid-cols-1 gap-2 space-y-2 lg:grid-cols-5 lg:space-y-0">

                {/* Left Column (Profile, Personal Info, and Additional Cards) */}
                <div className="space-y-0 lg:col-span-3">
                  {/* <div className="max-w-3xl mx-auto p-6"> */}
                  {/* Profile Section */}
                  <div className="max-w-full space-y-2  p-0">

                    <div className="rounded-2xl bg-white p-4">

                      <div className="flex items-center justify-between  space-x-4">
                        
                        <div className="flex flex-col">
                          {/* <img
                            src={`${import.meta.env.VITE_APP_HOST}/${
                              candidateProf?.profile_picture ||
                              "default-profile-pic.jpg"
                            }`} // Use a default if profile_picture is undefined
                            alt="Profile"
                            className="h-8 w-8 rounded-full object-cover"
                            onError={(e) => {
                              e.target.src =
                                "/@fs/C:/Users/<USER>/Desktop/ZoomJobs/zoomjobs-frontend/src/assets/img/avatars/avatar4.png"; // Fallback URL
                            }}
                          /> */}
                          <h1 className="text-xl font-semibold">
                            {candidateProf.cand_name}
                          </h1>

                          <div className="flex  gap-3">

                          <p className="mt-1 flex items-center text-gray-600">
                            <MapPin className="mr-0.5 inline h-4 w-4" />
                            <span className="text-[14px]">
                              {candidateProf.preferred_location}
                            </span>
                          </p>

                          <p className="mt-1 flex items-center text-gray-600">
                            <LuIndianRupee className="mr-0.3 inline h-3.5 w-3.5" />
                            <span className="text-[14px]">
                              Current:{" "}
                              {JSON.parse(profileData.salary).current || "N/A"}{" "}
                              LPA | Expected:{" "}
                              {JSON.parse(profileData.salary).expected || "N/A"}{" "}
                              LPA
                            </span>
                          </p>

                        </div>
                          
                        </div>
                        
                        <div>
                          <p className="font-poppins font-semibold text-brand-600"> {profileData.role}</p>
                          <p className="font-poppins font-semibold text-brand-600 text-sm">
                              <span className="font-semibold text-brandLinear">Skills - </span> {profileData.skills}
                          </p>

                        </div>

                      </div>

                      {/* {console.log(profileData)}
                      {console.log(profileData.questions)} */}

                      {profileData.video_url ? (
                        <video controls={true}  className="mt-4 h-[425px] w-full rounded-lg object-contain" src={profileData.video_url} />
                      ) : (
                        <div className="mt-4 flex h-[425px] w-full items-center justify-center rounded-lg bg-gray-500  text-white">
                          No Video Url Found
                        </div>
                      )}
                      <div className="mt-4 flex flex-col items-center justify-between sm:flex-row">
                        
                        {/* <div>
                          <p className="mt-1 flex items-center text-gray-600">
                            <MapPin className="mr-1 inline h-4 w-4" />
                            <span className="text-sm">
                              {candidateProf.preferred_location}
                            </span>
                          </p>
                          <p className="mt-1 flex items-center text-gray-600">
                            <DollarSign className="mr-1 inline h-4 w-4" />
                            <span className="text-sm">
                              Current:{" "}
                              {JSON.parse(profileData.salary).current || "N/A"}{" "}
                              LPA | Expected:{" "}
                              {JSON.parse(profileData.salary).expected || "N/A"}{" "}
                              LPA
                            </span>
                          </p>
                        </div> */}

                      <div className="rounded-2xl bg-white p-4 shadow-sm">

                                 <h2 className="mb-2 text-left text-xl font-semibold">Visume Score</h2>

                     <div className="grid gap-3  sm:grid-cols-3 ">

                                   {profileData?.score && (
                       <>
        <ScoreCard
            icon={ <Star className="h-5 w-5 text-yellow-500" />}
            title="Skill"
            score={JSON.parse(profileData.score)?.score ?.Skill_Score || 0}  />

      <ScoreCard
        icon={<MessageCircle className="h-5 w-5 text-blue-500" />}
        title="Communication"
        score={JSON.parse(profileData.score)?.score ?.Communication_Score || 0 }/>

      <ScoreCard
        icon={<BarChart2 className="h-5 w-5 text-green-500" /> }
        title="Overall"
        score={JSON.parse(profileData.score)?.score ?.Overall_Score || 0}
      />

    </>
  )}
</div>

{/* <div className="mt-2 h-56 w-full rounded-lg bg-gray-500 p-2 text-white">
Visume
</div> */}

{/* <div className="mb-3 mt-2 flex items-center justify-between">
  <h2 className="text-xl font-semibold">Skills</h2>
</div>
<div className="flex flex-wrap gap-2">
  {profileData.skills.split(",").map((skill, index) => (
    <span
      key={index}
      className="rounded-full bg-gray-100 px-3 py-1 text-sm text-gray-800"
    >                      
      {skill.trim()}
    </span>
  ))}
</div> */}

{/* <div className="mt-6">
<h3 className="mb-2 text-lg font-medium">Suggestions</h3>
<p className="text-sm text-gray-600">
{JSON.parse(profileData.score).Suggestions}
</p>
</div> */}
                      </div>
                        
                      <div className="mt-4 flex space-x-2 sm:mt-0">
                          <button className="flex h-10 items-center justify-center rounded-md bg-brand-600 p-2 text-center text-white">
                            <Unlock className="mr-1 inline h-4 w-4" />
                            <span className="text-sm">Unlock</span>
                          </button>
                          <button className="flex h-10 items-center justify-center rounded-md bg-brand-100 p-2 text-center text-brand-600">
                            <Star className="mr-1 inline h-4 w-4" />
                            <span className="text-sm">Shortlist</span>
                          </button>
                      </div>

                      </div>

                       {/* Professional Summary */}
                      <div className="mt-4 border-t border-gray-200 pt-4">
                        <h2 className="mb-2 flex items-center text-lg font-semibold">
                          <FileText className="mr-2 inline h-5 w-5" />
                          Professional Summary
                        </h2>
                        <p className="text-sm text-gray-600">
                          {strippedResumeJson?.professionalSummary || "N/A"}
                        </p>
                      </div>

                    </div>

                    {/* Skills */}
                    <section className=" rounded-2xl bg-white p-4 ">
                      <div className="mb-3 flex items-center justify-between">
                        <h2 className="text-xl font-semibold">Skills</h2>
                      </div>
                      <div className="flex flex-wrap gap-2">
                        {strippedResumeJson &&
                          Array.isArray(strippedResumeJson.skills) &&
                          strippedResumeJson.skills.map((skill, index) => (
                            <span
                              key={index}
                              className="rounded-full bg-gray-100 px-3 py-1 text-sm text-gray-800"
                            >
                              {skill.trim()}
                            </span>
                          ))}
                      </div>
                    </section>

                    {/* Experience Section */}
                    <div className="space-y-4 rounded-xl bg-white  p-4">
                      <div className="flex items-center justify-between">
                        <h2 className="text-xl font-semibold text-gray-800">
                          All Experience
                        </h2>
                      </div>
                      <div className="space-y-5">
                        {strippedResumeJson &&
                          strippedResumeJson.workExperience.map(
                            (experience, index) => (
                              <div key={index} className="space-y-3">
                                {/* Experience Item */}
                                <div className="flex items-start space-x-4">
                                  {/* Circular Image */}
                                  {/* <img
                                  src="https://via.placeholder.com/50"
                                  alt="Company Logo"
                                  className="h-10 w-10 rounded-full object-cover shadow-sm"
                                /> */}
                                  <div className="flex h-10 w-10 items-center justify-center rounded-full bg-brand-700 object-cover text-2xl text-white shadow-sm">
                                    {experience.company[0]}
                                  </div>
                                  {/* Experience Details */}
                                  <div className="flex flex-col space-y-1">
                                    {/* Company Name, Job Title, and Duration */}
                                    <div className="flex items-center space-x-2">
                                      <h3 className="text-lg font-semibold text-gray-800">
                                        {experience.company}
                                      </h3>
                                      <span className="text-sm text-gray-500">
                                        {experience.dates}
                                      </span>
                                    </div>

                                    <p className="text-sm font-medium text-brand-600">
                                      {experience.title}
                                    </p>

                                    {/* Responsibilities */}
                                    <ul className="ml-5 list-disc space-y-1 text-sm text-gray-600">
                                      {experience.responsibilities.map(
                                        (responsibility, index) => (
                                          <li key={index}>{responsibility}</li>
                                        )
                                      )}
                                    </ul>
                                  </div>
                                </div>

                                {/* Underline Separator */}
                                {strippedResumeJson &&
                                  index <
                                    strippedResumeJson.workExperience.length -
                                      1 && (
                                    <hr className="mt-3 border-t border-gray-200" />
                                  )}
                              </div>
                            )
                          )}
                      </div>
                    </div>

                    {/* Education Section */}
                    <div className="space-y-4 rounded-2xl bg-white p-4">
                      <div className="flex items-center justify-between">
                        <h2 className="text-xl font-semibold">Education</h2>
                      </div>
                      <div className="space-y-3">
                        {strippedResumeJson &&
                          strippedResumeJson.education.map((edu, index) => (
                            <div key={index} className="space-y-3">
                              {/* Education Item */}
                              <div className="flex items-start space-x-3">
                                {/* Circular Image */}
                                {/* <img
                                src="https://via.placeholder.com/50"
                                alt="Institution Logo"
                                className="h-10 w-10 rounded-full object-cover"
                              /> */}
                                <div className="flex h-10 w-10 items-center justify-center rounded-full bg-brand-700 object-cover text-2xl text-white shadow-sm">
                                  {edu.institution[0]}
                                </div>
                                {/* Education Details */}
                                <div className="flex flex-col ">
                                  {/* Institution Name and Type */}
                                  <div className="flex items-start space-x-2">
                                    <h3 className="text-md font-semibold">
                                      {edu.institution}
                                    </h3>
                                    <span className="rounded-full border border-blue-500 px-2 py-0.5 text-sm text-blue-500">
                                      Full-Time
                                    </span>
                                  </div>

                                  {/* Degree and Dates */}
                                  <div>
                                    <p className="text-sm text-gray-800">
                                      {edu.degree}
                                    </p>
                                    <p className="text-sm text-gray-400">
                                      {edu.dates} -{" "}
                                      {edu.location || edu.coursework}
                                    </p>
                                  </div>
                                </div>
                              </div>

                              {/* Underline Separator */}
                              {strippedResumeJson &&
                                index <
                                  strippedResumeJson.education.length - 1 && (
                                  <hr className="mt-3 rounded-full border border-gray-200" />
                                )}
                            </div>
                          ))}
                      </div>
                    </div>
                     
                     {/* All personel Information */}
                    <div className=" space-y-4 rounded-2xl bg-white p-6">
                      <div className="flex items-center justify-between">
                        <h2 className="text-xl font-semibold">
                          All Personal Information
                        </h2>
                      </div>

                      <div className="relative">
                        <div
                          className={`flex flex-col space-y-4 md:flex-row md:justify-between md:space-y-0 ${
                            detailsBlur ? "blur-md" : ""
                          }`}
                        >
                          <div className="flex flex-col space-y-3 md:space-y-3">
                            <div className="flex items-center space-x-2">
                              <Mail className="h-6 w-6 rounded-lg border border-gray-300 bg-gray-200 p-1" />
                              <p className="text-sm">
                                {candidateProf.cand_email}
                              </p>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Phone className="h-6 w-6 rounded-lg border border-gray-300 bg-gray-200 p-1" />
                              <p className="text-sm">
                                {candidateProf.cand_mobile}
                              </p>
                            </div>
                          </div>
                          {/* <div className="flex flex-col space-y-4 md:space-y-6"> */}
                          {/* <div className="flex items-center space-x-2">
                              <Cake className="h-6 w-6 rounded-lg border border-gray-300 bg-gray-200 p-1" />
                              <p className="text-sm">03 September 2000</p>
                            </div> */}

                          {/* <div className="flex items-center space-x-2">
                              <DollarSign className="h-6 w-6 rounded-lg border border-gray-300 bg-gray-200 p-1" />
                              <p className="text-sm">$24,000</p>
                            </div> */}
                          {/* </div> */}
                        </div>
                        {detailsBlur && (
                          <button
                            onClick={() => setSubPopup(true)}
                            className="absolute left-1/2 top-1/2 flex -translate-x-1/2 -translate-y-1/2 transform items-center space-x-2 rounded-full bg-brand-600 px-4 py-2 font-bold text-white transition duration-300 ease-in-out hover:bg-brand-700"
                            aria-label="Unlock candidate details"
                          >
                            <Lock className="h-4 w-4" />
                            <span>Unlock Details</span>
                          </button>
                        )}
                      </div>

                      {/* <hr /> */}

                      {/* <div className="flex items-center space-x-2">
                        <MapPin className="h-6 w-6 rounded-lg border border-gray-300 bg-gray-200 p-1" />
                        <p className="text-sm">
                          Istanbul, Izmir, Ankara, Turkey, US, Europe
                        </p>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Briefcase className="h-6 w-6 rounded-lg border border-gray-300 bg-gray-200 p-1" />
                        <p className="text-sm">
                          Remote, Full-Time, Part-Time, Internship, Freelance
                        </p>
                      </div> */}
                    </div>

                    {/* Resume Card */}
                    {/* <div className="space-y-4 rounded-2xl bg-white  p-6">
                      <h2 className="text-xl font-semibold">Resume</h2>

                      <div className="flex items-center justify-between">
                    
                        <div className="flex items-center space-x-2">
                          <FileText
                            className="rounded-lg border border-gray-300 bg-gray-200 p-1"
                            size={24}
                          />
                          <p className="text-sm font-semibold">
                            Resume-JeromeBell.pdf
                          </p>
                        </div>

                      
                        <button
                          className="flex items-center space-x-2 rounded-md bg-brand-600 p-2 text-white "
                          onClick={handleResumeDownload}
                        >
                          <Download size={18} />
                          <span className="text-sm">Download</span>
                        </button>
                      </div>
                    </div> */}

                      {/* Projects */}
                    <div className="space-y-4 rounded-xl bg-white p-4">
                      <div className="flex items-center justify-between">
                        <h2 className="text-xl font-semibold text-gray-800">
                          Projects
                        </h2>
                      </div>
                      <div className="space-y-5">
                        {strippedResumeJson &&
                          Array.isArray(strippedResumeJson.projects) &&
                          strippedResumeJson.projects.map((project, index) => (
                            <div key={index} className="space-y-3">
                              {/* Project Item */}
                              <div className="flex items-start space-x-4">
                                {/* Project Details */}
                                <div className="flex flex-col space-y-1">
                                  {/* Project Name */}
                                  <div className="flex items-center space-x-2">
                                    <h3 className="text-lg font-semibold text-gray-800">
                                      {project.name}
                                    </h3>
                                  </div>

                                  {/* Project Description */}
                                  <p className="text-sm text-gray-600">
                                    {project.description}
                                  </p>

                                  {/* Technologies Used */}
                                  <div className="flex flex-wrap space-x-2">
                                    {Array.isArray(project.technologies) &&
                                      project.technologies.map(
                                        (tech, index) => (
                                          <span
                                            key={index}
                                            className="rounded-lg bg-brand-500 px-2 py-1 text-xs font-thin text-white"
                                          >
                                            {tech}
                                          </span>
                                        )
                                      )}
                                  </div>
                                </div>
                              </div>

                              {/* Underline Separator */}
                              {index <
                                strippedResumeJson.projects.length - 1 && (
                                <hr className="mt-3 border-t border-gray-200" />
                              )}
                            </div>
                          ))}
                      </div>
                    </div>

                      {/* Languages */}
                    <section className="rounded-2xl bg-white p-6 ">
                      <div className="flex items-center justify-between">
                        <h2 className="text-xl font-semibold">Languages</h2>
                      </div>
                      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3">
                        {candidateProf.languages_known
                          .split(",")
                          .map((lang) => (
                            <div
                              key={lang.slice(0, 2).toUpperCase()}
                              className="flex items-center space-x-3 rounded-lg border border-b-gray-200 bg-white p-2"
                            >
                              <div className="flex h-9 w-9 items-center justify-center rounded-full bg-brand-600 p-2  text-white">
                                <span className="text-md font-semibold">
                                  {lang.slice(0, 2).toUpperCase()}
                                </span>
                              </div>
                              <div>
                                <p className="font-sm ">{lang}</p>
                              </div>
                            </div>
                          ))}
                      </div>
                    </section>

                  </div>

                </div>



                {/* Right Column (Active Positions, Career Status, etc.) */}
                <div className="max-w-full space-y-2 p-0  lg:col-span-2">

                  <div className="sticky top-[80px] space-y-2">
                    
                    <div className="rounded-2xl bg-white p-4 shadow-sm">

                         {JSON.parse(profileData.questions).map((questionObj, index) => (
                           <div key={index} className="border p-3  mb-2 rounded-lg">

                                 <div>
                                     <p className="text-[15px] text-gray-500 font-light">{index + 1 + " " + "."} {" "} {questionObj.question}</p>
                                     <p className="text-[14px] text-blueSecondary font-semibold">
                                         <span className="text-gray-800 text-[15px]" >Created at : </span>
                                          {new Date((profileData.created_at)).toISOString().slice(0,16).replace("T"," ")}
                                    </p>
                               </div>

                           </div>
                          ))}
            
                    </div>

                    <div className="rounded-2xl bg-white p-4">
                      <h2 className="mb-2 text-left  text-xl font-semibold">
                        Similar profiles
                      </h2>
                      {/* Active Positions content */}
                      <ul className="space-y-2">
                        {profiles.map((profile, index) => (
                          <li
                            key={index}
                            className="flex items-center space-x-4 rounded-lg border border-b-gray-200 bg-white p-3"
                          >
                            {/* <img
                              src={profile.image}
                              alt={`${profile.name}'s profile`}
                              className="h-12 w-12 rounded-full object-cover"
                            /> */}
                            <div className="flex h-10 w-10 items-center justify-center rounded-full bg-brand-700 object-cover text-2xl text-white shadow-sm">
                              {profile.name[0]}
                            </div>
                            <div className="flex-grow">
                              <h3 className="font-semibold">{profile.name}</h3>
                              <p className="text-sm text-brand-600">
                                {profile.role}
                              </p>
                            </div>
                            <div className="flex items-center">
                              <Star className="fill-current h-4 w-4 text-yellow-400" />
                              <span className="ml-1 text-sm font-medium">
                                {profile.rating}
                              </span>
                            </div>
                          </li>
                        ))}
                      </ul>

                      <button className="mt-2 w-full rounded-md bg-brand-600 py-2 text-white">
                        Show All
                      </button>
                    </div>
                    
                  </div>
                  
                </div>

              </div>

            </div>

            {subPopup && (
              <div className="fixed inset-0 z-50 flex items-center justify-center">
                <div
                  className="bg-black/20 absolute inset-0 backdrop-blur-sm"
                  onClick={() => setSubPopup(false)}
                />
                <div className="relative z-50 w-full max-w-md rounded-lg bg-white shadow-xl">
                  <div className="p-6">
                    <h2 className="mb-2 flex items-center text-2xl font-bold">
                      <AlertCircle className="mr-2 h-6 w-6 text-yellow-500" />
                      Unlock Profile Confirmation
                    </h2>
                    <p className="mb-4 text-gray-600">
                      Unlocking this profile will deduct 1 credit from your
                      credit balance.
                    </p>
                    <p className="mb-6 flex items-center">
                      Are you sure you want to proceed?
                    </p>
                    <div className="flex justify-end space-x-2">
                      <button
                        className="flex items-center px-4 py-2 text-gray-600 hover:text-gray-800"
                        onClick={() => setSubPopup(false)}
                      >
                        <X className="mr-1 h-4 w-4" />
                        Cancel
                      </button>
                      <button className="flex items-center rounded-md bg-brand-600 px-4 py-2 text-white hover:bg-brand-700">
                        <CreditCard className="mr-1 h-4 w-4" />
                        Unlock Now
                      </button>
                    </div>
                  </div>
                  <button
                    className="absolute right-2 top-2 text-gray-400 hover:text-gray-600"
                    onClick={() => setSubPopup(false)}
                  >
                    <X className="h-6 w-6" />
                    <span className="sr-only">Close</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </>
  );
};

export default CandidateProfile;

function ScoreCard({ icon, title, score }) {
  return (
    <div className="flex flex-col justify-between space-y-0 rounded-lg border p-2">
      <p className="break-words text-sm font-medium text-gray-500">{title}</p>
      <div className="flex items-center justify-between">
        {icon}
        <p className="text-2xl font-semibold">{score}</p>
      </div>
    </div>
  );
}
