{"version": 3, "sources": ["../../@smithy/property-provider/dist-es/ProviderError.js", "../../@smithy/property-provider/dist-es/CredentialsProviderError.js", "../../@smithy/property-provider/dist-es/memoize.js"], "sourcesContent": ["export class ProviderError extends Error {\n    constructor(message, options = true) {\n        let logger;\n        let tryNextLink = true;\n        if (typeof options === \"boolean\") {\n            logger = undefined;\n            tryNextLink = options;\n        }\n        else if (options != null && typeof options === \"object\") {\n            logger = options.logger;\n            tryNextLink = options.tryNextLink ?? true;\n        }\n        super(message);\n        this.name = \"ProviderError\";\n        this.tryNextLink = tryNextLink;\n        Object.setPrototypeOf(this, ProviderError.prototype);\n        logger?.debug?.(`@smithy/property-provider ${tryNextLink ? \"->\" : \"(!)\"} ${message}`);\n    }\n    static from(error, options = true) {\n        return Object.assign(new this(error.message, options), error);\n    }\n}\n", "import { ProviderError } from \"./ProviderError\";\nexport class CredentialsProviderError extends ProviderError {\n    constructor(message, options = true) {\n        super(message, options);\n        this.name = \"CredentialsProviderError\";\n        Object.setPrototypeOf(this, CredentialsProviderError.prototype);\n    }\n}\n", "export const memoize = (provider, isExpired, requiresRefresh) => {\n    let resolved;\n    let pending;\n    let hasResult;\n    let isConstant = false;\n    const coalesceProvider = async () => {\n        if (!pending) {\n            pending = provider();\n        }\n        try {\n            resolved = await pending;\n            hasResult = true;\n            isConstant = false;\n        }\n        finally {\n            pending = undefined;\n        }\n        return resolved;\n    };\n    if (isExpired === undefined) {\n        return async (options) => {\n            if (!hasResult || options?.forceRefresh) {\n                resolved = await coalesceProvider();\n            }\n            return resolved;\n        };\n    }\n    return async (options) => {\n        if (!hasResult || options?.forceRefresh) {\n            resolved = await coalesceProvider();\n        }\n        if (isConstant) {\n            return resolved;\n        }\n        if (requiresRefresh && !requiresRefresh(resolved)) {\n            isConstant = true;\n            return resolved;\n        }\n        if (isExpired(resolved)) {\n            await coalesceProvider();\n            return resolved;\n        }\n        return resolved;\n    };\n};\n"], "mappings": ";AAAO,IAAM,gBAAN,MAAM,uBAAsB,MAAM;AAAA,EACrC,YAAY,SAAS,UAAU,MAAM;AADzC;AAEQ,QAAI;AACJ,QAAI,cAAc;AAClB,QAAI,OAAO,YAAY,WAAW;AAC9B,eAAS;AACT,oBAAc;AAAA,IAClB,WACS,WAAW,QAAQ,OAAO,YAAY,UAAU;AACrD,eAAS,QAAQ;AACjB,oBAAc,QAAQ,eAAe;AAAA,IACzC;AACA,UAAM,OAAO;AACb,SAAK,OAAO;AACZ,SAAK,cAAc;AACnB,WAAO,eAAe,MAAM,eAAc,SAAS;AACnD,2CAAQ,UAAR,gCAAgB,6BAA6B,cAAc,OAAO,KAAK,IAAI,OAAO;AAAA,EACtF;AAAA,EACA,OAAO,KAAK,OAAO,UAAU,MAAM;AAC/B,WAAO,OAAO,OAAO,IAAI,KAAK,MAAM,SAAS,OAAO,GAAG,KAAK;AAAA,EAChE;AACJ;;;ACpBO,IAAM,2BAAN,MAAM,kCAAiC,cAAc;AAAA,EACxD,YAAY,SAAS,UAAU,MAAM;AACjC,UAAM,SAAS,OAAO;AACtB,SAAK,OAAO;AACZ,WAAO,eAAe,MAAM,0BAAyB,SAAS;AAAA,EAClE;AACJ;;;ACPO,IAAM,UAAU,CAAC,UAAU,WAAW,oBAAoB;AAC7D,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,aAAa;AACjB,QAAM,mBAAmB,YAAY;AACjC,QAAI,CAAC,SAAS;AACV,gBAAU,SAAS;AAAA,IACvB;AACA,QAAI;AACA,iBAAW,MAAM;AACjB,kBAAY;AACZ,mBAAa;AAAA,IACjB,UACA;AACI,gBAAU;AAAA,IACd;AACA,WAAO;AAAA,EACX;AACA,MAAI,cAAc,QAAW;AACzB,WAAO,OAAO,YAAY;AACtB,UAAI,CAAC,cAAa,mCAAS,eAAc;AACrC,mBAAW,MAAM,iBAAiB;AAAA,MACtC;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO,OAAO,YAAY;AACtB,QAAI,CAAC,cAAa,mCAAS,eAAc;AACrC,iBAAW,MAAM,iBAAiB;AAAA,IACtC;AACA,QAAI,YAAY;AACZ,aAAO;AAAA,IACX;AACA,QAAI,mBAAmB,CAAC,gBAAgB,QAAQ,GAAG;AAC/C,mBAAa;AACb,aAAO;AAAA,IACX;AACA,QAAI,UAAU,QAAQ,GAAG;AACrB,YAAM,iBAAiB;AACvB,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AACJ;", "names": []}