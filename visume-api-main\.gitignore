# Node.js dependencies
/node_modules

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# Environment variable files
.env
.env.test
.env.production

# dotenv environment variables file
.env.local
.env.*.local

# Build output directories
build
dist
out

# Temporary files
*.tmp
*.temp
/uploads

# IDE and Editor settings
.vscode/
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
.DS_Store

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# OS generated files
Thumbs.db
*.DS_Store
