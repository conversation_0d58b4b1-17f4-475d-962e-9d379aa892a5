import React from "react";

const VidProfPopup = ({ toggleVideoProfilePopup }) => {
  const strippedResume = {
    professionalSummary:
      "Experienced software engineer with a strong track record of delivering high-quality software solutions. Skilled in a wide range of technologies, including Javascript, Node.js, Vue.js, and frameworks like Feathers.js and Quasar.js. Proven ability to architect and develop complex applications, integrate with various systems, and optimize for performance and scalability.",
    workExperience: [
      {
        company: "Icolabora",
        title: "Senior Software Engineer",
        dates: "Jan 2022 - Present",
        responsibilities: [
          "Reduced the implementation time of a new solution for the end customer by 20x: developing a new multitenacy product for backoffice service, making it possible to serve thousands of new customers on the same stack, using javascript, node.js, feathers.js, vue.js, quasar.js, socket.io, kubernets, camunda, keycloack, microservices...",
          "Reduced the number of open tickets by 15%: developing an application that allows users to configure their users, groups and keycloak roles in a multitenancy environment, using javascript, node.js, vue.js, mysql and nginx;",
          "Planned, architected and developed a complete new product: Even transitioning technologies, in just 6 months I managed to stand out within the team, becoming technical leader of the company’s main focus on launching a new product, using kanban, git, agile methodology, team work and assertive communication.",
        ],
      },
      {
        company: "View Sites",
        title: "Senior Software Engineer",
        dates: "Jan 2019 - Dez 2021",
        responsibilities: [
          "Increased the number of customers by 37%: developing the integration feature with several marketplaces;",
          "The platform’s loading speed was increased by 70%: by applying caching techniques, such as dynamically creating pages and storing them in cache to avoid unnecessary reprocessing;",
          "Increased the number of purchase conversions by 18%: by creating integration for social login Google/Facebook.",
        ],
      },
      {
        company: "Aldeia Nissi",
        title: "Project Development Manager",
        dates: "Jan 2018 - Dez 2018",
        responsibilities: [
          "Developed a complete platform to manage all donations for NGOs: allowing user registration and recurring collection of donations through different payment methods.",
          "Increasing annual revenue by 3000%: designing and developing software for fundraising and recurring collection of donations to an NGO",
        ],
      },
      {
        company: "View Sites",
        title: "Full Stack Engineer",
        dates: "Jan 2012 - Dec 2017",
        responsibilities: [
          "Reduced website development time from 30 days to 1 business day: developing a complete platform with more than 100 templates for customization, using php, mysql, nginx and javascript;",
          "Developed more than 800 projects: on the platform I developed to create a zero code website;",
          "Reduced the number of hacker attacks by 98%: after fixing several SQL injection vulnerabilities, typing the data and using techniques to sanitize the data;",
          "Reduced the average time to block and unblock defaulting customers by 86%: automating the financial verification and blocking/unblocking process;",
        ],
      },
      {
        company: "Centauro Antenas",
        title: "Junior Software Developer",
        dates: "Jan 2008 - Dec 2011",
        responsibilities: [
          "Worked for introducing technology: Increasing the company’s monthly revenue by 200% developing a website with php and javascript, after ranking it first on Google with techniques to automatically generate pages according to Google searches and parameterized CEO terms.",
          "Developed a CRM: Reduce the average time from 15 minutes to 5 seconds in locating the history of services provided for each customer, developing a CRM using php, mysql and javascript.",
        ],
      },
    ],
    education: [
      {
        institution: "OPET college",
        degree: "Bachelor in Advertising and Propaganda",
        dates: "Feb 2007 - July 2011",
        coursework: [],
      },
    ],
    skills: [
      "Languages: Javascript, php, html, css",
      "Framework: Node.js, Vue.js, Express.js, Sequilize, Feathers.js, Quasar.js, Keycloak, Camunda bpm",
      "Stack: K8S, RabbitMQ, Redis, MYSQL, Microservices, CI/CD",
      "Tests: Unit, Integration, E2E",
    ],
    skills2: ["js", "python", "java", "Git", "tailwind", "MySQL"],
    certifications: [],
    projects: [
      {
        title: "Vue.js Frontend Integration with Keycloak",
        description:
          "This is a frontend application integrated with Keycloak, which allows you to manage users, groups, and roles through a user-friendly interface.",
        gitHubLink: "https://github.com/FernandoZorek/kc-users-management",
      },
      {
        title: "JWT Token FeathersJS Application",
        description:
          "This project is a base setup for a FeathersJS application. It is designed to serve as a starting point for other projects, providing a basic structure and common functionalities such as user authentication.",
        gitHubLink: "https://github.com/FernandoZorek/feathers.js-jwt-authentication",
      },
    ],
    volunteerExperience: [
      {
        organization: "Aldeia Nissi NGO",
        role: "Staff Volunteer Work",
        dates: "Oct 2018 - Nov 2018",
        description:
          "This was a rewarding experience, where me, my wife and my son, who was 3 years old at the time, had the opportunity to get to know and assist in a professionalization project for young orphans cared for by the NGO Aldeia Nissi.  Angola/Africa",
        location: "Angola/Africa",
      },
      {
        organization: "Friends of Caximba NGO",
        role: "Volunteer",
        dates: "Feb 2021 - Present",
        description:
          "Knowing this social project that helps socially vulnerable children and elderly people, I decided to help them with my skills and knowledge, creating a website and platform to receive donations to help with the expenses of this NGO, I continue helping by keeping the website/platform in full operation.",
        location: "Curitiba - Brazil",
      },
    ],
  };

  return (
    <>
      <div className="fixed inset-0 z-50 flex h-screen w-screen items-center justify-center bg-gray-800 bg-opacity-50 backdrop-blur-sm">
        {/* <button onClick={toggleVideoProfilePopup}>close</button> */}
        hello
        <div
          id="video-profile-in"
          className="smooth-scrollbar relative h-[90vh] w-full max-w-[700px] overflow-hidden overflow-y-auto rounded-lg bg-white px-5 py-3 shadow-lg"
        >
          {/* <!-- modal content goes here  -->
        <!-- Modal Content --> */}
          <div className="flex flex-col items-start space-y-5">
            {/* <!-- Profile Image and Name --> */}
            <header className="mb-0 mt-0 flex w-full items-center justify-between">
              <h1 className="text-lg font-bold text-gray-800">Your Resume</h1>
              {/* <!-- Close Button --> */}
              <button
                onClick={toggleVideoProfilePopup}
                className="text-gray-600 hover:text-gray-900"
              >
                <svg
                  className="h-6 w-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M6 18L18 6M6 6l12 12"
                  ></path>
                </svg>
              </button>
            </header>

            <div className="mt-4 flex items-start md:flex-row">
              {/* <!-- Profile Image --> */}
              <img
                src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSQE7P96UJ3AvW3NlB9FrVQeA1mv8qxfrsBOTQUcseqvvRNyKBmU_P-9PVeyrCjZYU9DFg&usqp=CAU"
                alt="Nicolas Trevino"
                className="mb-4 h-12 w-12 rounded-full object-cover md:mb-0 md:mr-4"
              />

              {/* <!-- Candidate Info --> */}
              <div className="flex-grow space-y-0">
                {/* <!-- Name, Role, and Location in the Same Line --> */}
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-bold text-gray-800">
                    Nicolas Trevino
                  </h2>

                  <div className="flex items-center space-x-3">
                    {/* <!-- Role Icon and Text --> */}
                    <div className="ml-2 flex items-center space-x-1">
                      <span className="rounded bg-blue-100 px-2 py-1 text-sm font-semibold text-blue-600">
                        Backend Developer
                      </span>
                    </div>
                    {/* <!-- Location Icon and Text --> */}
                    <div className="flex items-center space-x-1">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-4 w-4 text-blue-500"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M12 11c1.104 0 2-.896 2-2s-.896-2-2-2-2 .896-2 2 .896 2 2 2zm0 3c-2.761 0-5-2.239-5-5s2.239-5 5-5 5 2.239 5 5-2.239 5-5 5zm0 0v7m0 0H9m3 0h3"
                        />
                      </svg>
                      <span className="text-sm">Vancouver, Canada</span>
                    </div>
                  </div>
                </div>

                {/* <!-- Current and Expected Salary + View Documents Button on the Same Line --> */}
                <div className="flex items-center justify-between">
                  {/* <!-- Salary Info --> */}
                  <div className="flex space-x-3 text-gray-600">
                    <div className="flex items-center space-x-1">
                      <span className="text-sm font-bold">Current:</span>
                      <span className="text-sm">20 LPA</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <span className="text-sm font-bold">Expected:</span>
                      <span className="text-sm">30 LPA</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* <!-- Professional Skills --> */}
            <div className="mt-4 w-full rounded-md">
              <div className="flex items-center">
                <svg
                  className="mr-2 h-4 w-4 text-gray-500"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <circle cx="12" cy="12" r="10" />
                  <path d="M12 16v-4" />
                  <path d="M8 10l4-4 4 4" />
                </svg>
                <h3 className="text-lg font-semibold text-gray-800">
                  Professional Skills
                </h3>
              </div>

              <div className="mt-0 flex flex-wrap gap-2">
                {strippedResume.skills2.map((skill, index) => (
                  <span
                    key={index}
                    className="rounded-full bg-yellow-100 px-2 py-1 text-sm text-yellow-800"
                  >
                    {skill}
                  </span>
                ))}
              </div>
            </div>
            {/* <!-- About Part --> */}
            <section className="mt-4">
              <div className="flex items-center">
                <svg
                  className="mr-2 h-4 w-4 text-gray-500"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path d="M14 3H7a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V8z" />
                  <path d="M14 3v5h5" />
                  <path d="M16 13H8" />
                  <path d="M16 17H8" />
                </svg>
                <h3 className="text-lg font-semibold text-gray-800">
                  AI Summary
                </h3>
              </div>

              <p className="mb-0 text-gray-600">
                {/* Passionate back-end developer with 5+ years of experience in
                building scalable and efficient web applications. Specialized in
                Python and Node.js, with a strong focus on API development and
                database optimization. */}
                {strippedResume?.professionalSummary}
              </p>
            </section>

            {/* <!-- Experience part --> */}
            <section className="mx-0 mt-4 w-full rounded-xl">
              <div className="flex items-center space-x-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4 text-gray-600"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  stroke-width="1.5"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path d="M20 7H4a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2Z" />
                  <path d="M16 7V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v2" />
                  <path d="M12 12v3" />
                  <path d="M3 13h18" />
                </svg>
                <h3 className="text-lg font-semibold text-gray-800">
                  Experience
                </h3>
              </div>
              <div className="relative">
                <div className="space-y-3">
                  {strippedResume.workExperience.map((job, index) => (
                    <div key={index} className="relative flex items-start">
                      <div className="ml-3 w-full rounded-lg bg-white px-4 py-1">
                        <div
                          className={`border-l-4 ${
                            index % 2 === 0
                              ? "border-blue-400"
                              : "border-purple-500"
                          }`}
                        >
                          <h4 className="ml-2 text-sm font-semibold text-gray-800">
                            {job.company}
                          </h4>
                          <p className="flex justify-between text-sm text-gray-600">
                            <span className="ml-2 font-bold text-blue-600">
                              {job.title}
                            </span>
                            <span className="text-blue-500">{job.dates}</span>
                          </p>
                          <ul className="ml-2 list-inside list-disc text-sm text-gray-700">
                            {/* {job.responsibilities.map((responsibility, resIndex) => (
                                <li key={resIndex}>{responsibility}</li>
                              ))} */}
                            {job.responsibilities[0]}
                          </ul>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </section>

            <section className="mb-4 mt-4 w-full rounded-xl">
              <div className="flex items-center space-x-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4 text-gray-600"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  stroke-width="1.5"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path d="M12 14l9-5-9-5-9 5 9 5z" />
                  <path d="M12 14l6.16-3.422a12.083 12.083 0 015.281 4.946 1.998 1.998 0 01-.775 2.72l-5.518 2.592a13.087 13.087 0 01-10.296 0l-5.518-2.592a1.998 1.998 0 01-.775-2.72 12.083 12.083 0 015.281-4.946L12 14z" />
                  <path
                    stroke-linecap="butt"
                    d="M12 6.042A13.325 13.325 0 017 3.542m0 0a13.325 13.325 0 00-7 0"
                  />
                </svg>

                <h3 className="text-lg font-semibold text-gray-800">
                  Education
                </h3>
              </div>
              <div className="relative">
                <div className="space-y-9">
                  {strippedResume.education.map((school, index) => (
                    <div
                      key={index}
                      className="relative flex w-full items-start"
                    >
                      {/* <!-- Content --> */}
                      <div className="ml-3 w-full rounded-lg bg-white px-4 py-0">
                        <div className="border-l-4 border-blue-400">
                          <h4 className="ml-2 text-sm font-semibold text-gray-800">
                            {school.institution}
                          </h4>
                          <p className="flex justify-between space-x-28 text-sm text-gray-600">
                            <span className="ml-2 font-bold text-blue-600">
                              {school.degree}
                            </span>
                            <span className="text-blue-500">
                              {school.dates}
                            </span>
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </section>

            <section className="w-3xl mb-4 mt-4 max-w-3xl rounded-xl">
              <div className="flex items-center space-x-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4 text-gray-600"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  stroke-width="1.5"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
                  <path d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
                  <path d="M3 13h18" />
                </svg>

                <h3 className="text-lg font-semibold text-gray-800">
                  Projects
                </h3>
              </div>
              <div className="relative">
                {/* <!-- Project items --> */}
                <div className="space-y-3">

                  {strippedResume.projects.map((project, index) =>(

                  <div key={index} className="relative flex w-full items-start">
                    {/* <!-- Content --> */}
                    <div className="ml-3 w-full rounded-lg bg-white px-4 py-0">
                      <div className="border-l-4 border-blue-400">
                        <p className="flex justify-between space-x-28 text-sm text-gray-600">
                          <span className="ml-2 font-bold text-blue-600">
                           {project.title}
                          </span>
                          {/* <!-- <span className="text-blue-500">Feb 2007 - July 2011</span> --> */}
                        </p>
                        <p className="ml-2 text-sm text-gray-700">
                          {project.description}
                          <br />
                          <a
                            href={project.gitHubLink}
                            className="mt-1 inline-block text-blue-600 underline underline-offset-4 transition-colors duration-300 hover:text-blue-800 hover:underline-offset-2"
                          >
                            <span className="inline-flex items-center space-x-1">
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                className="h-4 w-4 text-blue-500"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                stroke-width="2"
                              >
                                <path
                                  stroke-linecap="round"
                                  stroke-linejoin="round"
                                  d="M12 4v16m8-8H4"
                                />
                              </svg>
                              <span>GitHub</span>
                            </span>
                          </a>
                        </p>
                      </div>
                    </div>
                  </div>



                  ))}

                  
                </div>
              </div>
            </section>

            {/* <!-- Volunteer Experience --> */}
            <section className="mx-0 mt-4 max-w-3xl rounded-xl">
              <div className="flex items-center space-x-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4 text-gray-600"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  stroke-width="1.5"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path d="M16 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2" />
                  <circle cx="8.5" cy="7" r="4" />
                  <path d="M20 8v6" />
                  <path d="M23 11h-6" />
                </svg>
                <h3 className="text-lg font-semibold text-gray-800">
                  Volunteering
                </h3>
              </div>
              <div className="relative">
                {/* <!-- Timeline line -->
              <!-- <div
                className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-blue-300 via-purple-300 to-pink-300 rounded-full"
              ></div> --> */}

                {/* <!-- Volunteering items --> */}
                <div className="space-y-3">
                  {/* <!-- TechCorp Inc. (Multiple Roles) --> */}
                  {strippedResume.volunteerExperience.map((vex, index)=>(

                  <div key={index} className="relative flex items-start">
                    {/* <!-- Content --> */}
                    <div className="ml-3 w-full rounded-lg bg-white px-4 py-0">
                      <h4 className="ml-2 text-sm font-semibold text-gray-800">
                        {vex.organization},<span>{vex.location}</span>
                      </h4>
                      <div className="border-l-4 border-blue-400">
                        <p className="flex justify-between text-sm text-gray-600">
                          <span className="ml-2 font-bold text-blue-600">
                            {vex.role}
                          </span>
                          <span className="text-blue-500">
                            {vex.dates}
                          </span>
                        </p>
                        <p className="ml-2 text-sm text-gray-700">
                         {vex.description}
                        </p>
                      </div>
                    </div>
                  </div>
                  ))}

                  
                </div>
              </div>
            </section>
          </div>
        </div>
      </div>
    </>
  );
};

export default VidProfPopup;
