import React, { useState, useEffect } from "react";
import Loader from "components/Loader";
import Cookies from "js-cookie";
import JobCard from "./smaller_comp/SuggestedJobCard";
import toast from "react-hot-toast";

function SuggestedJobs() {
  const [jobs, setJobs] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const jstoken = Cookies.get("jstoken");

  useEffect(() => {
    const fetchJobData = async () => {
      try {
        const resjobdata = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/suggestedJobs`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
          }
        );

        if (resjobdata.ok) {
          const data = await resjobdata.json();
          console.log("Fetched job data:", data);

          // Check if the data is an array; if not, set it to an empty array or adjust as needed
          setJobs(Array.isArray(data) ? data : []);
        } else {
          console.error("Error fetching job data:", resjobdata.status, resjobdata.statusText);
        }
      } catch (error) {
        console.error("Network error:", error);
      }
    };

    fetchJobData();
  }, []);


  return (
    <>
      {jstoken ? (
        <div className="card scrollbar-hide hover:scrollbar-thumb-rounded-full col-span-full h-[80vh] overflow-y-auto rounded-md bg-white p-6 scrollbar-thin hover:scrollbar-thumb-gray-900 dark:bg-navy-700 dark:text-white dark:hover:scrollbar-thumb-gray-300 lg:col-span-5 2xl:col-span-5">
          <span className="text-2xl font-semibold text-gray-800 dark:text-gray-200">
            Suggested Jobs
          </span>
          {isLoading ? (
            <Loader text={"Loading..."} />
          ) : (
            <div className="mt-10 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
                {jobs.length > 0 ? (
                    jobs.map((job, index) => (
                    <JobCard iconUrl={job.image} key={index} job={job} />
                    ))
                ) : (
                    <div className="col-span-full flex flex-col mt-10 items-center ">
                    <img
                      src="https://cdni.iconscout.com/illustration/premium/thumb/artificial-intelligence-search-illustration-download-in-svg-png-gif-file-formats--ai-view-business-technology-pack-illustrations-5408137.png" // Replace with the actual image URL
                      alt="AI Searching jobs"
                      className="mb-4 mt-4 w-[15rem]"
                    />
                    <p className="text-center text-gray-500">
                      AI is working to suggest jobs. Hang tight.
                    </p>
                  </div>
                )}
                </div>

          )}
        </div>
      ) : (
        <h2>Please sign in to view suggested jobs</h2>
      )}
    </>
  );
}

export default SuggestedJobs;
