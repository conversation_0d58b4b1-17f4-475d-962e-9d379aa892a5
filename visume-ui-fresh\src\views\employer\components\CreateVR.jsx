import React, { useState, useRef, useEffect } from "react";
import Cookies from "js-cookie";
import { useNavigate } from "react-router-dom";
import Loader from "components/Loader";

const CreateVR = ({ togglePopupVR }) => {
  const navigate = useNavigate();
  const [step, setStep] = useState(1);

  const candId = Cookies.get("candId"); // Retrieve the candId from the cookie

  const saveqnscookie = (data) => {
    const questions = data.questions;
    Cookies.set("questions", JSON.stringify(questions), { expires: 7 }); // store questions in cookie
    console.log(questions);
    Cookies.set("videoProfileId", data.videoProfileId, { expires: 7 }); // store videoProfileId

    // to display created video profiles
    Cookies.set("jobRole", data.jobRole, { expires: 7 }); // Store jobRole in cookie
    Cookies.set("skills", data.skills, { expires: 7 }); // Store jobRole in cookie
  };

  const [formData, setFormData] = useState({
    candId: candId,
    jobRole: "",
    skills: [],
    companyType: "",
    experience: "",
    salary: {
      current: "",
      expected: "",
    },
  });
  const [skillInput, setSkillInput] = useState("");
  const [jobRoleInput, setJobRoleInput] = useState("");
  const [jobRoleSuggestions, setJobRoleSuggestions] = useState([]);
  const [skillSuggestions, setSkillSuggestions] = useState([]);
  const [showJobRoleSuggestions, setShowJobRoleSuggestions] = useState(false);
  const [showSkillSuggestions, setShowSkillSuggestions] = useState(false);
  const formRef = useRef(null);
  const skillRef = useRef(null);
  const jobRef = useRef(null);
  const [error, setError] = useState(""); // Error state

  const [isLoading, setIsLoading] = useState(false);

  const jobRoles = [
    "Software Developer/Engineer",
    "Frontend Developer",
    "Backend Developer",
    "Full Stack Developer",
    "DevOps Engineer",
  ];

  const skillsList = [
    "JavaScript",
    "Python",
    "React",
    "Node.js",
    "CSS",
    "HTML",
    "Tailwind CSS",
    "Django",
  ];

  const saveFormDataToCookie = (formData) => {
    // Serialize formData and save it to a cookie
    Cookies.set("formData", JSON.stringify(formData), { expires: 7 }); // Expires in 7 days
  };

  useEffect(() => {
    const handleClickOutside = (e) => {
      if (formRef.current && !formRef.current.contains(e.target)) {
        togglePopupVR();
      }
      if (skillRef.current && !skillRef.current.contains(e.target)) {
        setShowSkillSuggestions(false);
      }

      if (jobRef.current && !jobRef.current.contains(e.target)) {
        setShowJobRoleSuggestions(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [togglePopupVR]);

  const handleJobRoleInput = (e) => {
    const value = e.target.value;
    setJobRoleInput(value);
    setFormData({ ...formData, jobRole: value });
    const filteredSuggestions = jobRoles.filter((role) =>
      role.toLowerCase().includes(value.toLowerCase())
    );
    setJobRoleSuggestions(filteredSuggestions);
    setShowJobRoleSuggestions(true);
    setShowSkillSuggestions(false);
  };

  const handleJobRoleSuggestionClick = (suggestion) => {
    setJobRoleInput(suggestion);
    setFormData({ ...formData, jobRole: suggestion });
    setShowJobRoleSuggestions(false);
  };

  const handleSkillInput = (e) => {
    const value = e.target.value;
    setSkillInput(value);
    const filteredSuggestions = skillsList.filter(
      (skill) =>
        skill.toLowerCase().includes(value.toLowerCase()) &&
        !formData.skills.includes(skill)
    );
    setSkillSuggestions(filteredSuggestions);
    setShowJobRoleSuggestions(false);
    setShowSkillSuggestions(true);
  };

  const addSkill = (skill) => {
    if (!formData.skills.includes(skill)) {
      setFormData({
        ...formData,
        skills: [...formData.skills, skill],
      });
      setSkillInput("");
      setShowSkillSuggestions(false);
    }
  };

  const removeSkill = (skillToRemove) => {
    setFormData({
      ...formData,
      skills: formData.skills.filter((skill) => skill !== skillToRemove),
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    setIsLoading(true); // Show the loader
    console.log("Form submitted:", formData);
    saveFormDataToCookie(formData); // Save form data to a cookie

    const url = `${import.meta.env.VITE_APP_HOST}/api/v1/create-video-resume`;
    console.log("Post method to", url);

    // Make an API call using fetch
    fetch(url, {
      method: "POST", // HTTP method
      headers: {
        "Content-Type": "application/json", // Specify JSON format
      },
      body: JSON.stringify(formData), // Convert formData to JSON string
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json(); // Parse JSON response
      })
      .then((data) => {
        console.log("Success:", data); // Handle success response
        saveqnscookie(data); // save questions and videoProfileId

        // You can redirect or show a success message here
      })
      .catch((error) => {
        console.error("Error:", error); // Handle errors
        // Optionally, you can display an error message to the user
      })
      .finally(() => {
        setIsLoading(false); // Hide the loader

        navigate(`/candidate/interview/${Cookies.get("videoProfileId")}`);
        // window.location.reload(); // Reload the page
      });
  };

  const handleNextStep = (e) => {
    e.preventDefault();
    // Validate based on current step
    if (step === 1) {
      if (!formData.jobRole || formData.skills.length === 0) {
        setError("Please select a job role and add at least one skill.");
        return;
      }
    } else if (step === 2) {
      if (!formData.companyType) {
        setError("Please select a company type.");
        return;
      }
    } else if (step === 3) {
      if (!formData.experience) {
        setError("Please select your experience level.");
        return;
      }
      if (
        (formData.experience === "2-3" || formData.experience === "4-5") &&
        (!formData.salary.current || !formData.salary.expected)
      ) {
        setError("Please enter both current and expected salary.");
        return;
      }
    }
    setError(""); // Clear the error if validation passes
    if (step < 4) {
      setStep(step + 1);
    }
  };

  const handlePrevStep = () => {
    if (step > 1) {
      setStep(step - 1);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;

    if (name === "currentSalary") {
      // Handling the nested salary.current
      setFormData((prevState) => ({
        ...prevState,
        salary: {
          ...prevState.salary,
          current: value, // Update only salary.current
        },
      }));
    } else if (name === "expectedSalary") {
      // Handling the nested salary.expected
      setFormData((prevState) => ({
        ...prevState,
        salary: {
          ...prevState.salary,
          expected: value, // Update only salary.expected
        },
      }));
    } else {
      // Handling other fields
      setFormData({ ...formData, [name]: value });
    }
  };

  return (
    <div className="fixed inset-0 z-[9999] flex h-screen w-screen items-center justify-center bg-gray-800 bg-opacity-50 backdrop-blur-sm">
      <div className="w-full  p-3">
        <div
          ref={formRef}
          className="mx-auto max-w-2xl rounded-md bg-white px-8 py-4"
        >
          <div className="mb-5 mt-0 flex w-full items-center justify-between">
            <h2 className="text-center text-2xl font-semibold text-blue-800">
              Create Video Profile
            </h2>
            <div className="">
              <button
                onClick={togglePopupVR}
                className=" text-gray-600 hover:text-gray-900"
              >
                <svg
                  className="h-6 w-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M6 18L18 6M6 6l12 12"
                  ></path>
                </svg>
              </button>
            </div>
          </div>

          <form
            id="multistepForm"
            className="flex h-[70vh] flex-col justify-between rounded-lg"
            onSubmit={handleSubmit}
          >
            {/* Progress Bar */}
            <div className="relative mb-8 h-2 rounded-full bg-gray-200">
              <div
                id="progressBar"
                className="absolute left-0 top-0 h-2 rounded-full bg-blue-600 transition-all duration-500"
                style={{ width: `${(step / 4) * 100}%` }}
              ></div>
            </div>

            {/* Error Message */}
            {error && (
              <div className="mb-4 font-semibold text-red-500">{error}</div>
            )}

            {/* Step 1 */}
            {step === 1 && (
              <div className="step">
                <div>
                  <h1 className="mb-1 text-3xl font-semibold text-gray-800">
                    Select Job Role and Skills
                  </h1>
                  <p className="mb-6 text-gray-500">
                    Choose a job role and add your skills.
                  </p>
                </div>

                {/* Job Role Input */}
                <div className="relative mb-4">
                  <label htmlFor="jobRole" className="mb-2 block text-gray-700">
                    Job Role
                  </label>
                  <input
                    required
                    id="jobRole"
                    name="jobRole"
                    type="text"
                    value={jobRoleInput}
                    onChange={handleJobRoleInput}
                    onClick={handleJobRoleInput}
                    onFocus={() => setShowJobRoleSuggestions(true)}
                    placeholder="Type to search..."
                    className="w-full rounded-lg border border-gray-300 px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  {showJobRoleSuggestions && (
                    <ul
                      ref={jobRef}
                      className="absolute z-10 mt-1 max-h-40 w-full overflow-y-auto rounded-lg border border-gray-300 bg-white shadow-lg"
                    >
                      {jobRoleSuggestions.map((suggestion, index) => (
                        <li
                          key={index}
                          className="cursor-pointer px-4 py-2 hover:bg-blue-100"
                          onClick={() =>
                            handleJobRoleSuggestionClick(suggestion)
                          }
                        >
                          {suggestion}
                        </li>
                      ))}
                    </ul>
                  )}
                </div>

                {/* Skills Input */}
                <div className="relative">
                  <label className="mb-2 block text-gray-700">Skills</label>
                  <input
                    type="text"
                    id="skill-input"
                    value={skillInput}
                    onChange={handleSkillInput}
                    onClick={handleSkillInput}
                    onFocus={() => setShowSkillSuggestions(true)}
                    onKeyDown={(e) => {
                      if (e.key === "Enter" && skillSuggestions.length > 0) {
                        e.preventDefault(); // Prevent form submission or any other default action
                        addSkill(skillSuggestions[0]); // Add the first suggestion or handle it as needed
                      }
                    }}
                    className="w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500"
                    placeholder="Enter a skill"
                  />
                  {showSkillSuggestions && (
                    <ul
                      ref={skillRef}
                      className="absolute z-10 mt-1 max-h-40 w-full overflow-y-auto rounded-lg border border-gray-300 bg-white shadow-lg"
                    >
                      {skillSuggestions.map((suggestion, index) => (
                        <li
                          key={index}
                          className="cursor-pointer px-4 py-2 hover:bg-blue-100"
                          onClick={() => addSkill(suggestion)}
                        >
                          {suggestion}
                        </li>
                      ))}
                    </ul>
                  )}
                  <div className="mt-3 h-auto max-h-[100px] w-full overflow-y-auto">
                    <div id="skills-container" className="flex flex-wrap gap-2">
                      {formData.skills.map((skill, index) => (
                        <span
                          key={index}
                          className="flex items-center rounded-full bg-gray-200 px-3 py-1"
                        >
                          {skill}
                          <button
                            type="button"
                            className="ml-2 text-red-500 hover:text-red-700"
                            onClick={() => removeSkill(skill)}
                          >
                            &times;
                          </button>
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Step 2 */}
            {step === 2 && (
              <div className="step">
                <h1 className="mb-1 text-3xl font-semibold text-gray-800">
                  Type of Company
                </h1>
                <p className="mb-6 text-gray-500">
                  Select your preferred type of company.
                </p>
                <div className="flex space-x-4">
                  <label
                    className={`block cursor-pointer rounded-lg border p-4 transition ${
                      formData.companyType === "MNC"
                        ? "border-blue-500 bg-blue-100"
                        : "hover:bg-gray-100"
                    }`}
                  >
                    <input
                      type="radio"
                      name="companyType"
                      value="MNC"
                      onChange={handleInputChange}
                      className="hidden"
                    />
                    <span className="text-gray-700">MNC</span>
                  </label>
                  <label
                    className={`block cursor-pointer rounded-lg border p-4 transition ${
                      formData.companyType === "Startup"
                        ? "border-blue-500 bg-blue-100"
                        : "hover:bg-gray-100"
                    }`}
                  >
                    <input
                      type="radio"
                      name="companyType"
                      value="Startup"
                      onChange={handleInputChange}
                      className="hidden"
                    />
                    <span className="text-gray-700">Startup</span>
                  </label>
                </div>
              </div>
            )}

            {/* Step 3 */}
            {step === 3 && (
              <div className="step">
                <h1 className="mb-0 text-3xl font-semibold text-gray-800">
                  Experience Level
                </h1>
                <p className="mb-6 text-gray-500">
                  Select your experience level.
                </p>

                <div className="flex space-x-4">
                  <label
                    className={`block cursor-pointer rounded-lg border p-4 transition ${
                      formData.experience === "0-1"
                        ? "border-blue-500 bg-blue-100"
                        : "hover:bg-gray-100"
                    }`}
                  >
                    <input
                      type="radio"
                      name="experience"
                      value="0-1"
                      onChange={handleInputChange}
                      className="hidden"
                    />
                    <span className="text-gray-700">0-1 years Fresher</span>
                  </label>
                  <label
                    className={`block cursor-pointer rounded-lg border p-4 transition ${
                      formData.experience === "2-3"
                        ? "border-blue-500 bg-blue-100"
                        : "hover:bg-gray-100"
                    }`}
                  >
                    <input
                      type="radio"
                      name="experience"
                      value="2-3"
                      onChange={handleInputChange}
                      className="hidden"
                    />
                    <span className="text-gray-700">
                      2-3 years Intermediate
                    </span>
                  </label>
                  <label
                    className={`block cursor-pointer rounded-lg border p-4 transition ${
                      formData.experience === "4-5"
                        ? "border-blue-500 bg-blue-100"
                        : "hover:bg-gray-100"
                    }`}
                  >
                    <input
                      type="radio"
                      name="experience"
                      value="4-5"
                      onChange={handleInputChange}
                      className="hidden"
                    />
                    <span className="text-gray-700">4-5 years Experienced</span>
                  </label>
                </div>

                {(formData.experience === "2-3" ||
                  formData.experience === "4-5") && (
                  <div className="mt-4">
                    <label
                      htmlFor="currentSalary"
                      className="mb-2 block text-gray-700"
                    >
                      Current Salary
                    </label>
                    <input
                      type="text"
                      id="currentSalary"
                      name="currentSalary"
                      value={formData.salary.current}
                      onChange={handleInputChange}
                      className="w-full rounded-lg border border-gray-300 px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <label
                      htmlFor="expectedSalary"
                      className="mb-2 mt-4 block text-gray-700"
                    >
                      Expected Salary
                    </label>
                    <input
                      type="text"
                      id="expectedSalary"
                      name="expectedSalary"
                      value={formData.salary.expected}
                      onChange={handleInputChange}
                      className="w-full rounded-lg border border-gray-300 px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                )}
              </div>
            )}

            {/* Step 4 */}
            {step === 4 && (
              <div className="step">
                <h1 className="mb-4 text-3xl font-semibold text-gray-800">
                  Confirm Your Details
                </h1>
                <p className="mb-6 text-gray-500">
                  Please review your selections.
                </p>
                <ul className="mb-6 text-gray-700">
                  <li>
                    Job Role:{" "}
                    <span className="font-semibold">{formData.jobRole}</span>
                  </li>
                  <li>
                    Skills:{" "}
                    <span className="font-semibold">
                      {formData.skills.join(", ")}
                    </span>
                  </li>
                  <li>
                    Company Type:{" "}
                    <span className="font-semibold">
                      {formData.companyType}
                    </span>
                  </li>
                  <li>
                    Experience:{" "}
                    <span className="font-semibold">{formData.experience}</span>
                  </li>
                  {(formData.experience === "2-3" ||
                    formData.experience === "4-5") && (
                    <>
                      <li>
                        Current Salary:{" "}
                        <span className="font-semibold">
                          {formData.salary.current}
                        </span>
                      </li>
                      <li>
                        Expected Salary:{" "}
                        <span className="font-semibold">
                          {formData.salary.expected}
                        </span>
                      </li>
                    </>
                  )}
                </ul>
                <p className="mb-6 text-gray-500">
                  Click submit to create your video profile.
                </p>
              </div>
            )}

            {/* Navigation Buttons */}
            <div className="mt-8 flex justify-between">
              {step > 1 && (
                <button
                  type="button"
                  id="prevBtn"
                  className="rounded-lg bg-gray-300 px-6 py-3 font-semibold text-gray-700 transition hover:bg-gray-400"
                  onClick={handlePrevStep}
                >
                  Previous
                </button>
              )}
              {step < 4 ? (
                <button
                  type="button"
                  id="nextBtn"
                  className="rounded-lg bg-blue-600 px-6 py-3 font-semibold text-white transition hover:bg-blue-700"
                  onClick={handleNextStep}
                >
                  Next
                </button>
              ) : (
                <button
                  type="submit"
                  id="submitBtn"
                  className="rounded-lg bg-green-600 px-6 py-3 font-semibold text-white transition hover:bg-green-700"
                >
                  Submit
                </button>
              )}
            </div>
          </form>

          {/* Loader */}
          {isLoading && (
            <Loader text = {"Creating your video profile..."} />
          )}
        </div>
      </div>
    </div>
  );
};

export default CreateVR;
