import { useState, useCallback, useEffect, useRef } from "react";

export function useRecording(localCamStream) {
  const [mediaRecorder, setMediaRecorder] = useState(null);
  const [recordedChunks, setRecordedChunks] = useState([]);
  const [recordedVideoUrl, setRecordedVideoUrl] = useState(null);
  const [isRecording, setIsRecording] = useState(false);
  const chunks = useRef([]);

  // Effect to set up media recorder
  useEffect(() => {
    if (!localCamStream) return;

    const recorder = new MediaRecorder(localCamStream, {
      mimeType: "video/webm;codecs=vp8,opus",
      videoBitsPerSecond: 5000000, // 5 Mbps for better quality
      audioBitsPerSecond: 128000   // 128 kbps for audio
    });

    recorder.onstart = () => {
      console.log("Recording started");
      chunks.current = [];
      setIsRecording(true);
      setRecordedVideoUrl(null);
    };

    recorder.ondataavailable = (event) => {
      if (event.data.size > 0) {
        chunks.current.push(event.data);
        // Preserve previous chunks and add new one
        setRecordedChunks(currentChunks => {
          const newChunks = [...currentChunks, event.data];
          // Create and update blob URL whenever we get new data
          const blob = new Blob(newChunks, { type: "video/webm" });
          try {
            if (recordedVideoUrl) {
              URL.revokeObjectURL(recordedVideoUrl);
            }
            const url = URL.createObjectURL(blob);
            setRecordedVideoUrl(url);
          } catch (error) {
            console.error("Error updating video URL:", error);
          }
          return newChunks;
        });
      }
    };

    recorder.onstop = () => {
      console.log("Recording stopped");
      setIsRecording(false);
      // Final processing is already done in ondataavailable
    };

    setMediaRecorder(recorder);

    return () => {
      if (recorder.state !== "inactive") {
        recorder.stop();
      }
      if (recordedVideoUrl) {
        URL.revokeObjectURL(recordedVideoUrl);
      }
      chunks.current = [];
    };
  }, [localCamStream]);

  const startRecording = useCallback(async () => {
    if (!mediaRecorder || isRecording) return;
    
    try {
      // Clear previous recording if exists
      if (recordedVideoUrl) {
        URL.revokeObjectURL(recordedVideoUrl);
        setRecordedVideoUrl(null);
      }
      chunks.current = [];
      setRecordedChunks([]);
      
      await new Promise((resolve) => {
        mediaRecorder.onstart = () => {
          console.log("Recording started successfully");
          resolve();
        };
        mediaRecorder.start(500); // Collect data every 500ms for smoother recording
      });
      
      return true;
    } catch (error) {
      console.error("Failed to start recording:", error);
      return false;
    }
  }, [mediaRecorder, isRecording, recordedVideoUrl]);

  const stopRecording = useCallback(async () => {
    if (!mediaRecorder || !isRecording) return;
    
    try {
      await new Promise((resolve) => {
        mediaRecorder.onstop = () => {
          console.log("Recording stopped successfully");
          resolve();
        };
        mediaRecorder.stop();
      });
      
      return true;
    } catch (error) {
      console.error("Failed to stop recording:", error);
      return false;
    }
  }, [mediaRecorder, isRecording]);

  const handleDownload = useCallback(() => {
    if (recordedVideoUrl) {
      const a = document.createElement("a");
      a.style = "display: none";
      a.href = recordedVideoUrl;
      a.download = "interview-recording.webm";
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    }
  }, [recordedVideoUrl]);

  return {
    startRecording,
    stopRecording,
    handleDownload,
    isRecording,
    recordedVideoUrl,
    recordedChunks
  };

}
