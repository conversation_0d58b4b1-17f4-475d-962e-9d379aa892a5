import React, { useState, useEffect } from "react";
import Loader from "components/Loader";
import Cookies from "js-cookie";
import VideoProfileCard from "./components/VideoProfileCard";
import { useNavigate } from "react-router-dom";
import toast from "react-hot-toast";

function AllVideoProfiles() {
  const [videoProfiles, setVideoProfiles] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const jstoken = Cookies.get("jstoken");
  const candId = Cookies.get("candId");
  const navigate = useNavigate()

  useEffect(() => {
    const fetchVideoProfiles = async () => {
      try {
        // Make the API request with the dynamic candId
        setIsLoading(true);
        const response = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/video-resume/${candId}`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
          }
        );

        if (response.ok) {
          const data = await response.json();

          // Iterate over the response and pick out the necessary fields
          const newProfiles = data.map((profile) => ({
            vpid: profile.video_profile_id,
            role: profile.role,
            skills: profile.skills.split(",").map((skill) => skill.trim()), // Convert skills to an array
            status: profile.status,
          }));
          // Append the new profiles to the existing videoProfiles list
          setVideoProfiles((prevProfiles) => {
            const allProfiles = [...prevProfiles, ...newProfiles];

            if (allProfiles.length > 0) {
              return allProfiles; // Update state with the new list of profiles
            } else {
              navigate("/"); // Redirect to home
              toast("No Video Resumes Found. Create Now."); // Show toast notification
              return prevProfiles; // Optionally return the previous profiles to avoid state becoming undefined
            }
          });
        } else {
          console.error(
            "Error fetching video profiles:",
            response.status,
            response.statusText
          );
        }
      } catch (error) {
        console.error("Network error:", error);
      }
      setIsLoading(false);
    };

    // Only fetch if candId is provided
    if (candId) {
      fetchVideoProfiles();
    }
  }, [candId]);

  return (
    <>
      {jstoken ? (
        <div className="card scrollbar-hide hover:scrollbar-thumb-rounded-full col-span-full h-full max-h-screen overflow-y-auto rounded-md bg-white p-6 scrollbar-thin hover:scrollbar-thumb-gray-900 dark:bg-navy-700 dark:text-white dark:hover:scrollbar-thumb-gray-300 lg:col-span-5 2xl:col-span-5">
          <span className=" text-2xl font-semibold text-gray-800 dark:text-gray-200">
            All Video Resumes
          </span>
          {isLoading ? (
            <Loader text={"Loading..."} />
          ) : (
            <div className="mt-10">
              {videoProfiles.map((profile, index) => (
                <VideoProfileCard
                  key={index}
                  profile={profile}
                  toggleVideoProfilePopup={() => {}}
                />
              ))}
            </div>
          )}
        </div>
      ) : (
        <h2>lol signin</h2>
      )}
    </>
  );
}

export default AllVideoProfiles;
