import { button } from "@material-tailwind/react";
import React from "react";
import toast from "react-hot-toast";
import { useNavigate } from "react-router-dom";

const DescVideoProfileCard = ({ profile }) => {

  const handleDelete = async () => {
    const videoProfileId = profile.vpid;

    try {
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/video-resume/${videoProfileId}`,
        {
          method: "DELETE",
        }
      );

      if (response.ok) {
        // Handle successful delete (e.g., show a success message or update state)
        toast.success("Video profile deleted successfully");
        window.location.reload();
      } else {
        // Handle errors
        console.error("Failed to delete video profile");
      }
    } catch (error) {
      console.error("Error:", error);
    }
  };

  const navigate = useNavigate();

  return (
    <div className="col-span-full mb-2 rounded-lg bg-white p-4 shadow-lg transition-all duration-300 ease-in-out dark:bg-navy-900 ">
      <div className="flex items-center justify-between gap-4">
        {/* Profile Details */}
        <div className="flex flex-col">
          {profile.status === "pending" ? (
            <span className="text-yellow-400">pending!</span>
          ) : (
            ""
          )}
          <h2 className="mb-2 text-lg font-bold text-gray-800 dark:text-white">
            {profile.role}
          </h2>
          <ul className="flex flex-wrap gap-2">
            {profile.skills.map((skill, index) => (
              <li
                key={index}
                className="rounded-md bg-gray-200 px-2 py-1 text-xs text-gray-600 dark:bg-blue-600 dark:text-gray-300"
              >
                {skill}
              </li>
            ))}
          </ul>
        </div>

        {/* View Button */}
        <div className="flex flex-col">
          {profile.status === "pending" ? (
            <button
              onClick={() => navigate(`/candidate/interview/${profile.vpid}`)}
              className="mb-1 rounded-lg bg-brand-500 px-3 py-1 text-white transition-colors duration-200 hover:bg-blue-600"
            >
              complete
            </button>
          ) : (
            <button
              onClick={()=>navigate(`/candidate/videoResume/${profile.vpid}`)}
              className="mb-1 rounded-lg bg-brand-500 px-3 py-1 text-white transition-colors duration-200 hover:bg-blue-600"
            >
              view
            </button>
          )}
          <button
            onClick={handleDelete}
            className="rounded-lg bg-white px-3 py-1 text-red-500 transition-colors duration-200 hover:bg-red-200"
          >
            Delete
          </button>
        </div>
      </div>
    </div>
  );
};

export default DescVideoProfileCard;
