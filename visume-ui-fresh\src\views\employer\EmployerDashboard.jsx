import React, { useEffect } from "react";
import { useState } from "react";
import Cookies from "js-cookie";
import avatar from "assets/img/avatars/avatar4.png";
import {HiOutlineBriefcase, HiOutlineCloudUpload, HiOutlineSparkles, HiChevronLeft , HiChevronRight, HiX,} from "react-icons/hi";
import StatCard from "./components/StatCard";
import PositionsCard from "./components/PositionsCard";
import toast from "react-hot-toast";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import { useNavigate } from "react-router-dom";

function ProfileSkelLoader({ keyValue }) {
  return (
    <div
      key={keyValue}
      className="mb-1 flex items-center justify-between rounded-lg border border-gray-300 bg-white p-3 shadow-sm dark:border-navy-500 dark:bg-navy-800"
    >
      {/* Left side: Job title, skills, and location */}
      <div className="flex flex-col">
        {/* Job title and location */}
        <div className="mb-1 flex items-center">
          <Skeleton width={120} height={16} />
          <div className="ml-2 flex items-center text-sm text-gray-500 dark:text-gray-300">
            <Skeleton width={80} height={16} />
          </div>
        </div>

        {/* Skills */}
        <div className="mt-1 flex flex-wrap gap-2">
          <Skeleton width={50} height={24} className="rounded-full" />
          <Skeleton width={50} height={24} className="rounded-full" />
          <Skeleton width={50} height={24} className="rounded-full" />
          <Skeleton width={50} height={24} className="rounded-full" />
          <Skeleton width={40} height={24} className="rounded-full" />
        </div>
      </div>

      {/* Right side: View button and three dots */}
      <div className="flex items-center gap-4">
        {/* View button */}
        <Skeleton width={60} height={36} className="rounded-full" />
        {/* Three dots */}
        <Skeleton width={20} height={20} circle />
      </div>
    </div>
  );
}

const JobDescriptionModal = ({ isOpen, onClose }) => {
  const jobRoles = [
    "Software Developer/Engineer",
    "Java Developer",
    "Frontend Developer",
    "Backend Developer",
    "Full Stack Developer",
    "DevOps Engineer",
  ];

  const skillsList = [
    "JavaScript",
    "Python",
    "React",
    "Node.js",
    "CSS",
    "HTML",
    "Tailwind CSS",
    "Django",
    "Java",
    "Spring MVC",
  ];

  const locations = ["Bangalore", "Delhi", "Mumbai", "Hyderabad"];
  const emp_id = Cookies.get("employerId");
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState(1);
  const [fileName, setFileName] = useState("");
  const [role, setRole] = useState("");
  const [filteredRoles, setFilteredRoles] = useState(jobRoles);
  const [location, setLocation] = useState("");
  const [filteredLocations, setFilteredLocations] = useState(locations);
  const [skills, setSkills] = useState([]);
  const [filteredSkills, setFilteredSkills] = useState(skillsList);
  const [experience, setExperience] = useState("");
  const [uploadedUrl, setUploadedUrl] = useState("");

  const handleFileUpload = async (e) => {
    const file = e.target.files[0];
    if (file) {
      try {
        setLoading(true);

        // Create FormData and append the file
        const formData = new FormData();
        formData.append("job_description", file);

        // Make API call
        const response = await fetch(
          `${
            import.meta.env.VITE_APP_HOST
          }/api/v1/upload-job-description/${emp_id}`,
          {
            method: "POST",
            body: formData, // FormData automatically sets the correct headers
          }
        );

        if (!response.ok) {
          const errorData = await response.json();
          toast.error(errorData.message || "Failed to upload job description");
          throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();
        if (data?.JobDescription) {
          // Populate the state with the response data
          setLocation(data.JobDescription.location?.[0] || "");
          setRole(data.JobDescription.role || "");
          setExperience(data.JobDescription.experience || "");
          setSkills(
            Array.isArray(data.JobDescription.skills)
              ? data.JobDescription.skills
              : JSON.parse(data.JobDescription.skills || "[]")
          );
          setUploadedUrl(data.filePath || "");
          toast.success(data.message);
          toast.success("Job description uploaded successfully!");
        } else {
          toast.error("Failed to extract necessary details from the file.");
        }
      } catch (err) {
        console.error(`Failed to upload job description: ${err}`);
        toast.error(
          err?.message ||
            "An error occurred while uploading the job description"
        );
      } finally {
        setLoading(false);
        setStep(2); // Proceed to step 2 regardless of success or failure
      }
    } else {
      toast.error("No file selected. Please choose a file to upload.");
    }
  };

  const handleSave = async () => {
    setLoading(true);
    const jobDescription = { role, experience, location, skills };
    try {
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/create-job-description`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            ...jobDescription,
            filePath: uploadedUrl,
            emp_id: emp_id,
          }),
        }
      );

      if (!response.ok) {
        const msg = await response.json();
        toast.error(msg.message);
        throw new Error(`HTTP error! status: ${msg.message}`);
      }

      const data = await response.json();
      toast.success(data.message);
    } catch (err) {
      console.error(`Failed to upload job description: ${err}`);
      toast.error(
        err?.message || "An error occurred while uploading the job description"
      );
    } finally {
      setLoading(false);
      onClose();
    }
  };

  return (
    <div
      className={`fixed inset-0 z-50 bg-gray-500 bg-opacity-75 transition-opacity ${
        isOpen ? "block" : "hidden"
      }`}
      aria-labelledby="modal-title"
      role="dialog"
      aria-modal="true"
    >
      <div className="flex min-h-screen items-center justify-center px-4 text-center">
        <div className="relative w-full max-w-lg overflow-hidden rounded-lg bg-white text-left shadow-xl dark:bg-navy-700">
          {/* Header */}
          <div className="flex items-center justify-between border-b border-gray-300 bg-gray-100 px-4 py-3 dark:border-navy-500 dark:bg-navy-800">
            <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
              {step === 1 ? "Upload Job Description" : "Enter Job Details"}
            </h3>
            <button
              type="button"
              className="text-gray-500 dark:text-gray-400"
              onClick={onClose}
            >
              <HiX className="text-2xl" />
            </button>
          </div>

          {/* Body */}
          <div className="p-6 transition-transform duration-500 ease-in-out">
            {/* Step 1: Upload */}
            {step === 1 && (
              <div>
                <div className="flex w-full flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 p-6">
                  <input
                    type="file"
                    id="file-upload"
                    className="hidden"
                    accept=".pdf,.doc,.docx"
                    onChange={handleFileUpload}
                  />
                  <label
                    htmlFor="file-upload"
                    className="flex cursor-pointer flex-col items-center justify-center text-center text-gray-600 dark:text-gray-300"
                  >
                    <HiOutlineCloudUpload className="mb-2 text-3xl" />
                    <span className="font-semibold">
                      Drag and drop your job description file here, or
                    </span>
                    <span className="font-semibold text-brand-500">
                      Browse files
                    </span>
                  </label>
                  <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                    Supported formats: PDF, DOC, DOCX
                  </p>
                </div>
                {fileName && (
                  <div className="mt-4 text-sm text-gray-600 dark:text-gray-300">
                    Selected file: <strong>{fileName}</strong>
                  </div>
                )}
                <div className="mt-4">
                  <button
                    type="button"
                    className="w-full rounded-md bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
                    onClick={() => setStep(2)}
                  >
                    Fill Details Manually
                  </button>
                </div>
              </div>
            )}

            {/* Step 2: Manual Entry */}
            {step === 2 && (
              <div>
                {/* Role Input */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Role
                  </label>
                  <input
                    type="text"
                    className="mt-1 w-full rounded-md border-[1px] border-gray-300 p-2 focus:border-blue-500 focus:ring-blue-500 dark:bg-navy-600 dark:text-white"
                    placeholder="Enter the role"
                    value={role}
                    onChange={(e) => {
                      const input = e.target.value;
                      setRole(input);
                      setFilteredRoles(
                        jobRoles.filter((r) =>
                          r.toLowerCase().includes(input.toLowerCase())
                        )
                      );
                    }}
                  />
                  {role && (
                    <ul className="mt-2 max-h-32 overflow-auto rounded-md border border-gray-300 bg-white shadow-md dark:bg-navy-600">
                      {filteredRoles.map((suggestedRole, index) => (
                        <li
                          key={index}
                          className="cursor-pointer p-2 hover:bg-gray-100 dark:hover:bg-navy-700"
                          onClick={() => {
                            setRole(suggestedRole);
                            setFilteredRoles([]); // Close dropdown after selection
                          }}
                        >
                          {suggestedRole}
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
                {/* Experience Dropdown */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Experience
                  </label>
                  <select
                    className="mt-1 w-full rounded-md border-[1px] border-gray-300 p-2 focus:border-blue-500 focus:ring-blue-500 dark:bg-navy-600 dark:text-white"
                    value={experience}
                    onChange={(e) => setExperience(e.target.value)}
                  >
                    <option value="">Select experience level</option>
                    <option value="0-1">0-1 years (Fresher)</option>
                    <option value="2-3">2-3 years (Intermediate)</option>
                    <option value="4-5">4-5 years (Experienced)</option>
                    <option value="5+">5+ years (Senior)</option>
                  </select>
                </div>
                {/* Location Input */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Location
                  </label>
                  <input
                    type="text"
                    className="mt-1 w-full rounded-md border-[1px] border-gray-300 p-2 focus:border-blue-500 focus:ring-blue-500 dark:bg-navy-600 dark:text-white"
                    placeholder="Enter the location"
                    value={location}
                    onChange={(e) => {
                      const input = e.target.value;
                      setLocation(input);
                      setFilteredLocations(
                        locations.filter((loc) =>
                          loc.toLowerCase().includes(input.toLowerCase())
                        )
                      );
                    }}
                  />
                  {location && (
                    <ul className="mt-2 max-h-32 overflow-auto rounded-md border border-gray-300 bg-white shadow-md dark:bg-navy-600">
                      {filteredLocations.map((suggestedLocation, index) => (
                        <li
                          key={index}
                          className="cursor-pointer p-2 hover:bg-gray-100 dark:hover:bg-navy-700"
                          onClick={() => {
                            setLocation(suggestedLocation);
                            setFilteredLocations([]); // Close dropdown after selection
                          }}
                        >
                          {suggestedLocation}
                        </li>
                      ))}
                    </ul>
                  )}
                </div>

                {/* Skills Input */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Skills
                  </label>
                  <input
                    type="text"
                    className="mt-1 w-full rounded-md border-[1px] border-gray-300 p-2 focus:border-blue-500 focus:ring-blue-500 dark:bg-navy-600 dark:text-white"
                    placeholder="Type a skill"
                    onChange={(e) => {
                      const input = e.target.value;
                      setFilteredSkills(
                        skillsList.filter((skill) =>
                          skill.toLowerCase().includes(input.toLowerCase())
                        )
                      );
                    }}
                    onKeyDown={(e) => {
                      if (
                        e.key === "Enter" &&
                        e.target.value.trim() !== "" &&
                        !skills.includes(e.target.value.trim())
                      ) {
                        setSkills([...skills, e.target.value.trim()]);
                        e.target.value = ""; // Clear input after adding skill
                      }
                    }}
                  />
                  {filteredSkills.length > 0 && (
                    <ul className="mt-2 max-h-32 overflow-auto rounded-md border border-gray-300 bg-white shadow-md dark:bg-navy-600">
                      {filteredSkills.map((suggestedSkill, index) => (
                        <li
                          key={index}
                          className="cursor-pointer p-2 hover:bg-gray-100 dark:hover:bg-navy-700"
                          onClick={() => {
                            if (!skills.includes(suggestedSkill)) {
                              setSkills([...skills, suggestedSkill]);
                            }
                            setFilteredSkills([]); // Close dropdown after selection
                          }}
                        >
                          {suggestedSkill}
                        </li>
                      ))}
                    </ul>
                  )}
                  <div className="mt-2 flex flex-wrap gap-2">
                    {skills.map((skill, index) => (
                      <span
                        key={index}
                        className="flex items-center rounded-full bg-indigo-100 px-3 py-1 text-xs text-indigo-800"
                      >
                        {skill}
                        <button
                          className="ml-2 text-indigo-600 hover:text-indigo-800"
                          onClick={() =>
                            setSkills(skills.filter((s) => s !== skill))
                          }
                        >
                          ×
                        </button>
                      </span>
                    ))}
                  </div>
                </div>

                <div className="mt-4 flex justify-between">
                  <button
                    type="button"
                    className="flex items-center rounded-md bg-gray-300 px-4 py-2 text-gray-700 hover:bg-gray-400 dark:bg-gray-600 dark:text-white"
                    onClick={() => setStep(1)}
                  >
                    <HiChevronLeft className="mr-2" />
                    Back
                  </button>
                  <button
                    type="button"
                    className="flex items-center rounded-md bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
                    onClick={handleSave}
                  >
                    Create Job Description
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

const EmployerDashboard = () => {
  const jstoken = Cookies.get("jstoken");
  const emp_id = Cookies.get("employerId");
  const navigate = useNavigate();
  const [shortListedCandidatesCount, setShortListedCandidatesCount] =useState(0);
  const [unlockedCandidatesCount, setUnlockedCandidatesCount] = useState(0);
  const [InterviewedCandidatesCount, setInterviewedCandidatesCount] = useState(0);
  const [offeredCandidatesCount, setOfferedCandidatesCount] = useState(0);
  const [empData, setEmpData] = useState({
    name: "Tejas S P",
    plan_name: "PRO",
    creditsLeft: 100,
  });

  const [isModalOpen, setModalOpen] = useState(false);
  const [jobData, setJobData] = useState([]);

  useEffect(() => {
    // Get the values from cookies
    const getAllProfiles = async () => {
      try {
        const dummyProfiles = Array.from({ length: 5 }, (_, index) => ({
          name: `Loading... ${index}`,
        }));

        setJobData((prev) => [...prev, ...dummyProfiles]);

        const page = Math.ceil(jobData.length / 10);

        const data = await fetch(
          `${
            import.meta.env.VITE_APP_HOST
          }/api/v1/getSuggestedCandidates?emp_id=${emp_id}`
        );
        let res = await data.json();
        if (res.candidateProfiles.length > 5) {
          res.candidateProfiles = res.candidateProfiles.slice(0, 5);
        }
        console.log(jobData.length)
        setJobData((prev) => {
          const newProfiles = prev.slice(0, prev.length - 10);
          return [...newProfiles, ...res.candidateProfiles];
        });
      } catch (err) {
        console.log(`Error`, err);
      }
    };
    getAllProfiles();
  }, [emp_id]);
  

  useEffect(() => {
    const fetchCandidates = async () => {
      try {
        const profileData = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/employerProfilesData`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              Authorization: emp_id,
            },
          }
        );
        const profileJson = await profileData.json();
      

        if (profileJson.data) {
          setShortListedCandidatesCount(
            profileJson?.data?.candidate_counts?.shortlisted_count || 0
          );
          setUnlockedCandidatesCount(
            profileJson?.data?.candidate_counts?.unlocked_count || 0
          );
          setInterviewedCandidatesCount(
            profileJson?.data?.candidate_counts?.interviewed_count || 0
          );
          setOfferedCandidatesCount(
            profileJson?.data?.candidate_counts?.offered_count || 0
          );
          setEmpData({
            name: profileJson?.data?.emp_name || "Tejas S P",
            plan_name: profileJson?.data?.plan_name || "PRO",
            creditsLeft: profileJson?.data?.creditsLeft,
          });
        }
      } catch (err) {
        console.error("Error fetching shortlisted profiles:", err);
      }
    };

    fetchCandidates();
  }, [emp_id]);

  
  return (
    <>
      {jstoken ? (
        <div className="mt-2 grid grid-cols-12 gap-x-4 gap-y-4">
          {/* Row 1: Stats and User Info */}
          <div className="card col-span-full rounded-2xl bg-white px-6 py-4 shadow-lg dark:bg-navy-700 dark:text-white">

            <div className="grid grid-cols-12 items-center gap-6">
              {/* User Info & Plan */}
              <div className="col-span-full flex items-center space-x-4 lg:col-span-3">

                <img src={avatar} alt="User Avatar" className="h-16 w-16 rounded-full object-cover shadow-md" />

                <div className="flex flex-col space-y-1">

                  <h3 className="text-xl md:text-2xl font-semibold text-gray-800 dark:text-white">
                    {empData?.name || "TEJAS S P"}
                  </h3>

                  <div>
                    <span className="w-max rounded-full bg-brand-500 px-2 md:px-3 py-1 text-xs font-medium md:font-semibold text-white shadow">
                      {empData?.plan_name || "PRO"} Plan
                    </span>
                  </div>

                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-500 dark:text-gray-300">
                      Credits:
                    </span>
                    <span className="text-sm md:text-lg font-bold text-brand-500">
                      {empData?.creditsLeft || 0}
                    </span>
                  </div>

                </div>

              </div>

              {/* Stats: Shortlisted, Unlocked,Interviews and offers */}
              <div className="flex-wrap justify-center  md:flex-nowrap gap-8 md:gap-0  col-span-full flex items-center md:space-x-4 lg:col-span-5">
                <StatCard
                  title="Shortlisted"
                  value={shortListedCandidatesCount}
                  gradientClass="bg-gradient-to-r from-blue-500 to-blue-700"
                  onClick={() =>
                    navigate("/employer/track-candidates?tab=Shortlisted")
                  }
                />
                <StatCard
                  title="Unlocked"
                  value={unlockedCandidatesCount}
                  gradientClass="bg-gradient-to-r from-yellow-500 to-yellow-700"
                  onClick={() =>
                    navigate("/employer/track-candidates?tab=Unlocked")
                  }
                />
                <StatCard
                  title="Interviews"
                  value={InterviewedCandidatesCount}
                  gradientClass="bg-gradient-to-r from-orange-500 to-orange-700"
                  onClick={() =>
                    navigate("/employer/track-candidates?tab=Interview")
                  }
                />
                <StatCard
                  title="Offers"
                  value={offeredCandidatesCount}
                  gradientClass="bg-gradient-to-r from-green-500 to-green-700"
                  onClick={() =>
                    navigate("/employer/track-candidates?tab=Offers")
                  }
                />
              </div>

              <div className="w-full p-0 md:p-4 ml-8 md:ml-20 lg:ml-0 col-span-10 md:col-span-8 lg:col-span-4 flex flex-col items-center rounded-lg bg-white  dark:bg-navy-700">

                <h3 className="w-full text-center lg:text-left text-sm sm:text-lg font-semibold text-gray-800 dark:text-white">
                  Match Candidates on JD
                </h3>

                <p className="mb-2 text-[14px] sm:text-sm text-center md:text-left text-gray-500 dark:text-gray-300">
                  Upload a job description to match suitable candidates.
                </p>

                <button
                  className="flex flex-row items-center justify-center -ml-0 lg:-ml-[70px] xl:-ml-[90px] text-[12px] sm:text-base rounded-lg bg-brand-500 px-2 py-2 text-white transition hover:bg-brand-600 dark:bg-brand-600 dark:hover:bg-brand-700"
                  onClick={() => setModalOpen(true)}
                >
                  <HiOutlineSparkles className="mr-1 text-[13px] sm:text-lg" /> Upload Job
                  Description
                </button>
              </div>

            </div>
            
          </div>

          <div className="col-span-full grid grid-cols-12 gap-4">
            {/* Button to Open Modal */}

            {/* Modal for Uploading Job Description */}
            <JobDescriptionModal
              isOpen={isModalOpen}
              onClose={() => setModalOpen(false)}
            />

            {/* Positions List */}
            <div className="col-span-full rounded-2xl bg-white p-6 shadow-lg dark:bg-navy-700 dark:text-white">

              <div className="mb-4flex items-center justify-between">

                <h2 className="flex flex-row items-center text-lg font-semibold text-gray-800 dark:text-white">
                  <HiOutlineBriefcase className="mr-2 text-lg" /> Positions
                  You're Hiring For
                </h2>

              </div>
              {jobData && jobData.length && (
                <div
                  className={`mt-4 ${ jobData.length < 4 ? "w-full": "grid grid-1 md:grid-cols-2 gap-x-4 gap-y-4"
                  }`}
                >
                  {jobData.map((job) =>
                    job.id ? (<PositionsCard key={job.id} job={job} />) : ( <ProfileSkelLoader key={`skeleton-${Math.random()}`} />
                    )
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      ) : (
        <h2>lol signin</h2>
      )}
    </>
  );
};

export default EmployerDashboard;
