import React, { useEffect } from "react";
import JobCard from "./components/JobCard";
import { useState } from "react";
import CreateVR from "./components/CreateVR";
import Cookies from "js-cookie";
import { useNavigate } from "react-router-dom";
import VideoProfileCard from "./components/VideoProfileCard";
import VidProfPopup from "./components/VidProfPopup";
import avatar from "assets/img/avatars/avatar4.png";
import { HiOutlineSparkles, HiVideoCamera } from "react-icons/hi";
// import Loader from "../../components/Loader";
import SmLoader from "../../components/SmLoader";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import {
  Eye,
  Video,
  Star,
  ClipboardCheck,
  VideoIcon,
  Unlock,
  MousePointerClick,
} from "lucide-react";
import MockInterviewPopup from "./components/MockInterPopup";
import CreateMR from "./components/CreateMR";

const Dashboard = () => {
  // const navigate = useNavigate();
  // const getFormDataFromCookie = () => {
  //   const cookieData = Cookies.get("formData");
  //   cookieData ? alert(cookieData) : alert("no video profile created");
  // };
  const jstoken = Cookies.get("jstoken");
  const candId = Cookies.get("candId");
  const [profile_picture, setProfilePicture] = useState(avatar);
  const [imageError, setImageError] = useState(false);

  // const jobData = [
  //   {
  //     id: 1,
  //     title: "Frontend Developer",
  //     company: "Jio",
  //     image: "https://img.naukimg.com/logo_images/groups/v1/2095704.gif",
  //     openings: "3",
  //     url: "https://www.naukri.com/job-listings-front-end-developer-jio-bengaluru-2-to-6-years-130824006621?src=seo_srp&sid=17284557721049113&xp=2&px=1", // Add the job URL here
  //   },
  //   {
  //     id: 2,
  //     title: "TypeScript Developer",
  //     company: "Repucom",
  //     image:
  //       "https://media.licdn.com/dms/image/v2/C4D0BAQGktkfY0ZbGCA/company-logo_200_200/company-logo_200_200/0/1631367548711?e=2147483647&v=beta&t=KkmfYrMzqJkBj2B7UxtvNkUqASPqyWjD-lcvRthrfUk",
  //     openings: "1",
  //     url: "https://www.naukri.com/job-listings-front-end-developers-react-typescript-repucom-bengaluru-2-to-8-years-031024503089?src=seo_srp&sid=17284557721049113&xp=5&px=1", // Add the job URL here
  //   },
  //   {
  //     id: 3,
  //     title: "Front - End Developer",
  //     company: "Digital Glyde",
  //     image:
  //       "https://media.licdn.com/dms/image/v2/C4E0BAQGZhHvix75w9A/company-logo_200_200/company-logo_200_200/0/1630597899639?e=2147483647&v=beta&t=0FXz6WwtPi3n9_KCrKqNYYQy_pivEHzoiH6V0G4rJsg",
  //     openings: "1",
  //     url: "https://www.naukri.com/job-listings-front-end-developer-digital-glyde-kolkata-mumbai-new-delhi-hyderabad-pune-chennai-bengaluru-0-to-3-years-170524500933?src=seo_srp&sid=17284557721049113&xp=10&px=1", // Add the job URL here
  //   },
  //   {
  //     id: 4,
  //     title: "Front End Developer",
  //     company: "PwC India",
  //     image:
  //       "https://s32519.pcdn.co/wp-content/uploads/2022/12/partner-pwc-512px.png",
  //     openings: "3",
  //     url: "https://www.naukri.com/job-listings-front-end-developer-pwc-india-hyderabad-chennai-bengaluru-5-to-7-years-110924020786?src=seo_srp&sid=17284557721049113&xp=15&px=1", // Add the job URL here
  //   },
  // ];

  const stats = {
    interviewsAttended: 5,
    shortlists: 3,
    videoProfilesCreated: 10,
  };

  const [videoProfiles, setVideoProfiles] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [userStats, setUserStats] = useState([
    {
      count: 15,
      label: "Shortlists",
      Icon: ClipboardCheck,
    },
    {
      count: 23,
      label: "Views",
      Icon: Eye,
    },
    {
      count: 23,
      label: "Clicks",
      Icon: MousePointerClick,
    },
    {
      count: 3,
      label: "UnLocked",
      Icon: Unlock,
    },
  ]);
  const [candData, setCandData] = useState("");
  const [loadingInfo, setLoadingInfo] = useState(false);
  const [jobData, setJobData] = useState([]);
  // const jobData={};

  // const resjobdata = fetch(
  //   `${import.meta.env.VITE_APP_HOST}/api/v1/suggestedJobs`,
  //   {
  //     method: "GET",
  //     headers: {
  //       "Content-Type": "application/json",
  //     },
  //   }
  // );

  // if (resjobdata.ok) {
  //   jobData = resjobdata.json();
  //   console.log(jobData);
  // }

  useEffect(() => {
    const fetchJobData = async () => {
      try {
        const resjobdata = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/suggestedJobs`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
          }
        );

        if (resjobdata.ok) {
          const data = await resjobdata.json();
          console.log("Fetched job data:", data);

          // Check if the data is an array; if not, set it to an empty array or adjust as needed
          setJobData(Array.isArray(data) ? data : []);
        } else {
          console.error(
            "Error fetching job data:",
            resjobdata.status,
            resjobdata.statusText
          );
        }
      } catch (error) {
        console.error("Network error:", error);
      }
    };

    fetchJobData();
  }, []);

  useEffect(() => {
    const fetchCandidateInfo = async () => {
      try {
        // Make the API request with the dynamic candId
        setLoadingInfo(true);
        const response = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/candidate/${candId}`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
          }
        );

        if (response.ok) {
          const data = await response.json();
          console.log(data)
          // data = JSON.parse(data)
          // Iterate over the response and pick out the necessary fields
          const newProfiles = data.videoProfiles.map((profile) => ({
            vpid: profile.video_profile_id,
            role: profile.role,
            skills: profile.skills.split(",").map((skill) => skill.trim()), // Convert skills to an array
            status: profile.status,
          }));

          // Append the new profiles to the existing videoProfiles list
          setVideoProfiles((prevProfiles) => [...prevProfiles, ...newProfiles]);

          // Iterate over the response and pick out the necessary fields
          setCandData(data.candidateProfile[0]);
          setUserStats([
            {
              count: data.candidateProfile[0].statusCounts.shortlisted,
              label: "Shortlists",
              Icon: ClipboardCheck,
            },
            {
              count: data.candidateProfile[0].interactions.view,
              label: "Views",
              Icon: Eye,
            },
            {
              count: data.candidateProfile[0].interactions.click,
              label: "Clicks",
              Icon: MousePointerClick,
            },
            {
              count: data.candidateProfile[0].statusCounts.unlocked,
              label: "UnLocked",
              Icon: Unlock,
            },
          ]);
          data.candidateProfile[0].profile_picture &&
            setProfilePicture(
              `${import.meta.env.VITE_APP_HOST}/${
                data.candidateProfile[0].profile_picture
              }`
            );
          // Append the new profiles to the existing videoProfiles list
          // setVideoProfiles((prevProfiles) => [...prevProfiles, ...newProfiles]);
        } else {
          console.error(
            "Error fetching video profiles:",
            response.status,
            response.statusText
          );
        }
      } catch (error) {
        console.error("Network error:", error);
      }
      setLoadingInfo(false);
    };

    // Only fetch if candId is provided
    if (candId) {
      fetchCandidateInfo();
    }
  }, [candId]);

  const [createVRpopup, setcreateVRpopup] = useState(false);
  const [createMRpopup, setcreateMRpopup] = useState(false);
  const [showMockInterviewPopup, setShowMockInterviewPopup] = useState(false);
  const [showVideoProfilePopup, setShowVideoProfilePopup] = useState(false);

  const togglePopupVR = () => {
    setcreateVRpopup(!createVRpopup);
  };

  const togglePopupMR = () => {
    setcreateMRpopup(!createMRpopup);
  };

  const toggleMockInterviewPopup = () => {
    setShowMockInterviewPopup(!showMockInterviewPopup);
  };

  const toggleVideoProfilePopup = () => {
    setShowVideoProfilePopup(!showVideoProfilePopup);
  };

  return (
    <>
      {jstoken ? (
        <div className="mt-1 grid grid-cols-12 gap-x-4 gap-y-4">
          {/* Start Intro */}
          <div className="card col-span-full rounded-xl bg-white p-0 dark:bg-navy-700  dark:text-white lg:col-span-8 2xl:col-span-7">
            <div className="relative grid h-full  grid-cols-12 overflow-hidden rounded-lg bg-white px-6 py-6  dark:bg-navy-700 dark:text-white">
              <div className="col-span-full inline-flex flex-col items-start justify-center space-y-3 md:col-span-7">
                <h1 className="text-2xl font-bold text-gray-800 dark:text-white">
                  Welcome Back,{" "}
                  {loadingInfo ? (
                    <span>
                      {/* <Skeleton width={150} height={20} inline={true}/> */}
                    </span> // Skeleton
                  ) : (
                    <span className="text-brand-500">{candData.cand_name}</span>
                  )}
                </h1>
                <p className="text-sm text-gray-600">
                  Ready to enhance your profile? <br></br>Create a professional
                  video resume and stand out to potential employers.
                </p>

                <div className="flex flex-col md:flex-row items-start m-auto justify-center md:justify-around gap-2">
                  <a
                    id="createvr"
                    className="inline-flex cursor-pointer items-center rounded-xl bg-brand-500 px-3 py-3 font-semibold text-white shadow-md transition-all duration-200 ease-in-out hover:bg-brand-600 w-full md:w-auto"
                    onClick={togglePopupVR}
                  >
                    <svg
                      className="mr-3 h-6 w-6"
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <path
                        fill="#FFFFFF"
                        d="M17 10.5v-2c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v7c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2v-2l4 2v-7l-4 2Z"
                      />
                    </svg>
                    Create Video Resume
                  </a>
                  <a
                    id="createvr"
                    className="inline-flex cursor-pointer items-center rounded-xl bg-yellow-500 px-3 py-3 font-semibold text-white shadow-md transition-all duration-200 ease-in-out hover:bg-yellow-600 w-full md:w-auto"
                    onClick={togglePopupMR}
                  >
                    <svg
                      className="mr-3 h-6 w-6"
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <path
                        fill="#FFFFFF"
                        d="M17 10.5v-2c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v7c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2v-2l4 2v-7l-4 2Z"
                      />
                    </svg>
                    Practice Mock Interview
                  </a>
                </div>
              </div>

              <div className="col-span-full items-center justify-end md:col-span-5 md:flex">
                <img
                  src="https://www.jobma.com/blog/wp-content/uploads/2013/08/Video-Resumes-The-Importance-of-Audio-1-300x217.png"
                  alt="Illustration for Video Resume"
                  className="h-auto w-full max-w-[200px] object-cover hidden md:block"
                />
              </div>
            </div>
          </div>

          <div className="card col-span-full rounded-xl bg-white p-6 dark:bg-navy-700 dark:text-white lg:col-span-4 2xl:col-span-5">
            <div className="mb-3 flex items-center justify-start gap-10">
              <div className="flex items-center">
                {imageError ? (
                  // Render this div if image load fails
                  <div className="mr-4 flex h-14 w-14 items-center justify-center rounded-full bg-brand-600 text-xl font-semibold text-white">
                    {candData && candData?.cand_name[0]?.toUpperCase()}
                  </div>
                ) : (
                  // Render the image by default
                  <img
                    className="mr-4 h-14 w-14 rounded-full border-2"
                    style={{
                      borderColor: "#F4F7FD",
                    }}
                    src={profile_picture}
                    alt={candData?.cand_name}
                    onError={() => setImageError(true)} // Set error to true if image fails to load
                  />
                )}
                <div className="flex flex-col items-start gap-1">
                  <h3 className="text-xl font-bold">{candData.cand_name}</h3>
                  <span className=" inline-flex items-center rounded-full bg-brand-200 px-2 py-1 text-[10px] font-semibold text-white">
                    PRO Plan
                  </span>
                </div>
              </div>
            </div>
            <div className="mb-3 grid grid-cols-4 gap-2">
              {userStats.map(({ count, label, Icon }) => (
                <div className="flex flex-col" key={label}>
                  <div className="flex items-center">
                    <span className="text-2xl font-bold text-gray-800 dark:text-white">
                      {count > 9 ? count : `0${count}`}
                    </span>
                  </div>
                  <div className="flex items-center">
                    <Icon className="mr-1 h-3 w-3 text-brand-500" />
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      {label}
                    </span>
                  </div>
                </div>
              ))}
            </div>

            <hr className="my-2 border-gray-300 dark:border-gray-600" />
            <div>
              <div className="mb-2 flex items-center justify-between">
                <div className="flex items-center">
                  <Video className="mr-1 h-4 w-4 text-brand-500" />
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    Video Resumes
                  </span>
                </div>
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  7 / 10
                </span>
              </div>
              <div className="mb-2 h-2.5 w-full rounded-full bg-gray-200 dark:bg-gray-700">
                <div
                  className="h-2.5 rounded-full bg-brand-500"
                  style={{ width: "70%" }}
                ></div>
              </div>
              <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
                <span>7 Created</span>
                <span>3 Left</span>
              </div>
            </div>
          </div>

          {/* 
            <div className="flex items-center justify-between">
              <div className="flex gap-2 text-sm font-medium">
                <button className="rounded-lg bg-gray-100 px-2 py-1 hover:bg-gray-200 dark:bg-navy-600">
                  Shortlists{" "}
                  <span className="font-bold">{stats.shortlists}</span>
                </button>
                <button className="rounded-lg bg-gray-100 px-2 py-1 hover:bg-gray-200 dark:bg-navy-600">
                  Interviews{" "}
                  <span className="font-bold">{stats.interviewsAttended}</span>
                </button>
                <button className="rounded-lg bg-gray-100 px-2 py-1 hover:bg-gray-200 dark:bg-navy-600">
                  Visumes™{" "}
                  <span className="font-bold">
                    {stats.videoProfilesCreated}
                  </span>
                </button>
              </div>
            </div> */}

          {/* End Intro */}
          {/* Start Short Progress Card */}
            <div className="card col-span-full h-[300px] overflow-auto rounded-xl bg-white p-4 scrollbar-thin hover:scrollbar-thumb-gray-900 dark:bg-navy-700 dark:text-white dark:hover:scrollbar-thumb-gray-300 lg:col-span-7">
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white">
                <HiOutlineSparkles className="inline text-lg text-indigo-500" />{" "}
                Suggested Jobs
              </h2>

                {jobData.length > 0 ? (
                  <div className="space-y-4">
                    {jobData.map((job) => (
                      <div key={job.id} className="grid grid-cols-12 gap-4 px-5 py-5 sm:px-9">
                        <JobCard iconUrl={job.image} job={job} />
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="flex flex-col items-center mt-4">
                    <img
                      src="https://cdni.iconscout.com/illustration/premium/thumb/artificial-intelligence-search-illustration-download-in-svg-png-gif-file-formats--ai-view-business-technology-pack-illustrations-5408137.png" // Replace with the actual image URL
                      alt="AI Searching jobs"
                      className="mb-4 mt-4 w-[10rem]"
                    />
                    <p className="text-center text-gray-500">
                      AI is working to suggest jobs. Hang tight.
                    </p>
                  </div>
                )}
              </div>
            

          <div className="card scrollbar-hide hover:scrollbar-thumb-rounded-full col-span-full h-[300px] overflow-auto rounded-md bg-white p-4 scrollbar-thin hover:scrollbar-thumb-gray-900 dark:bg-navy-700 dark:text-white dark:hover:scrollbar-thumb-gray-300 lg:col-span-5 2xl:col-span-5">
            <h2 className="mb-4 ml-2 flex items-center gap-2 text-lg font-semibold text-gray-800 dark:text-white">
              <HiVideoCamera className="inline text-lg text-indigo-500" /> Your Visumes
            </h2>
            {isLoading ? (
              <SmLoader text={"Loading..."} />
            ) : videoProfiles.length === 0 ? (
              <div className="flex flex-col items-center">
                <img
                  src="https://img.freepik.com/premium-vector/no-data-concept-illustration_86047-488.jpg?size=626&ext=jpg&ga=GA1.1.2008272138.1727481600&semt=ais_hybrid" // Replace with the actual image URL
                  alt="No Video Profiles"
                  className="mb-4 mt-4 w-[10rem]"
                />
                <p className="text-center text-gray-500">
                  Create your video resume to view it here.
                </p>
              </div>
            ) : (
              <>
                {videoProfiles.map((profile) => (
                  <VideoProfileCard
                    key={profile.vpid}
                    profile={profile}
                    toggleVideoProfilePopup={toggleVideoProfilePopup}
                  />
                ))}
              </>
            )}
          </div>
        </div>
      ) : (
        <h2>Please SignIn</h2>
      )}

      {/* Start Popup for Video Resume */}
      {createVRpopup && <CreateVR key="create-vr" togglePopupVR={togglePopupVR} />}
      {createMRpopup && <CreateMR key="create-mr" togglePopupVR={togglePopupMR} />}
      {/* End Popup for Video Resume */}

      {/* Video profile popup */}
      {showVideoProfilePopup && (
        <VidProfPopup key="vid-prof" toggleVideoProfilePopup={toggleVideoProfilePopup} />
      )}
      {showMockInterviewPopup && (
        <MockInterviewPopup key="mock-interview" toggleVideoProfilePopup={toggleMockInterviewPopup} />
      )}
    </>
  );
};

export default Dashboard;
