import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Button from '../smaller_comp/Button';

export default function EndInterviewSection() {
  const navigate = useNavigate();
  const [videoUrl, setVideoUrl] = useState(null);

  useEffect(() => {
    // Get the saved video from localStorage
    const savedVideo = localStorage.getItem('interviewVideo');
    if (savedVideo) {
      setVideoUrl(savedVideo);
    }
  }, []);

  const handleNavigate = () => {
    // Clean up the video from localStorage before navigating
    localStorage.removeItem('interviewVideo');
    navigate("/candidate/dashboard");
  };

  return (
    <main className="container mx-auto flex h-full items-start justify-center pt-0">
      <div className="m-auto mt-0 flex w-3/4 flex-col rounded-xl bg-white p-6 shadow-lg">
        <h2 className="mb-4 text-center text-xl font-semibold">
          Your Video Resume Has Been Successfully Submitted.
        </h2>

        {videoUrl && (
          <div className="mb-6 w-full max-w-2xl mx-auto rounded-lg overflow-hidden">
            <video
              src={videoUrl}
              controls
              className="w-full h-full object-cover"
              style={{ maxHeight: '400px' }}
            />
          </div>
        )}
        
        <Button onClick={handleNavigate} className="mx-auto">
          Back to Dashboard
        </Button>
      </div>
    </main>
  );
}