const fs = require("fs");
const path = require("path");
const db = require("../config/db");
const { parse } = require("csv-parse");

const importCSV = (filePath, tableName, columnNames) => {
  fs.createReadStream(filePath)
    .pipe(parse({ delimiter: ",", columns: true }))
    .on("data", async (row) => {
      try {
        const values = Object.values(row);
        const placeholders = values.map(() => "?").join(", ");
        const query = `INSERT INTO ${tableName} (${columnNames}) VALUES (${placeholders})`;

        await db.promise().query(query, values);
      } catch (err) {
        console.error(`Error inserting row into ${tableName}:`, err.message);
      }
    })
    .on("end", () => {
      console.log(`Finished importing ${filePath} into ${tableName}`);
    })
    .on("error", (err) => {
      console.error(`Error reading ${filePath}:`, err.message);
    });
};

// Import roles.csv
const rolesPath = path.join(__dirname, "../utils/files/roles.csv");
importCSV(rolesPath, "roles", "role_name");

// Import skills.csv
const skillsPath = path.join(__dirname, "../utils/files/skills.csv");
importCSV(skillsPath, "skills", "skill_name");
