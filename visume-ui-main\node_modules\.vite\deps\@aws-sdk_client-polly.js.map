{"version": 3, "sources": ["../../@aws-sdk/client-polly/dist-es/auth/httpAuthSchemeProvider.js", "../../@aws-sdk/client-polly/dist-es/endpoint/EndpointParameters.js", "../../@aws-sdk/client-polly/package.json", "../../@aws-sdk/client-polly/dist-es/endpoint/ruleset.js", "../../@aws-sdk/client-polly/dist-es/endpoint/endpointResolver.js", "../../@aws-sdk/client-polly/dist-es/runtimeConfig.shared.js", "../../@aws-sdk/client-polly/dist-es/runtimeConfig.browser.js", "../../@aws-sdk/client-polly/dist-es/auth/httpAuthExtensionConfiguration.js", "../../@aws-sdk/client-polly/dist-es/runtimeExtensions.js", "../../@aws-sdk/client-polly/dist-es/PollyClient.js", "../../@aws-sdk/client-polly/dist-es/models/PollyServiceException.js", "../../@aws-sdk/client-polly/dist-es/models/models_0.js", "../../@aws-sdk/client-polly/dist-es/protocols/Aws_restJson1.js", "../../@aws-sdk/client-polly/dist-es/commands/DeleteLexiconCommand.js", "../../@aws-sdk/client-polly/dist-es/commands/DescribeVoicesCommand.js", "../../@aws-sdk/client-polly/dist-es/commands/GetLexiconCommand.js", "../../@aws-sdk/client-polly/dist-es/commands/GetSpeechSynthesisTaskCommand.js", "../../@aws-sdk/client-polly/dist-es/commands/ListLexiconsCommand.js", "../../@aws-sdk/client-polly/dist-es/commands/ListSpeechSynthesisTasksCommand.js", "../../@aws-sdk/client-polly/dist-es/commands/PutLexiconCommand.js", "../../@aws-sdk/client-polly/dist-es/commands/StartSpeechSynthesisTaskCommand.js", "../../@aws-sdk/client-polly/dist-es/commands/SynthesizeSpeechCommand.js", "../../@aws-sdk/client-polly/dist-es/Polly.js", "../../@aws-sdk/client-polly/dist-es/pagination/ListSpeechSynthesisTasksPaginator.js"], "sourcesContent": ["import { resolveAwsSdkSigV4Config, } from \"@aws-sdk/core\";\nimport { getSmithyContext, normalizeProvider } from \"@smithy/util-middleware\";\nexport const defaultPollyHttpAuthSchemeParametersProvider = async (config, context, input) => {\n    return {\n        operation: getSmithyContext(context).operation,\n        region: (await normalizeProvider(config.region)()) ||\n            (() => {\n                throw new Error(\"expected `region` to be configured for `aws.auth#sigv4`\");\n            })(),\n    };\n};\nfunction createAwsAuthSigv4HttpAuthOption(authParameters) {\n    return {\n        schemeId: \"aws.auth#sigv4\",\n        signingProperties: {\n            name: \"polly\",\n            region: authParameters.region,\n        },\n        propertiesExtractor: (config, context) => ({\n            signingProperties: {\n                config,\n                context,\n            },\n        }),\n    };\n}\nexport const defaultPollyHttpAuthSchemeProvider = (authParameters) => {\n    const options = [];\n    switch (authParameters.operation) {\n        default: {\n            options.push(createAwsAuthSigv4HttpAuthOption(authParameters));\n        }\n    }\n    return options;\n};\nexport const resolveHttpAuthSchemeConfig = (config) => {\n    const config_0 = resolveAwsSdkSigV4Config(config);\n    return {\n        ...config_0,\n    };\n};\n", "export const resolveClientEndpointParameters = (options) => {\n    return {\n        ...options,\n        useDualstackEndpoint: options.useDualstackEndpoint ?? false,\n        useFipsEndpoint: options.useFipsEndpoint ?? false,\n        defaultSigningName: \"polly\",\n    };\n};\nexport const commonParams = {\n    UseFIPS: { type: \"builtInParams\", name: \"useFipsEndpoint\" },\n    Endpoint: { type: \"builtInParams\", name: \"endpoint\" },\n    Region: { type: \"builtInParams\", name: \"region\" },\n    UseDualStack: { type: \"builtInParams\", name: \"useDualstackEndpoint\" },\n};\n", "{\n  \"name\": \"@aws-sdk/client-polly\",\n  \"description\": \"AWS SDK for JavaScript Polly Client for Node.js, <PERSON>rowser and React Native\",\n  \"version\": \"3.699.0\",\n  \"scripts\": {\n    \"build\": \"concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types'\",\n    \"build:cjs\": \"node ../../scripts/compilation/inline client-polly\",\n    \"build:es\": \"tsc -p tsconfig.es.json\",\n    \"build:include:deps\": \"lerna run --scope $npm_package_name --include-dependencies build\",\n    \"build:types\": \"tsc -p tsconfig.types.json\",\n    \"build:types:downlevel\": \"downlevel-dts dist-types dist-types/ts3.4\",\n    \"clean\": \"rimraf ./dist-* && rimraf *.tsbuildinfo\",\n    \"extract:docs\": \"api-extractor run --local\",\n    \"generate:client\": \"node ../../scripts/generate-clients/single-service --solo polly\"\n  },\n  \"main\": \"./dist-cjs/index.js\",\n  \"types\": \"./dist-types/index.d.ts\",\n  \"module\": \"./dist-es/index.js\",\n  \"sideEffects\": false,\n  \"dependencies\": {\n    \"@aws-crypto/sha256-browser\": \"5.2.0\",\n    \"@aws-crypto/sha256-js\": \"5.2.0\",\n    \"@aws-sdk/client-sso-oidc\": \"3.699.0\",\n    \"@aws-sdk/client-sts\": \"3.699.0\",\n    \"@aws-sdk/core\": \"3.696.0\",\n    \"@aws-sdk/credential-provider-node\": \"3.699.0\",\n    \"@aws-sdk/middleware-host-header\": \"3.696.0\",\n    \"@aws-sdk/middleware-logger\": \"3.696.0\",\n    \"@aws-sdk/middleware-recursion-detection\": \"3.696.0\",\n    \"@aws-sdk/middleware-user-agent\": \"3.696.0\",\n    \"@aws-sdk/region-config-resolver\": \"3.696.0\",\n    \"@aws-sdk/types\": \"3.696.0\",\n    \"@aws-sdk/util-endpoints\": \"3.696.0\",\n    \"@aws-sdk/util-user-agent-browser\": \"3.696.0\",\n    \"@aws-sdk/util-user-agent-node\": \"3.696.0\",\n    \"@smithy/config-resolver\": \"^3.0.12\",\n    \"@smithy/core\": \"^2.5.3\",\n    \"@smithy/fetch-http-handler\": \"^4.1.1\",\n    \"@smithy/hash-node\": \"^3.0.10\",\n    \"@smithy/invalid-dependency\": \"^3.0.10\",\n    \"@smithy/middleware-content-length\": \"^3.0.12\",\n    \"@smithy/middleware-endpoint\": \"^3.2.3\",\n    \"@smithy/middleware-retry\": \"^3.0.27\",\n    \"@smithy/middleware-serde\": \"^3.0.10\",\n    \"@smithy/middleware-stack\": \"^3.0.10\",\n    \"@smithy/node-config-provider\": \"^3.1.11\",\n    \"@smithy/node-http-handler\": \"^3.3.1\",\n    \"@smithy/protocol-http\": \"^4.1.7\",\n    \"@smithy/smithy-client\": \"^3.4.4\",\n    \"@smithy/types\": \"^3.7.1\",\n    \"@smithy/url-parser\": \"^3.0.10\",\n    \"@smithy/util-base64\": \"^3.0.0\",\n    \"@smithy/util-body-length-browser\": \"^3.0.0\",\n    \"@smithy/util-body-length-node\": \"^3.0.0\",\n    \"@smithy/util-defaults-mode-browser\": \"^3.0.27\",\n    \"@smithy/util-defaults-mode-node\": \"^3.0.27\",\n    \"@smithy/util-endpoints\": \"^2.1.6\",\n    \"@smithy/util-middleware\": \"^3.0.10\",\n    \"@smithy/util-retry\": \"^3.0.10\",\n    \"@smithy/util-stream\": \"^3.3.1\",\n    \"@smithy/util-utf8\": \"^3.0.0\",\n    \"tslib\": \"^2.6.2\"\n  },\n  \"devDependencies\": {\n    \"@tsconfig/node16\": \"16.1.3\",\n    \"@types/node\": \"^16.18.96\",\n    \"concurrently\": \"7.0.0\",\n    \"downlevel-dts\": \"0.10.1\",\n    \"rimraf\": \"3.0.2\",\n    \"typescript\": \"~4.9.5\"\n  },\n  \"engines\": {\n    \"node\": \">=16.0.0\"\n  },\n  \"typesVersions\": {\n    \"<4.0\": {\n      \"dist-types/*\": [\n        \"dist-types/ts3.4/*\"\n      ]\n    }\n  },\n  \"files\": [\n    \"dist-*/**\"\n  ],\n  \"author\": {\n    \"name\": \"AWS SDK for JavaScript Team\",\n    \"url\": \"https://aws.amazon.com/javascript/\"\n  },\n  \"license\": \"Apache-2.0\",\n  \"browser\": {\n    \"./dist-es/runtimeConfig\": \"./dist-es/runtimeConfig.browser\"\n  },\n  \"react-native\": {\n    \"./dist-es/runtimeConfig\": \"./dist-es/runtimeConfig.native\"\n  },\n  \"homepage\": \"https://github.com/aws/aws-sdk-js-v3/tree/main/clients/client-polly\",\n  \"repository\": {\n    \"type\": \"git\",\n    \"url\": \"https://github.com/aws/aws-sdk-js-v3.git\",\n    \"directory\": \"clients/client-polly\"\n  }\n}\n", "const s = \"required\", t = \"fn\", u = \"argv\", v = \"ref\";\nconst a = true, b = \"isSet\", c = \"booleanEquals\", d = \"error\", e = \"endpoint\", f = \"tree\", g = \"PartitionResult\", h = { [s]: false, \"type\": \"String\" }, i = { [s]: true, \"default\": false, \"type\": \"Boolean\" }, j = { [v]: \"Endpoint\" }, k = { [t]: c, [u]: [{ [v]: \"UseFIPS\" }, true] }, l = { [t]: c, [u]: [{ [v]: \"UseDualStack\" }, true] }, m = {}, n = { [t]: \"getAttr\", [u]: [{ [v]: g }, \"supportsFIPS\"] }, o = { [t]: c, [u]: [true, { [t]: \"getAttr\", [u]: [{ [v]: g }, \"supportsDualStack\"] }] }, p = [k], q = [l], r = [{ [v]: \"Region\" }];\nconst _data = { version: \"1.0\", parameters: { Region: h, UseDualStack: i, UseFIPS: i, Endpoint: h }, rules: [{ conditions: [{ [t]: b, [u]: [j] }], rules: [{ conditions: p, error: \"Invalid Configuration: FIPS and custom endpoint are not supported\", type: d }, { conditions: q, error: \"Invalid Configuration: Dualstack and custom endpoint are not supported\", type: d }, { endpoint: { url: j, properties: m, headers: m }, type: e }], type: f }, { conditions: [{ [t]: b, [u]: r }], rules: [{ conditions: [{ [t]: \"aws.partition\", [u]: r, assign: g }], rules: [{ conditions: [k, l], rules: [{ conditions: [{ [t]: c, [u]: [a, n] }, o], rules: [{ endpoint: { url: \"https://polly-fips.{Region}.{PartitionResult#dualStackDnsSuffix}\", properties: m, headers: m }, type: e }], type: f }, { error: \"FIPS and DualStack are enabled, but this partition does not support one or both\", type: d }], type: f }, { conditions: p, rules: [{ conditions: [{ [t]: c, [u]: [n, a] }], rules: [{ endpoint: { url: \"https://polly-fips.{Region}.{PartitionResult#dnsSuffix}\", properties: m, headers: m }, type: e }], type: f }, { error: \"FIPS is enabled but this partition does not support FIPS\", type: d }], type: f }, { conditions: q, rules: [{ conditions: [o], rules: [{ endpoint: { url: \"https://polly.{Region}.{PartitionResult#dualStackDnsSuffix}\", properties: m, headers: m }, type: e }], type: f }, { error: \"DualStack is enabled but this partition does not support DualStack\", type: d }], type: f }, { endpoint: { url: \"https://polly.{Region}.{PartitionResult#dnsSuffix}\", properties: m, headers: m }, type: e }], type: f }], type: f }, { error: \"Invalid Configuration: Missing Region\", type: d }] };\nexport const ruleSet = _data;\n", "import { awsEndpointFunctions } from \"@aws-sdk/util-endpoints\";\nimport { customEndpointFunctions, EndpointCache, resolveEndpoint } from \"@smithy/util-endpoints\";\nimport { ruleSet } from \"./ruleset\";\nconst cache = new EndpointCache({\n    size: 50,\n    params: [\"Endpoint\", \"Region\", \"UseDualStack\", \"UseFIPS\"],\n});\nexport const defaultEndpointResolver = (endpointParams, context = {}) => {\n    return cache.get(endpointParams, () => resolveEndpoint(ruleSet, {\n        endpointParams: endpointParams,\n        logger: context.logger,\n    }));\n};\ncustomEndpointFunctions.aws = awsEndpointFunctions;\n", "import { AwsSdkSigV4Signer } from \"@aws-sdk/core\";\nimport { NoOpLogger } from \"@smithy/smithy-client\";\nimport { parseUrl } from \"@smithy/url-parser\";\nimport { fromBase64, toBase64 } from \"@smithy/util-base64\";\nimport { sdkStreamMixin } from \"@smithy/util-stream\";\nimport { fromUtf8, toUtf8 } from \"@smithy/util-utf8\";\nimport { defaultPollyHttpAuthSchemeProvider } from \"./auth/httpAuthSchemeProvider\";\nimport { defaultEndpointResolver } from \"./endpoint/endpointResolver\";\nexport const getRuntimeConfig = (config) => {\n    return {\n        apiVersion: \"2016-06-10\",\n        base64Decoder: config?.base64Decoder ?? fromBase64,\n        base64Encoder: config?.base64Encoder ?? toBase64,\n        disableHostPrefix: config?.disableHostPrefix ?? false,\n        endpointProvider: config?.endpointProvider ?? defaultEndpointResolver,\n        extensions: config?.extensions ?? [],\n        httpAuthSchemeProvider: config?.httpAuthSchemeProvider ?? defaultPollyHttpAuthSchemeProvider,\n        httpAuthSchemes: config?.httpAuthSchemes ?? [\n            {\n                schemeId: \"aws.auth#sigv4\",\n                identityProvider: (ipc) => ipc.getIdentityProvider(\"aws.auth#sigv4\"),\n                signer: new AwsSdkSigV4Signer(),\n            },\n        ],\n        logger: config?.logger ?? new NoOpLogger(),\n        sdkStreamMixin: config?.sdkStreamMixin ?? sdkStreamMixin,\n        serviceId: config?.serviceId ?? \"Polly\",\n        urlParser: config?.urlParser ?? parseUrl,\n        utf8Decoder: config?.utf8Decoder ?? fromUtf8,\n        utf8Encoder: config?.utf8Encoder ?? toUtf8,\n    };\n};\n", "import packageInfo from \"../package.json\";\nimport { Sha256 } from \"@aws-crypto/sha256-browser\";\nimport { createDefaultUserAgentProvider } from \"@aws-sdk/util-user-agent-browser\";\nimport { DEFAULT_USE_DUALSTACK_ENDPOINT, DEFAULT_USE_FIPS_ENDPOINT } from \"@smithy/config-resolver\";\nimport { FetchHttpHandler as RequestHandler, streamCollector } from \"@smithy/fetch-http-handler\";\nimport { invalidProvider } from \"@smithy/invalid-dependency\";\nimport { calculateBodyLength } from \"@smithy/util-body-length-browser\";\nimport { DEFAULT_MAX_ATTEMPTS, DEFAULT_RETRY_MODE } from \"@smithy/util-retry\";\nimport { getRuntimeConfig as getSharedRuntimeConfig } from \"./runtimeConfig.shared\";\nimport { loadConfigsForDefaultMode } from \"@smithy/smithy-client\";\nimport { resolveDefaultsModeConfig } from \"@smithy/util-defaults-mode-browser\";\nexport const getRuntimeConfig = (config) => {\n    const defaultsMode = resolveDefaultsModeConfig(config);\n    const defaultConfigProvider = () => defaultsMode().then(loadConfigsForDefaultMode);\n    const clientSharedValues = getSharedRuntimeConfig(config);\n    return {\n        ...clientSharedValues,\n        ...config,\n        runtime: \"browser\",\n        defaultsMode,\n        bodyLengthChecker: config?.bodyLengthChecker ?? calculateBodyLength,\n        credentialDefaultProvider: config?.credentialDefaultProvider ?? ((_) => () => Promise.reject(new Error(\"Credential is missing\"))),\n        defaultUserAgentProvider: config?.defaultUserAgentProvider ??\n            createDefaultUserAgentProvider({ serviceId: clientSharedValues.serviceId, clientVersion: packageInfo.version }),\n        maxAttempts: config?.maxAttempts ?? DEFAULT_MAX_ATTEMPTS,\n        region: config?.region ?? invalidProvider(\"Region is missing\"),\n        requestHandler: RequestHandler.create(config?.requestHandler ?? defaultConfigProvider),\n        retryMode: config?.retryMode ?? (async () => (await defaultConfigProvider()).retryMode || DEFAULT_RETRY_MODE),\n        sha256: config?.sha256 ?? Sha256,\n        streamCollector: config?.streamCollector ?? streamCollector,\n        useDualstackEndpoint: config?.useDualstackEndpoint ?? (() => Promise.resolve(DEFAULT_USE_DUALSTACK_ENDPOINT)),\n        useFipsEndpoint: config?.useFipsEndpoint ?? (() => Promise.resolve(DEFAULT_USE_FIPS_ENDPOINT)),\n    };\n};\n", "export const getHttpAuthExtensionConfiguration = (runtimeConfig) => {\n    const _httpAuthSchemes = runtimeConfig.httpAuthSchemes;\n    let _httpAuthSchemeProvider = runtimeConfig.httpAuthSchemeProvider;\n    let _credentials = runtimeConfig.credentials;\n    return {\n        setHttpAuthScheme(httpAuthScheme) {\n            const index = _httpAuthSchemes.findIndex((scheme) => scheme.schemeId === httpAuthScheme.schemeId);\n            if (index === -1) {\n                _httpAuthSchemes.push(httpAuthScheme);\n            }\n            else {\n                _httpAuthSchemes.splice(index, 1, httpAuthScheme);\n            }\n        },\n        httpAuthSchemes() {\n            return _httpAuthSchemes;\n        },\n        setHttpAuthSchemeProvider(httpAuthSchemeProvider) {\n            _httpAuthSchemeProvider = httpAuthSchemeProvider;\n        },\n        httpAuthSchemeProvider() {\n            return _httpAuthSchemeProvider;\n        },\n        setCredentials(credentials) {\n            _credentials = credentials;\n        },\n        credentials() {\n            return _credentials;\n        },\n    };\n};\nexport const resolveHttpAuthRuntimeConfig = (config) => {\n    return {\n        httpAuthSchemes: config.httpAuthSchemes(),\n        httpAuthSchemeProvider: config.httpAuthSchemeProvider(),\n        credentials: config.credentials(),\n    };\n};\n", "import { getAwsRegionExtensionConfiguration, resolveAwsRegionExtensionConfiguration, } from \"@aws-sdk/region-config-resolver\";\nimport { getHttpHandlerExtensionConfiguration, resolveHttpHandlerRuntimeConfig } from \"@smithy/protocol-http\";\nimport { getDefaultExtensionConfiguration, resolveDefaultRuntimeConfig } from \"@smithy/smithy-client\";\nimport { getHttpAuthExtensionConfiguration, resolveHttpAuthRuntimeConfig } from \"./auth/httpAuthExtensionConfiguration\";\nconst asPartial = (t) => t;\nexport const resolveRuntimeExtensions = (runtimeConfig, extensions) => {\n    const extensionConfiguration = {\n        ...asPartial(getAwsRegionExtensionConfiguration(runtimeConfig)),\n        ...asPartial(getDefaultExtensionConfiguration(runtimeConfig)),\n        ...asPartial(getHttpHandlerExtensionConfiguration(runtimeConfig)),\n        ...asPartial(getHttpAuthExtensionConfiguration(runtimeConfig)),\n    };\n    extensions.forEach((extension) => extension.configure(extensionConfiguration));\n    return {\n        ...runtimeConfig,\n        ...resolveAwsRegionExtensionConfiguration(extensionConfiguration),\n        ...resolveDefaultRuntimeConfig(extensionConfiguration),\n        ...resolveHttpHandlerRuntimeConfig(extensionConfiguration),\n        ...resolveHttpAuthRuntimeConfig(extensionConfiguration),\n    };\n};\n", "import { getHostHeaderPlugin, resolveHostHeaderConfig, } from \"@aws-sdk/middleware-host-header\";\nimport { getLoggerPlugin } from \"@aws-sdk/middleware-logger\";\nimport { getRecursionDetectionPlugin } from \"@aws-sdk/middleware-recursion-detection\";\nimport { getUserAgentPlugin, resolveUserAgentConfig, } from \"@aws-sdk/middleware-user-agent\";\nimport { resolveRegionConfig } from \"@smithy/config-resolver\";\nimport { DefaultIdentityProviderConfig, getHttpAuthSchemeEndpointRuleSetPlugin, getHttpSigningPlugin, } from \"@smithy/core\";\nimport { getContentLengthPlugin } from \"@smithy/middleware-content-length\";\nimport { resolveEndpointConfig } from \"@smithy/middleware-endpoint\";\nimport { getRetryPlugin, resolveRetryConfig } from \"@smithy/middleware-retry\";\nimport { Client as __Client, } from \"@smithy/smithy-client\";\nimport { defaultPollyHttpAuthSchemeParametersProvider, resolveHttpAuthSchemeConfig, } from \"./auth/httpAuthSchemeProvider\";\nimport { resolveClientEndpointParameters, } from \"./endpoint/EndpointParameters\";\nimport { getRuntimeConfig as __getRuntimeConfig } from \"./runtimeConfig\";\nimport { resolveRuntimeExtensions } from \"./runtimeExtensions\";\nexport { __Client };\nexport class PollyClient extends __Client {\n    constructor(...[configuration]) {\n        const _config_0 = __getRuntimeConfig(configuration || {});\n        const _config_1 = resolveClientEndpointParameters(_config_0);\n        const _config_2 = resolveUserAgentConfig(_config_1);\n        const _config_3 = resolveRetryConfig(_config_2);\n        const _config_4 = resolveRegionConfig(_config_3);\n        const _config_5 = resolveHostHeaderConfig(_config_4);\n        const _config_6 = resolveEndpointConfig(_config_5);\n        const _config_7 = resolveHttpAuthSchemeConfig(_config_6);\n        const _config_8 = resolveRuntimeExtensions(_config_7, configuration?.extensions || []);\n        super(_config_8);\n        this.config = _config_8;\n        this.middlewareStack.use(getUserAgentPlugin(this.config));\n        this.middlewareStack.use(getRetryPlugin(this.config));\n        this.middlewareStack.use(getContentLengthPlugin(this.config));\n        this.middlewareStack.use(getHostHeaderPlugin(this.config));\n        this.middlewareStack.use(getLoggerPlugin(this.config));\n        this.middlewareStack.use(getRecursionDetectionPlugin(this.config));\n        this.middlewareStack.use(getHttpAuthSchemeEndpointRuleSetPlugin(this.config, {\n            httpAuthSchemeParametersProvider: defaultPollyHttpAuthSchemeParametersProvider,\n            identityProviderConfigProvider: async (config) => new DefaultIdentityProviderConfig({\n                \"aws.auth#sigv4\": config.credentials,\n            }),\n        }));\n        this.middlewareStack.use(getHttpSigningPlugin(this.config));\n    }\n    destroy() {\n        super.destroy();\n    }\n}\n", "import { ServiceException as __ServiceException, } from \"@smithy/smithy-client\";\nexport { __ServiceException };\nexport class PollyServiceException extends __ServiceException {\n    constructor(options) {\n        super(options);\n        Object.setPrototypeOf(this, PollyServiceException.prototype);\n    }\n}\n", "import { SENSITIVE_STRING } from \"@smithy/smithy-client\";\nimport { PollyServiceException as __BaseException } from \"./PollyServiceException\";\nexport class LexiconNotFoundException extends __BaseException {\n    constructor(opts) {\n        super({\n            name: \"LexiconNotFoundException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        this.name = \"LexiconNotFoundException\";\n        this.$fault = \"client\";\n        Object.setPrototypeOf(this, LexiconNotFoundException.prototype);\n    }\n}\nexport class ServiceFailureException extends __BaseException {\n    constructor(opts) {\n        super({\n            name: \"ServiceFailureException\",\n            $fault: \"server\",\n            ...opts,\n        });\n        this.name = \"ServiceFailureException\";\n        this.$fault = \"server\";\n        Object.setPrototypeOf(this, ServiceFailureException.prototype);\n    }\n}\nexport const Engine = {\n    GENERATIVE: \"generative\",\n    LONG_FORM: \"long-form\",\n    NEURAL: \"neural\",\n    STANDARD: \"standard\",\n};\nexport const LanguageCode = {\n    ar_AE: \"ar-AE\",\n    arb: \"arb\",\n    ca_ES: \"ca-ES\",\n    cmn_CN: \"cmn-CN\",\n    cs_CZ: \"cs-CZ\",\n    cy_GB: \"cy-GB\",\n    da_DK: \"da-DK\",\n    de_AT: \"de-AT\",\n    de_CH: \"de-CH\",\n    de_DE: \"de-DE\",\n    en_AU: \"en-AU\",\n    en_GB: \"en-GB\",\n    en_GB_WLS: \"en-GB-WLS\",\n    en_IE: \"en-IE\",\n    en_IN: \"en-IN\",\n    en_NZ: \"en-NZ\",\n    en_US: \"en-US\",\n    en_ZA: \"en-ZA\",\n    es_ES: \"es-ES\",\n    es_MX: \"es-MX\",\n    es_US: \"es-US\",\n    fi_FI: \"fi-FI\",\n    fr_BE: \"fr-BE\",\n    fr_CA: \"fr-CA\",\n    fr_FR: \"fr-FR\",\n    hi_IN: \"hi-IN\",\n    is_IS: \"is-IS\",\n    it_IT: \"it-IT\",\n    ja_JP: \"ja-JP\",\n    ko_KR: \"ko-KR\",\n    nb_NO: \"nb-NO\",\n    nl_BE: \"nl-BE\",\n    nl_NL: \"nl-NL\",\n    pl_PL: \"pl-PL\",\n    pt_BR: \"pt-BR\",\n    pt_PT: \"pt-PT\",\n    ro_RO: \"ro-RO\",\n    ru_RU: \"ru-RU\",\n    sv_SE: \"sv-SE\",\n    tr_TR: \"tr-TR\",\n    yue_CN: \"yue-CN\",\n};\nexport const Gender = {\n    Female: \"Female\",\n    Male: \"Male\",\n};\nexport const VoiceId = {\n    Aditi: \"Aditi\",\n    Adriano: \"Adriano\",\n    Amy: \"Amy\",\n    Andres: \"Andres\",\n    Aria: \"Aria\",\n    Arlet: \"Arlet\",\n    Arthur: \"Arthur\",\n    Astrid: \"Astrid\",\n    Ayanda: \"Ayanda\",\n    Bianca: \"Bianca\",\n    Brian: \"Brian\",\n    Burcu: \"Burcu\",\n    Camila: \"Camila\",\n    Carla: \"Carla\",\n    Carmen: \"Carmen\",\n    Celine: \"Celine\",\n    Chantal: \"Chantal\",\n    Conchita: \"Conchita\",\n    Cristiano: \"Cristiano\",\n    Daniel: \"Daniel\",\n    Danielle: \"Danielle\",\n    Dora: \"Dora\",\n    Elin: \"Elin\",\n    Emma: \"Emma\",\n    Enrique: \"Enrique\",\n    Ewa: \"Ewa\",\n    Filiz: \"Filiz\",\n    Gabrielle: \"Gabrielle\",\n    Geraint: \"Geraint\",\n    Giorgio: \"Giorgio\",\n    Gregory: \"Gregory\",\n    Gwyneth: \"Gwyneth\",\n    Hala: \"Hala\",\n    Hannah: \"Hannah\",\n    Hans: \"Hans\",\n    Hiujin: \"Hiujin\",\n    Ida: \"Ida\",\n    Ines: \"Ines\",\n    Isabelle: \"Isabelle\",\n    Ivy: \"Ivy\",\n    Jacek: \"Jacek\",\n    Jan: \"Jan\",\n    Jitka: \"Jitka\",\n    Joanna: \"Joanna\",\n    Joey: \"Joey\",\n    Justin: \"Justin\",\n    Kajal: \"Kajal\",\n    Karl: \"Karl\",\n    Kazuha: \"Kazuha\",\n    Kendra: \"Kendra\",\n    Kevin: \"Kevin\",\n    Kimberly: \"Kimberly\",\n    Laura: \"Laura\",\n    Lea: \"Lea\",\n    Liam: \"Liam\",\n    Lisa: \"Lisa\",\n    Liv: \"Liv\",\n    Lotte: \"Lotte\",\n    Lucia: \"Lucia\",\n    Lupe: \"Lupe\",\n    Mads: \"Mads\",\n    Maja: \"Maja\",\n    Marlene: \"Marlene\",\n    Mathieu: \"Mathieu\",\n    Matthew: \"Matthew\",\n    Maxim: \"Maxim\",\n    Mia: \"Mia\",\n    Miguel: \"Miguel\",\n    Mizuki: \"Mizuki\",\n    Naja: \"Naja\",\n    Niamh: \"Niamh\",\n    Nicole: \"Nicole\",\n    Ola: \"Ola\",\n    Olivia: \"Olivia\",\n    Pedro: \"Pedro\",\n    Penelope: \"Penelope\",\n    Raveena: \"Raveena\",\n    Remi: \"Remi\",\n    Ricardo: \"Ricardo\",\n    Ruben: \"Ruben\",\n    Russell: \"Russell\",\n    Ruth: \"Ruth\",\n    Sabrina: \"Sabrina\",\n    Salli: \"Salli\",\n    Seoyeon: \"Seoyeon\",\n    Sergio: \"Sergio\",\n    Sofie: \"Sofie\",\n    Stephen: \"Stephen\",\n    Suvi: \"Suvi\",\n    Takumi: \"Takumi\",\n    Tatyana: \"Tatyana\",\n    Thiago: \"Thiago\",\n    Tomoko: \"Tomoko\",\n    Vicki: \"Vicki\",\n    Vitoria: \"Vitoria\",\n    Zayd: \"Zayd\",\n    Zeina: \"Zeina\",\n    Zhiyu: \"Zhiyu\",\n};\nexport class InvalidNextTokenException extends __BaseException {\n    constructor(opts) {\n        super({\n            name: \"InvalidNextTokenException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        this.name = \"InvalidNextTokenException\";\n        this.$fault = \"client\";\n        Object.setPrototypeOf(this, InvalidNextTokenException.prototype);\n    }\n}\nexport class EngineNotSupportedException extends __BaseException {\n    constructor(opts) {\n        super({\n            name: \"EngineNotSupportedException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        this.name = \"EngineNotSupportedException\";\n        this.$fault = \"client\";\n        Object.setPrototypeOf(this, EngineNotSupportedException.prototype);\n    }\n}\nexport const OutputFormat = {\n    JSON: \"json\",\n    MP3: \"mp3\",\n    OGG_VORBIS: \"ogg_vorbis\",\n    PCM: \"pcm\",\n};\nexport const SpeechMarkType = {\n    SENTENCE: \"sentence\",\n    SSML: \"ssml\",\n    VISEME: \"viseme\",\n    WORD: \"word\",\n};\nexport const TaskStatus = {\n    COMPLETED: \"completed\",\n    FAILED: \"failed\",\n    IN_PROGRESS: \"inProgress\",\n    SCHEDULED: \"scheduled\",\n};\nexport const TextType = {\n    SSML: \"ssml\",\n    TEXT: \"text\",\n};\nexport class InvalidTaskIdException extends __BaseException {\n    constructor(opts) {\n        super({\n            name: \"InvalidTaskIdException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        this.name = \"InvalidTaskIdException\";\n        this.$fault = \"client\";\n        Object.setPrototypeOf(this, InvalidTaskIdException.prototype);\n    }\n}\nexport class SynthesisTaskNotFoundException extends __BaseException {\n    constructor(opts) {\n        super({\n            name: \"SynthesisTaskNotFoundException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        this.name = \"SynthesisTaskNotFoundException\";\n        this.$fault = \"client\";\n        Object.setPrototypeOf(this, SynthesisTaskNotFoundException.prototype);\n    }\n}\nexport class InvalidLexiconException extends __BaseException {\n    constructor(opts) {\n        super({\n            name: \"InvalidLexiconException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        this.name = \"InvalidLexiconException\";\n        this.$fault = \"client\";\n        Object.setPrototypeOf(this, InvalidLexiconException.prototype);\n    }\n}\nexport class InvalidS3BucketException extends __BaseException {\n    constructor(opts) {\n        super({\n            name: \"InvalidS3BucketException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        this.name = \"InvalidS3BucketException\";\n        this.$fault = \"client\";\n        Object.setPrototypeOf(this, InvalidS3BucketException.prototype);\n    }\n}\nexport class InvalidS3KeyException extends __BaseException {\n    constructor(opts) {\n        super({\n            name: \"InvalidS3KeyException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        this.name = \"InvalidS3KeyException\";\n        this.$fault = \"client\";\n        Object.setPrototypeOf(this, InvalidS3KeyException.prototype);\n    }\n}\nexport class InvalidSampleRateException extends __BaseException {\n    constructor(opts) {\n        super({\n            name: \"InvalidSampleRateException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        this.name = \"InvalidSampleRateException\";\n        this.$fault = \"client\";\n        Object.setPrototypeOf(this, InvalidSampleRateException.prototype);\n    }\n}\nexport class InvalidSnsTopicArnException extends __BaseException {\n    constructor(opts) {\n        super({\n            name: \"InvalidSnsTopicArnException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        this.name = \"InvalidSnsTopicArnException\";\n        this.$fault = \"client\";\n        Object.setPrototypeOf(this, InvalidSnsTopicArnException.prototype);\n    }\n}\nexport class InvalidSsmlException extends __BaseException {\n    constructor(opts) {\n        super({\n            name: \"InvalidSsmlException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        this.name = \"InvalidSsmlException\";\n        this.$fault = \"client\";\n        Object.setPrototypeOf(this, InvalidSsmlException.prototype);\n    }\n}\nexport class LanguageNotSupportedException extends __BaseException {\n    constructor(opts) {\n        super({\n            name: \"LanguageNotSupportedException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        this.name = \"LanguageNotSupportedException\";\n        this.$fault = \"client\";\n        Object.setPrototypeOf(this, LanguageNotSupportedException.prototype);\n    }\n}\nexport class LexiconSizeExceededException extends __BaseException {\n    constructor(opts) {\n        super({\n            name: \"LexiconSizeExceededException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        this.name = \"LexiconSizeExceededException\";\n        this.$fault = \"client\";\n        Object.setPrototypeOf(this, LexiconSizeExceededException.prototype);\n    }\n}\nexport class MarksNotSupportedForFormatException extends __BaseException {\n    constructor(opts) {\n        super({\n            name: \"MarksNotSupportedForFormatException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        this.name = \"MarksNotSupportedForFormatException\";\n        this.$fault = \"client\";\n        Object.setPrototypeOf(this, MarksNotSupportedForFormatException.prototype);\n    }\n}\nexport class MaxLexemeLengthExceededException extends __BaseException {\n    constructor(opts) {\n        super({\n            name: \"MaxLexemeLengthExceededException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        this.name = \"MaxLexemeLengthExceededException\";\n        this.$fault = \"client\";\n        Object.setPrototypeOf(this, MaxLexemeLengthExceededException.prototype);\n    }\n}\nexport class MaxLexiconsNumberExceededException extends __BaseException {\n    constructor(opts) {\n        super({\n            name: \"MaxLexiconsNumberExceededException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        this.name = \"MaxLexiconsNumberExceededException\";\n        this.$fault = \"client\";\n        Object.setPrototypeOf(this, MaxLexiconsNumberExceededException.prototype);\n    }\n}\nexport class UnsupportedPlsAlphabetException extends __BaseException {\n    constructor(opts) {\n        super({\n            name: \"UnsupportedPlsAlphabetException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        this.name = \"UnsupportedPlsAlphabetException\";\n        this.$fault = \"client\";\n        Object.setPrototypeOf(this, UnsupportedPlsAlphabetException.prototype);\n    }\n}\nexport class UnsupportedPlsLanguageException extends __BaseException {\n    constructor(opts) {\n        super({\n            name: \"UnsupportedPlsLanguageException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        this.name = \"UnsupportedPlsLanguageException\";\n        this.$fault = \"client\";\n        Object.setPrototypeOf(this, UnsupportedPlsLanguageException.prototype);\n    }\n}\nexport class SsmlMarksNotSupportedForTextTypeException extends __BaseException {\n    constructor(opts) {\n        super({\n            name: \"SsmlMarksNotSupportedForTextTypeException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        this.name = \"SsmlMarksNotSupportedForTextTypeException\";\n        this.$fault = \"client\";\n        Object.setPrototypeOf(this, SsmlMarksNotSupportedForTextTypeException.prototype);\n    }\n}\nexport class TextLengthExceededException extends __BaseException {\n    constructor(opts) {\n        super({\n            name: \"TextLengthExceededException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        this.name = \"TextLengthExceededException\";\n        this.$fault = \"client\";\n        Object.setPrototypeOf(this, TextLengthExceededException.prototype);\n    }\n}\nexport const LexiconFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.Content && { Content: SENSITIVE_STRING }),\n});\nexport const GetLexiconOutputFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.Lexicon && { Lexicon: LexiconFilterSensitiveLog(obj.Lexicon) }),\n});\nexport const PutLexiconInputFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.Content && { Content: SENSITIVE_STRING }),\n});\nexport const SynthesizeSpeechOutputFilterSensitiveLog = (obj) => ({\n    ...obj,\n});\n", "import { loadRestJsonErrorCode, parseJsonBody as parseBody, parseJsonErrorBody as parseErrorBody } from \"@aws-sdk/core\";\nimport { requestBuilder as rb } from \"@smithy/core\";\nimport { _json, collectBody, decorateServiceException as __decorateServiceException, expectInt32 as __expectInt32, expectNonNull as __expectNonNull, expectNumber as __expectNumber, expectObject as __expectObject, expectString as __expectString, map, parseEpochTimestamp as __parseEpochTimestamp, strictParseInt32 as __strictParseInt32, take, withBaseException, } from \"@smithy/smithy-client\";\nimport { EngineNotSupportedException, InvalidLexiconException, InvalidNextTokenException, InvalidS3BucketException, InvalidS3KeyException, InvalidSampleRateException, InvalidSnsTopicArnException, InvalidSsmlException, InvalidTaskIdException, LanguageNotSupportedException, LexiconNotFoundException, LexiconSizeExceededException, MarksNotSupportedForFormatException, MaxLexemeLengthExceededException, MaxLexiconsNumberExceededException, ServiceFailureException, SsmlMarksNotSupportedForTextTypeException, SynthesisTaskNotFoundException, TextLengthExceededException, UnsupportedPlsAlphabetException, UnsupportedPlsLanguageException, } from \"../models/models_0\";\nimport { PollyServiceException as __BaseException } from \"../models/PollyServiceException\";\nexport const se_DeleteLexiconCommand = async (input, context) => {\n    const b = rb(input, context);\n    const headers = {};\n    b.bp(\"/v1/lexicons/{Name}\");\n    b.p(\"Name\", () => input.Name, \"{Name}\", false);\n    let body;\n    b.m(\"DELETE\").h(headers).b(body);\n    return b.build();\n};\nexport const se_DescribeVoicesCommand = async (input, context) => {\n    const b = rb(input, context);\n    const headers = {};\n    b.bp(\"/v1/voices\");\n    const query = map({\n        [_E]: [, input[_E]],\n        [_LC]: [, input[_LC]],\n        [_IALC]: [() => input.IncludeAdditionalLanguageCodes !== void 0, () => input[_IALC].toString()],\n        [_NT]: [, input[_NT]],\n    });\n    let body;\n    b.m(\"GET\").h(headers).q(query).b(body);\n    return b.build();\n};\nexport const se_GetLexiconCommand = async (input, context) => {\n    const b = rb(input, context);\n    const headers = {};\n    b.bp(\"/v1/lexicons/{Name}\");\n    b.p(\"Name\", () => input.Name, \"{Name}\", false);\n    let body;\n    b.m(\"GET\").h(headers).b(body);\n    return b.build();\n};\nexport const se_GetSpeechSynthesisTaskCommand = async (input, context) => {\n    const b = rb(input, context);\n    const headers = {};\n    b.bp(\"/v1/synthesisTasks/{TaskId}\");\n    b.p(\"TaskId\", () => input.TaskId, \"{TaskId}\", false);\n    let body;\n    b.m(\"GET\").h(headers).b(body);\n    return b.build();\n};\nexport const se_ListLexiconsCommand = async (input, context) => {\n    const b = rb(input, context);\n    const headers = {};\n    b.bp(\"/v1/lexicons\");\n    const query = map({\n        [_NT]: [, input[_NT]],\n    });\n    let body;\n    b.m(\"GET\").h(headers).q(query).b(body);\n    return b.build();\n};\nexport const se_ListSpeechSynthesisTasksCommand = async (input, context) => {\n    const b = rb(input, context);\n    const headers = {};\n    b.bp(\"/v1/synthesisTasks\");\n    const query = map({\n        [_MR]: [() => input.MaxResults !== void 0, () => input[_MR].toString()],\n        [_NT]: [, input[_NT]],\n        [_S]: [, input[_S]],\n    });\n    let body;\n    b.m(\"GET\").h(headers).q(query).b(body);\n    return b.build();\n};\nexport const se_PutLexiconCommand = async (input, context) => {\n    const b = rb(input, context);\n    const headers = {\n        \"content-type\": \"application/json\",\n    };\n    b.bp(\"/v1/lexicons/{Name}\");\n    b.p(\"Name\", () => input.Name, \"{Name}\", false);\n    let body;\n    body = JSON.stringify(take(input, {\n        Content: [],\n    }));\n    b.m(\"PUT\").h(headers).b(body);\n    return b.build();\n};\nexport const se_StartSpeechSynthesisTaskCommand = async (input, context) => {\n    const b = rb(input, context);\n    const headers = {\n        \"content-type\": \"application/json\",\n    };\n    b.bp(\"/v1/synthesisTasks\");\n    let body;\n    body = JSON.stringify(take(input, {\n        Engine: [],\n        LanguageCode: [],\n        LexiconNames: (_) => _json(_),\n        OutputFormat: [],\n        OutputS3BucketName: [],\n        OutputS3KeyPrefix: [],\n        SampleRate: [],\n        SnsTopicArn: [],\n        SpeechMarkTypes: (_) => _json(_),\n        Text: [],\n        TextType: [],\n        VoiceId: [],\n    }));\n    b.m(\"POST\").h(headers).b(body);\n    return b.build();\n};\nexport const se_SynthesizeSpeechCommand = async (input, context) => {\n    const b = rb(input, context);\n    const headers = {\n        \"content-type\": \"application/json\",\n    };\n    b.bp(\"/v1/speech\");\n    let body;\n    body = JSON.stringify(take(input, {\n        Engine: [],\n        LanguageCode: [],\n        LexiconNames: (_) => _json(_),\n        OutputFormat: [],\n        SampleRate: [],\n        SpeechMarkTypes: (_) => _json(_),\n        Text: [],\n        TextType: [],\n        VoiceId: [],\n    }));\n    b.m(\"POST\").h(headers).b(body);\n    return b.build();\n};\nexport const de_DeleteLexiconCommand = async (output, context) => {\n    if (output.statusCode !== 200 && output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const contents = map({\n        $metadata: deserializeMetadata(output),\n    });\n    await collectBody(output.body, context);\n    return contents;\n};\nexport const de_DescribeVoicesCommand = async (output, context) => {\n    if (output.statusCode !== 200 && output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const contents = map({\n        $metadata: deserializeMetadata(output),\n    });\n    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), \"body\");\n    const doc = take(data, {\n        NextToken: __expectString,\n        Voices: _json,\n    });\n    Object.assign(contents, doc);\n    return contents;\n};\nexport const de_GetLexiconCommand = async (output, context) => {\n    if (output.statusCode !== 200 && output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const contents = map({\n        $metadata: deserializeMetadata(output),\n    });\n    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), \"body\");\n    const doc = take(data, {\n        Lexicon: _json,\n        LexiconAttributes: (_) => de_LexiconAttributes(_, context),\n    });\n    Object.assign(contents, doc);\n    return contents;\n};\nexport const de_GetSpeechSynthesisTaskCommand = async (output, context) => {\n    if (output.statusCode !== 200 && output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const contents = map({\n        $metadata: deserializeMetadata(output),\n    });\n    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), \"body\");\n    const doc = take(data, {\n        SynthesisTask: (_) => de_SynthesisTask(_, context),\n    });\n    Object.assign(contents, doc);\n    return contents;\n};\nexport const de_ListLexiconsCommand = async (output, context) => {\n    if (output.statusCode !== 200 && output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const contents = map({\n        $metadata: deserializeMetadata(output),\n    });\n    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), \"body\");\n    const doc = take(data, {\n        Lexicons: (_) => de_LexiconDescriptionList(_, context),\n        NextToken: __expectString,\n    });\n    Object.assign(contents, doc);\n    return contents;\n};\nexport const de_ListSpeechSynthesisTasksCommand = async (output, context) => {\n    if (output.statusCode !== 200 && output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const contents = map({\n        $metadata: deserializeMetadata(output),\n    });\n    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), \"body\");\n    const doc = take(data, {\n        NextToken: __expectString,\n        SynthesisTasks: (_) => de_SynthesisTasks(_, context),\n    });\n    Object.assign(contents, doc);\n    return contents;\n};\nexport const de_PutLexiconCommand = async (output, context) => {\n    if (output.statusCode !== 200 && output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const contents = map({\n        $metadata: deserializeMetadata(output),\n    });\n    await collectBody(output.body, context);\n    return contents;\n};\nexport const de_StartSpeechSynthesisTaskCommand = async (output, context) => {\n    if (output.statusCode !== 200 && output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const contents = map({\n        $metadata: deserializeMetadata(output),\n    });\n    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), \"body\");\n    const doc = take(data, {\n        SynthesisTask: (_) => de_SynthesisTask(_, context),\n    });\n    Object.assign(contents, doc);\n    return contents;\n};\nexport const de_SynthesizeSpeechCommand = async (output, context) => {\n    if (output.statusCode !== 200 && output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const contents = map({\n        $metadata: deserializeMetadata(output),\n        [_CT]: [, output.headers[_ct]],\n        [_RC]: [() => void 0 !== output.headers[_xar], () => __strictParseInt32(output.headers[_xar])],\n    });\n    const data = output.body;\n    context.sdkStreamMixin(data);\n    contents.AudioStream = data;\n    return contents;\n};\nconst de_CommandError = async (output, context) => {\n    const parsedOutput = {\n        ...output,\n        body: await parseErrorBody(output.body, context),\n    };\n    const errorCode = loadRestJsonErrorCode(output, parsedOutput.body);\n    switch (errorCode) {\n        case \"LexiconNotFoundException\":\n        case \"com.amazonaws.polly#LexiconNotFoundException\":\n            throw await de_LexiconNotFoundExceptionRes(parsedOutput, context);\n        case \"ServiceFailureException\":\n        case \"com.amazonaws.polly#ServiceFailureException\":\n            throw await de_ServiceFailureExceptionRes(parsedOutput, context);\n        case \"InvalidNextTokenException\":\n        case \"com.amazonaws.polly#InvalidNextTokenException\":\n            throw await de_InvalidNextTokenExceptionRes(parsedOutput, context);\n        case \"InvalidTaskIdException\":\n        case \"com.amazonaws.polly#InvalidTaskIdException\":\n            throw await de_InvalidTaskIdExceptionRes(parsedOutput, context);\n        case \"SynthesisTaskNotFoundException\":\n        case \"com.amazonaws.polly#SynthesisTaskNotFoundException\":\n            throw await de_SynthesisTaskNotFoundExceptionRes(parsedOutput, context);\n        case \"InvalidLexiconException\":\n        case \"com.amazonaws.polly#InvalidLexiconException\":\n            throw await de_InvalidLexiconExceptionRes(parsedOutput, context);\n        case \"LexiconSizeExceededException\":\n        case \"com.amazonaws.polly#LexiconSizeExceededException\":\n            throw await de_LexiconSizeExceededExceptionRes(parsedOutput, context);\n        case \"MaxLexemeLengthExceededException\":\n        case \"com.amazonaws.polly#MaxLexemeLengthExceededException\":\n            throw await de_MaxLexemeLengthExceededExceptionRes(parsedOutput, context);\n        case \"MaxLexiconsNumberExceededException\":\n        case \"com.amazonaws.polly#MaxLexiconsNumberExceededException\":\n            throw await de_MaxLexiconsNumberExceededExceptionRes(parsedOutput, context);\n        case \"UnsupportedPlsAlphabetException\":\n        case \"com.amazonaws.polly#UnsupportedPlsAlphabetException\":\n            throw await de_UnsupportedPlsAlphabetExceptionRes(parsedOutput, context);\n        case \"UnsupportedPlsLanguageException\":\n        case \"com.amazonaws.polly#UnsupportedPlsLanguageException\":\n            throw await de_UnsupportedPlsLanguageExceptionRes(parsedOutput, context);\n        case \"EngineNotSupportedException\":\n        case \"com.amazonaws.polly#EngineNotSupportedException\":\n            throw await de_EngineNotSupportedExceptionRes(parsedOutput, context);\n        case \"InvalidS3BucketException\":\n        case \"com.amazonaws.polly#InvalidS3BucketException\":\n            throw await de_InvalidS3BucketExceptionRes(parsedOutput, context);\n        case \"InvalidS3KeyException\":\n        case \"com.amazonaws.polly#InvalidS3KeyException\":\n            throw await de_InvalidS3KeyExceptionRes(parsedOutput, context);\n        case \"InvalidSampleRateException\":\n        case \"com.amazonaws.polly#InvalidSampleRateException\":\n            throw await de_InvalidSampleRateExceptionRes(parsedOutput, context);\n        case \"InvalidSnsTopicArnException\":\n        case \"com.amazonaws.polly#InvalidSnsTopicArnException\":\n            throw await de_InvalidSnsTopicArnExceptionRes(parsedOutput, context);\n        case \"InvalidSsmlException\":\n        case \"com.amazonaws.polly#InvalidSsmlException\":\n            throw await de_InvalidSsmlExceptionRes(parsedOutput, context);\n        case \"LanguageNotSupportedException\":\n        case \"com.amazonaws.polly#LanguageNotSupportedException\":\n            throw await de_LanguageNotSupportedExceptionRes(parsedOutput, context);\n        case \"MarksNotSupportedForFormatException\":\n        case \"com.amazonaws.polly#MarksNotSupportedForFormatException\":\n            throw await de_MarksNotSupportedForFormatExceptionRes(parsedOutput, context);\n        case \"SsmlMarksNotSupportedForTextTypeException\":\n        case \"com.amazonaws.polly#SsmlMarksNotSupportedForTextTypeException\":\n            throw await de_SsmlMarksNotSupportedForTextTypeExceptionRes(parsedOutput, context);\n        case \"TextLengthExceededException\":\n        case \"com.amazonaws.polly#TextLengthExceededException\":\n            throw await de_TextLengthExceededExceptionRes(parsedOutput, context);\n        default:\n            const parsedBody = parsedOutput.body;\n            return throwDefaultError({\n                output,\n                parsedBody,\n                errorCode,\n            });\n    }\n};\nconst throwDefaultError = withBaseException(__BaseException);\nconst de_EngineNotSupportedExceptionRes = async (parsedOutput, context) => {\n    const contents = map({});\n    const data = parsedOutput.body;\n    const doc = take(data, {\n        message: __expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new EngineNotSupportedException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return __decorateServiceException(exception, parsedOutput.body);\n};\nconst de_InvalidLexiconExceptionRes = async (parsedOutput, context) => {\n    const contents = map({});\n    const data = parsedOutput.body;\n    const doc = take(data, {\n        message: __expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new InvalidLexiconException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return __decorateServiceException(exception, parsedOutput.body);\n};\nconst de_InvalidNextTokenExceptionRes = async (parsedOutput, context) => {\n    const contents = map({});\n    const data = parsedOutput.body;\n    const doc = take(data, {\n        message: __expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new InvalidNextTokenException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return __decorateServiceException(exception, parsedOutput.body);\n};\nconst de_InvalidS3BucketExceptionRes = async (parsedOutput, context) => {\n    const contents = map({});\n    const data = parsedOutput.body;\n    const doc = take(data, {\n        message: __expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new InvalidS3BucketException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return __decorateServiceException(exception, parsedOutput.body);\n};\nconst de_InvalidS3KeyExceptionRes = async (parsedOutput, context) => {\n    const contents = map({});\n    const data = parsedOutput.body;\n    const doc = take(data, {\n        message: __expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new InvalidS3KeyException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return __decorateServiceException(exception, parsedOutput.body);\n};\nconst de_InvalidSampleRateExceptionRes = async (parsedOutput, context) => {\n    const contents = map({});\n    const data = parsedOutput.body;\n    const doc = take(data, {\n        message: __expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new InvalidSampleRateException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return __decorateServiceException(exception, parsedOutput.body);\n};\nconst de_InvalidSnsTopicArnExceptionRes = async (parsedOutput, context) => {\n    const contents = map({});\n    const data = parsedOutput.body;\n    const doc = take(data, {\n        message: __expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new InvalidSnsTopicArnException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return __decorateServiceException(exception, parsedOutput.body);\n};\nconst de_InvalidSsmlExceptionRes = async (parsedOutput, context) => {\n    const contents = map({});\n    const data = parsedOutput.body;\n    const doc = take(data, {\n        message: __expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new InvalidSsmlException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return __decorateServiceException(exception, parsedOutput.body);\n};\nconst de_InvalidTaskIdExceptionRes = async (parsedOutput, context) => {\n    const contents = map({});\n    const data = parsedOutput.body;\n    const doc = take(data, {\n        message: __expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new InvalidTaskIdException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return __decorateServiceException(exception, parsedOutput.body);\n};\nconst de_LanguageNotSupportedExceptionRes = async (parsedOutput, context) => {\n    const contents = map({});\n    const data = parsedOutput.body;\n    const doc = take(data, {\n        message: __expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new LanguageNotSupportedException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return __decorateServiceException(exception, parsedOutput.body);\n};\nconst de_LexiconNotFoundExceptionRes = async (parsedOutput, context) => {\n    const contents = map({});\n    const data = parsedOutput.body;\n    const doc = take(data, {\n        message: __expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new LexiconNotFoundException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return __decorateServiceException(exception, parsedOutput.body);\n};\nconst de_LexiconSizeExceededExceptionRes = async (parsedOutput, context) => {\n    const contents = map({});\n    const data = parsedOutput.body;\n    const doc = take(data, {\n        message: __expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new LexiconSizeExceededException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return __decorateServiceException(exception, parsedOutput.body);\n};\nconst de_MarksNotSupportedForFormatExceptionRes = async (parsedOutput, context) => {\n    const contents = map({});\n    const data = parsedOutput.body;\n    const doc = take(data, {\n        message: __expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new MarksNotSupportedForFormatException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return __decorateServiceException(exception, parsedOutput.body);\n};\nconst de_MaxLexemeLengthExceededExceptionRes = async (parsedOutput, context) => {\n    const contents = map({});\n    const data = parsedOutput.body;\n    const doc = take(data, {\n        message: __expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new MaxLexemeLengthExceededException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return __decorateServiceException(exception, parsedOutput.body);\n};\nconst de_MaxLexiconsNumberExceededExceptionRes = async (parsedOutput, context) => {\n    const contents = map({});\n    const data = parsedOutput.body;\n    const doc = take(data, {\n        message: __expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new MaxLexiconsNumberExceededException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return __decorateServiceException(exception, parsedOutput.body);\n};\nconst de_ServiceFailureExceptionRes = async (parsedOutput, context) => {\n    const contents = map({});\n    const data = parsedOutput.body;\n    const doc = take(data, {\n        message: __expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new ServiceFailureException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return __decorateServiceException(exception, parsedOutput.body);\n};\nconst de_SsmlMarksNotSupportedForTextTypeExceptionRes = async (parsedOutput, context) => {\n    const contents = map({});\n    const data = parsedOutput.body;\n    const doc = take(data, {\n        message: __expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new SsmlMarksNotSupportedForTextTypeException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return __decorateServiceException(exception, parsedOutput.body);\n};\nconst de_SynthesisTaskNotFoundExceptionRes = async (parsedOutput, context) => {\n    const contents = map({});\n    const data = parsedOutput.body;\n    const doc = take(data, {\n        message: __expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new SynthesisTaskNotFoundException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return __decorateServiceException(exception, parsedOutput.body);\n};\nconst de_TextLengthExceededExceptionRes = async (parsedOutput, context) => {\n    const contents = map({});\n    const data = parsedOutput.body;\n    const doc = take(data, {\n        message: __expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new TextLengthExceededException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return __decorateServiceException(exception, parsedOutput.body);\n};\nconst de_UnsupportedPlsAlphabetExceptionRes = async (parsedOutput, context) => {\n    const contents = map({});\n    const data = parsedOutput.body;\n    const doc = take(data, {\n        message: __expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new UnsupportedPlsAlphabetException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return __decorateServiceException(exception, parsedOutput.body);\n};\nconst de_UnsupportedPlsLanguageExceptionRes = async (parsedOutput, context) => {\n    const contents = map({});\n    const data = parsedOutput.body;\n    const doc = take(data, {\n        message: __expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new UnsupportedPlsLanguageException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return __decorateServiceException(exception, parsedOutput.body);\n};\nconst de_LexiconAttributes = (output, context) => {\n    return take(output, {\n        Alphabet: __expectString,\n        LanguageCode: __expectString,\n        LastModified: (_) => __expectNonNull(__parseEpochTimestamp(__expectNumber(_))),\n        LexemesCount: __expectInt32,\n        LexiconArn: __expectString,\n        Size: __expectInt32,\n    });\n};\nconst de_LexiconDescription = (output, context) => {\n    return take(output, {\n        Attributes: (_) => de_LexiconAttributes(_, context),\n        Name: __expectString,\n    });\n};\nconst de_LexiconDescriptionList = (output, context) => {\n    const retVal = (output || [])\n        .filter((e) => e != null)\n        .map((entry) => {\n        return de_LexiconDescription(entry, context);\n    });\n    return retVal;\n};\nconst de_SynthesisTask = (output, context) => {\n    return take(output, {\n        CreationTime: (_) => __expectNonNull(__parseEpochTimestamp(__expectNumber(_))),\n        Engine: __expectString,\n        LanguageCode: __expectString,\n        LexiconNames: _json,\n        OutputFormat: __expectString,\n        OutputUri: __expectString,\n        RequestCharacters: __expectInt32,\n        SampleRate: __expectString,\n        SnsTopicArn: __expectString,\n        SpeechMarkTypes: _json,\n        TaskId: __expectString,\n        TaskStatus: __expectString,\n        TaskStatusReason: __expectString,\n        TextType: __expectString,\n        VoiceId: __expectString,\n    });\n};\nconst de_SynthesisTasks = (output, context) => {\n    const retVal = (output || [])\n        .filter((e) => e != null)\n        .map((entry) => {\n        return de_SynthesisTask(entry, context);\n    });\n    return retVal;\n};\nconst deserializeMetadata = (output) => ({\n    httpStatusCode: output.statusCode,\n    requestId: output.headers[\"x-amzn-requestid\"] ?? output.headers[\"x-amzn-request-id\"] ?? output.headers[\"x-amz-request-id\"],\n    extendedRequestId: output.headers[\"x-amz-id-2\"],\n    cfId: output.headers[\"x-amz-cf-id\"],\n});\nconst collectBodyString = (streamBody, context) => collectBody(streamBody, context).then((body) => context.utf8Encoder(body));\nconst _CT = \"ContentType\";\nconst _E = \"Engine\";\nconst _IALC = \"IncludeAdditionalLanguageCodes\";\nconst _LC = \"LanguageCode\";\nconst _MR = \"MaxResults\";\nconst _NT = \"NextToken\";\nconst _RC = \"RequestCharacters\";\nconst _S = \"Status\";\nconst _ct = \"content-type\";\nconst _xar = \"x-amzn-requestcharacters\";\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { de_DeleteLexiconCommand, se_DeleteLexiconCommand } from \"../protocols/Aws_restJson1\";\nexport { $Command };\nexport class DeleteLexiconCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"Parrot_v1\", \"DeleteLexicon\", {})\n    .n(\"PollyClient\", \"DeleteLexiconCommand\")\n    .f(void 0, void 0)\n    .ser(se_DeleteLexiconCommand)\n    .de(de_DeleteLexiconCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { de_DescribeVoicesCommand, se_DescribeVoicesCommand } from \"../protocols/Aws_restJson1\";\nexport { $Command };\nexport class DescribeVoicesCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"Parrot_v1\", \"DescribeVoices\", {})\n    .n(\"PollyClient\", \"DescribeVoicesCommand\")\n    .f(void 0, void 0)\n    .ser(se_DescribeVoicesCommand)\n    .de(de_DescribeVoicesCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { GetLexiconOutputFilterSensitiveLog } from \"../models/models_0\";\nimport { de_GetLexiconCommand, se_GetLexiconCommand } from \"../protocols/Aws_restJson1\";\nexport { $Command };\nexport class GetLexiconCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"Parrot_v1\", \"GetLexicon\", {})\n    .n(\"PollyClient\", \"GetLexiconCommand\")\n    .f(void 0, GetLexiconOutputFilterSensitiveLog)\n    .ser(se_GetLexiconCommand)\n    .de(de_GetLexiconCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { de_GetSpeechSynthesisTaskCommand, se_GetSpeechSynthesisTaskCommand } from \"../protocols/Aws_restJson1\";\nexport { $Command };\nexport class GetSpeechSynthesisTaskCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"Parrot_v1\", \"GetSpeechSynthesisTask\", {})\n    .n(\"PollyClient\", \"GetSpeechSynthesisTaskCommand\")\n    .f(void 0, void 0)\n    .ser(se_GetSpeechSynthesisTaskCommand)\n    .de(de_GetSpeechSynthesisTaskCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { de_ListLexiconsCommand, se_ListLexiconsCommand } from \"../protocols/Aws_restJson1\";\nexport { $Command };\nexport class ListLexiconsCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"Parrot_v1\", \"ListLexicons\", {})\n    .n(\"PollyClient\", \"ListLexiconsCommand\")\n    .f(void 0, void 0)\n    .ser(se_ListLexiconsCommand)\n    .de(de_ListLexiconsCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { de_ListSpeechSynthesisTasksCommand, se_ListSpeechSynthesisTasksCommand } from \"../protocols/Aws_restJson1\";\nexport { $Command };\nexport class ListSpeechSynthesisTasksCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"Parrot_v1\", \"ListSpeechSynthesisTasks\", {})\n    .n(\"PollyClient\", \"ListSpeechSynthesisTasksCommand\")\n    .f(void 0, void 0)\n    .ser(se_ListSpeechSynthesisTasksCommand)\n    .de(de_ListSpeechSynthesisTasksCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { PutLexiconInputFilterSensitiveLog } from \"../models/models_0\";\nimport { de_PutLexiconCommand, se_PutLexiconCommand } from \"../protocols/Aws_restJson1\";\nexport { $Command };\nexport class PutLexiconCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"Parrot_v1\", \"PutLexicon\", {})\n    .n(\"PollyClient\", \"PutLexiconCommand\")\n    .f(PutLexiconInputFilterSensitiveLog, void 0)\n    .ser(se_PutLexiconCommand)\n    .de(de_PutLexiconCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { de_StartSpeechSynthesisTaskCommand, se_StartSpeechSynthesisTaskCommand } from \"../protocols/Aws_restJson1\";\nexport { $Command };\nexport class StartSpeechSynthesisTaskCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"Parrot_v1\", \"StartSpeechSynthesisTask\", {})\n    .n(\"PollyClient\", \"StartSpeechSynthesisTaskCommand\")\n    .f(void 0, void 0)\n    .ser(se_StartSpeechSynthesisTaskCommand)\n    .de(de_StartSpeechSynthesisTaskCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { SynthesizeSpeechOutputFilterSensitiveLog, } from \"../models/models_0\";\nimport { de_SynthesizeSpeechCommand, se_SynthesizeSpeechCommand } from \"../protocols/Aws_restJson1\";\nexport { $Command };\nexport class SynthesizeSpeechCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"Parrot_v1\", \"SynthesizeSpeech\", {})\n    .n(\"PollyClient\", \"SynthesizeSpeechCommand\")\n    .f(void 0, SynthesizeSpeechOutputFilterSensitiveLog)\n    .ser(se_SynthesizeSpeechCommand)\n    .de(de_SynthesizeSpeechCommand)\n    .build() {\n}\n", "import { createAggregatedClient } from \"@smithy/smithy-client\";\nimport { DeleteLexiconCommand, } from \"./commands/DeleteLexiconCommand\";\nimport { DescribeVoicesCommand, } from \"./commands/DescribeVoicesCommand\";\nimport { GetLexiconCommand } from \"./commands/GetLexiconCommand\";\nimport { GetSpeechSynthesisTaskCommand, } from \"./commands/GetSpeechSynthesisTaskCommand\";\nimport { ListLexiconsCommand, } from \"./commands/ListLexiconsCommand\";\nimport { ListSpeechSynthesisTasksCommand, } from \"./commands/ListSpeechSynthesisTasksCommand\";\nimport { PutLexiconCommand } from \"./commands/PutLexiconCommand\";\nimport { StartSpeechSynthesisTaskCommand, } from \"./commands/StartSpeechSynthesisTaskCommand\";\nimport { SynthesizeSpeechCommand, } from \"./commands/SynthesizeSpeechCommand\";\nimport { PollyClient } from \"./PollyClient\";\nconst commands = {\n    DeleteLexiconCommand,\n    DescribeVoicesCommand,\n    GetLexiconCommand,\n    GetSpeechSynthesisTaskCommand,\n    ListLexiconsCommand,\n    ListSpeechSynthesisTasksCommand,\n    PutLexiconCommand,\n    StartSpeechSynthesisTaskCommand,\n    SynthesizeSpeechCommand,\n};\nexport class Polly extends PollyClient {\n}\ncreateAggregatedClient(commands, Polly);\n", "import { createPaginator } from \"@smithy/core\";\nimport { ListSpeechSynthesisTasksCommand, } from \"../commands/ListSpeechSynthesisTasksCommand\";\nimport { PollyClient } from \"../PollyClient\";\nexport const paginateListSpeechSynthesisTasks = createPaginator(PollyClient, ListSpeechSynthesisTasksCommand, \"NextToken\", \"NextToken\", \"MaxResults\");\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEO,IAAM,+CAA+C,OAAO,QAAQ,SAAS,UAAU;AAC1F,SAAO;AAAA,IACH,WAAW,iBAAiB,OAAO,EAAE;AAAA,IACrC,QAAS,MAAM,kBAAkB,OAAO,MAAM,EAAE,MAC3C,MAAM;AACH,YAAM,IAAI,MAAM,yDAAyD;AAAA,IAC7E,GAAG;AAAA,EACX;AACJ;AACA,SAAS,iCAAiC,gBAAgB;AACtD,SAAO;AAAA,IACH,UAAU;AAAA,IACV,mBAAmB;AAAA,MACf,MAAM;AAAA,MACN,QAAQ,eAAe;AAAA,IAC3B;AAAA,IACA,qBAAqB,CAAC,QAAQ,aAAa;AAAA,MACvC,mBAAmB;AAAA,QACf;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACJ;AACO,IAAM,qCAAqC,CAAC,mBAAmB;AAClE,QAAM,UAAU,CAAC;AACjB,UAAQ,eAAe,WAAW;AAAA,IAC9B,SAAS;AACL,cAAQ,KAAK,iCAAiC,cAAc,CAAC;AAAA,IACjE;AAAA,EACJ;AACA,SAAO;AACX;AACO,IAAM,8BAA8B,CAAC,WAAW;AACnD,QAAM,WAAW,yBAAyB,MAAM;AAChD,SAAO;AAAA,IACH,GAAG;AAAA,EACP;AACJ;;;ACxCO,IAAM,kCAAkC,CAAC,YAAY;AACxD,SAAO;AAAA,IACH,GAAG;AAAA,IACH,sBAAsB,QAAQ,wBAAwB;AAAA,IACtD,iBAAiB,QAAQ,mBAAmB;AAAA,IAC5C,oBAAoB;AAAA,EACxB;AACJ;AACO,IAAM,eAAe;AAAA,EACxB,SAAS,EAAE,MAAM,iBAAiB,MAAM,kBAAkB;AAAA,EAC1D,UAAU,EAAE,MAAM,iBAAiB,MAAM,WAAW;AAAA,EACpD,QAAQ,EAAE,MAAM,iBAAiB,MAAM,SAAS;AAAA,EAChD,cAAc,EAAE,MAAM,iBAAiB,MAAM,uBAAuB;AACxE;;;ACbA;AAAA,EACE,MAAQ;AAAA,EACR,aAAe;AAAA,EACf,SAAW;AAAA,EACX,SAAW;AAAA,IACT,OAAS;AAAA,IACT,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,sBAAsB;AAAA,IACtB,eAAe;AAAA,IACf,yBAAyB;AAAA,IACzB,OAAS;AAAA,IACT,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,EACrB;AAAA,EACA,MAAQ;AAAA,EACR,OAAS;AAAA,EACT,QAAU;AAAA,EACV,aAAe;AAAA,EACf,cAAgB;AAAA,IACd,8BAA8B;AAAA,IAC9B,yBAAyB;AAAA,IACzB,4BAA4B;AAAA,IAC5B,uBAAuB;AAAA,IACvB,iBAAiB;AAAA,IACjB,qCAAqC;AAAA,IACrC,mCAAmC;AAAA,IACnC,8BAA8B;AAAA,IAC9B,2CAA2C;AAAA,IAC3C,kCAAkC;AAAA,IAClC,mCAAmC;AAAA,IACnC,kBAAkB;AAAA,IAClB,2BAA2B;AAAA,IAC3B,oCAAoC;AAAA,IACpC,iCAAiC;AAAA,IACjC,2BAA2B;AAAA,IAC3B,gBAAgB;AAAA,IAChB,8BAA8B;AAAA,IAC9B,qBAAqB;AAAA,IACrB,8BAA8B;AAAA,IAC9B,qCAAqC;AAAA,IACrC,+BAA+B;AAAA,IAC/B,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,gCAAgC;AAAA,IAChC,6BAA6B;AAAA,IAC7B,yBAAyB;AAAA,IACzB,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,sBAAsB;AAAA,IACtB,uBAAuB;AAAA,IACvB,oCAAoC;AAAA,IACpC,iCAAiC;AAAA,IACjC,sCAAsC;AAAA,IACtC,mCAAmC;AAAA,IACnC,0BAA0B;AAAA,IAC1B,2BAA2B;AAAA,IAC3B,sBAAsB;AAAA,IACtB,uBAAuB;AAAA,IACvB,qBAAqB;AAAA,IACrB,OAAS;AAAA,EACX;AAAA,EACA,iBAAmB;AAAA,IACjB,oBAAoB;AAAA,IACpB,eAAe;AAAA,IACf,cAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,QAAU;AAAA,IACV,YAAc;AAAA,EAChB;AAAA,EACA,SAAW;AAAA,IACT,MAAQ;AAAA,EACV;AAAA,EACA,eAAiB;AAAA,IACf,QAAQ;AAAA,MACN,gBAAgB;AAAA,QACd;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAS;AAAA,IACP;AAAA,EACF;AAAA,EACA,QAAU;AAAA,IACR,MAAQ;AAAA,IACR,KAAO;AAAA,EACT;AAAA,EACA,SAAW;AAAA,EACX,SAAW;AAAA,IACT,2BAA2B;AAAA,EAC7B;AAAA,EACA,gBAAgB;AAAA,IACd,2BAA2B;AAAA,EAC7B;AAAA,EACA,UAAY;AAAA,EACZ,YAAc;AAAA,IACZ,MAAQ;AAAA,IACR,KAAO;AAAA,IACP,WAAa;AAAA,EACf;AACF;;;ACrGA,IAAM,IAAI;AAAV,IAAsB,IAAI;AAA1B,IAAgC,IAAI;AAApC,IAA4C,IAAI;AAChD,IAAM,IAAI;AAAV,IAAgB,IAAI;AAApB,IAA6B,IAAI;AAAjC,IAAkD,IAAI;AAAtD,IAA+D,IAAI;AAAnE,IAA+E,IAAI;AAAnF,IAA2F,IAAI;AAA/F,IAAkH,IAAI,EAAE,CAAC,CAAC,GAAG,OAAO,QAAQ,SAAS;AAArJ,IAAwJ,IAAI,EAAE,CAAC,CAAC,GAAG,MAAM,WAAW,OAAO,QAAQ,UAAU;AAA7M,IAAgN,IAAI,EAAE,CAAC,CAAC,GAAG,WAAW;AAAtO,IAAyO,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,UAAU,GAAG,IAAI,EAAE;AAAvR,IAA0R,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,eAAe,GAAG,IAAI,EAAE;AAA7U,IAAgV,IAAI,CAAC;AAArV,IAAwV,IAAI,EAAE,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,cAAc,EAAE;AAAhZ,IAAmZ,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,mBAAmB,EAAE,CAAC,EAAE;AAAze,IAA4e,IAAI,CAAC,CAAC;AAAlf,IAAqf,IAAI,CAAC,CAAC;AAA3f,IAA8f,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,SAAS,CAAC;AACphB,IAAM,QAAQ,EAAE,SAAS,OAAO,YAAY,EAAE,QAAQ,GAAG,cAAc,GAAG,SAAS,GAAG,UAAU,EAAE,GAAG,OAAO,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,EAAE,YAAY,GAAG,OAAO,qEAAqE,MAAM,EAAE,GAAG,EAAE,YAAY,GAAG,OAAO,0EAA0E,MAAM,EAAE,GAAG,EAAE,UAAU,EAAE,KAAK,GAAG,YAAY,GAAG,SAAS,EAAE,GAAG,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,GAAG,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,OAAO,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,GAAG,iBAAiB,CAAC,CAAC,GAAG,GAAG,QAAQ,EAAE,CAAC,GAAG,OAAO,CAAC,EAAE,YAAY,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,GAAG,OAAO,CAAC,EAAE,UAAU,EAAE,KAAK,oEAAoE,YAAY,GAAG,SAAS,EAAE,GAAG,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,GAAG,EAAE,OAAO,mFAAmF,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,GAAG,EAAE,YAAY,GAAG,OAAO,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,EAAE,UAAU,EAAE,KAAK,2DAA2D,YAAY,GAAG,SAAS,EAAE,GAAG,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,GAAG,EAAE,OAAO,4DAA4D,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,GAAG,EAAE,YAAY,GAAG,OAAO,CAAC,EAAE,YAAY,CAAC,CAAC,GAAG,OAAO,CAAC,EAAE,UAAU,EAAE,KAAK,+DAA+D,YAAY,GAAG,SAAS,EAAE,GAAG,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,GAAG,EAAE,OAAO,sEAAsE,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,GAAG,EAAE,UAAU,EAAE,KAAK,sDAAsD,YAAY,GAAG,SAAS,EAAE,GAAG,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,GAAG,EAAE,OAAO,yCAAyC,MAAM,EAAE,CAAC,EAAE;AACloD,IAAM,UAAU;;;ACAvB,IAAM,QAAQ,IAAI,cAAc;AAAA,EAC5B,MAAM;AAAA,EACN,QAAQ,CAAC,YAAY,UAAU,gBAAgB,SAAS;AAC5D,CAAC;AACM,IAAM,0BAA0B,CAAC,gBAAgB,UAAU,CAAC,MAAM;AACrE,SAAO,MAAM,IAAI,gBAAgB,MAAM,gBAAgB,SAAS;AAAA,IAC5D;AAAA,IACA,QAAQ,QAAQ;AAAA,EACpB,CAAC,CAAC;AACN;AACA,wBAAwB,MAAM;;;ACLvB,IAAM,mBAAmB,CAAC,WAAW;AACxC,SAAO;AAAA,IACH,YAAY;AAAA,IACZ,gBAAe,iCAAQ,kBAAiB;AAAA,IACxC,gBAAe,iCAAQ,kBAAiB;AAAA,IACxC,oBAAmB,iCAAQ,sBAAqB;AAAA,IAChD,mBAAkB,iCAAQ,qBAAoB;AAAA,IAC9C,aAAY,iCAAQ,eAAc,CAAC;AAAA,IACnC,yBAAwB,iCAAQ,2BAA0B;AAAA,IAC1D,kBAAiB,iCAAQ,oBAAmB;AAAA,MACxC;AAAA,QACI,UAAU;AAAA,QACV,kBAAkB,CAAC,QAAQ,IAAI,oBAAoB,gBAAgB;AAAA,QACnE,QAAQ,IAAI,kBAAkB;AAAA,MAClC;AAAA,IACJ;AAAA,IACA,SAAQ,iCAAQ,WAAU,IAAI,WAAW;AAAA,IACzC,iBAAgB,iCAAQ,mBAAkB;AAAA,IAC1C,YAAW,iCAAQ,cAAa;AAAA,IAChC,YAAW,iCAAQ,cAAa;AAAA,IAChC,cAAa,iCAAQ,gBAAe;AAAA,IACpC,cAAa,iCAAQ,gBAAe;AAAA,EACxC;AACJ;;;ACpBO,IAAMA,oBAAmB,CAAC,WAAW;AACxC,QAAM,eAAe,0BAA0B,MAAM;AACrD,QAAM,wBAAwB,MAAM,aAAa,EAAE,KAAK,yBAAyB;AACjF,QAAM,qBAAqB,iBAAuB,MAAM;AACxD,SAAO;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,SAAS;AAAA,IACT;AAAA,IACA,oBAAmB,iCAAQ,sBAAqB;AAAA,IAChD,4BAA2B,iCAAQ,+BAA8B,CAAC,MAAM,MAAM,QAAQ,OAAO,IAAI,MAAM,uBAAuB,CAAC;AAAA,IAC/H,2BAA0B,iCAAQ,6BAC9B,+BAA+B,EAAE,WAAW,mBAAmB,WAAW,eAAe,gBAAY,QAAQ,CAAC;AAAA,IAClH,cAAa,iCAAQ,gBAAe;AAAA,IACpC,SAAQ,iCAAQ,WAAU,gBAAgB,mBAAmB;AAAA,IAC7D,gBAAgB,iBAAe,QAAO,iCAAQ,mBAAkB,qBAAqB;AAAA,IACrF,YAAW,iCAAQ,eAAc,aAAa,MAAM,sBAAsB,GAAG,aAAa;AAAA,IAC1F,SAAQ,iCAAQ,WAAU;AAAA,IAC1B,kBAAiB,iCAAQ,oBAAmB;AAAA,IAC5C,uBAAsB,iCAAQ,0BAAyB,MAAM,QAAQ,QAAQ,8BAA8B;AAAA,IAC3G,kBAAiB,iCAAQ,qBAAoB,MAAM,QAAQ,QAAQ,yBAAyB;AAAA,EAChG;AACJ;;;ACjCO,IAAM,oCAAoC,CAAC,kBAAkB;AAChE,QAAM,mBAAmB,cAAc;AACvC,MAAI,0BAA0B,cAAc;AAC5C,MAAI,eAAe,cAAc;AACjC,SAAO;AAAA,IACH,kBAAkB,gBAAgB;AAC9B,YAAM,QAAQ,iBAAiB,UAAU,CAAC,WAAW,OAAO,aAAa,eAAe,QAAQ;AAChG,UAAI,UAAU,IAAI;AACd,yBAAiB,KAAK,cAAc;AAAA,MACxC,OACK;AACD,yBAAiB,OAAO,OAAO,GAAG,cAAc;AAAA,MACpD;AAAA,IACJ;AAAA,IACA,kBAAkB;AACd,aAAO;AAAA,IACX;AAAA,IACA,0BAA0B,wBAAwB;AAC9C,gCAA0B;AAAA,IAC9B;AAAA,IACA,yBAAyB;AACrB,aAAO;AAAA,IACX;AAAA,IACA,eAAe,aAAa;AACxB,qBAAe;AAAA,IACnB;AAAA,IACA,cAAc;AACV,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;AACO,IAAM,+BAA+B,CAAC,WAAW;AACpD,SAAO;AAAA,IACH,iBAAiB,OAAO,gBAAgB;AAAA,IACxC,wBAAwB,OAAO,uBAAuB;AAAA,IACtD,aAAa,OAAO,YAAY;AAAA,EACpC;AACJ;;;ACjCA,IAAM,YAAY,CAACC,OAAMA;AAClB,IAAM,2BAA2B,CAAC,eAAe,eAAe;AACnE,QAAM,yBAAyB;AAAA,IAC3B,GAAG,UAAU,mCAAmC,aAAa,CAAC;AAAA,IAC9D,GAAG,UAAU,iCAAiC,aAAa,CAAC;AAAA,IAC5D,GAAG,UAAU,qCAAqC,aAAa,CAAC;AAAA,IAChE,GAAG,UAAU,kCAAkC,aAAa,CAAC;AAAA,EACjE;AACA,aAAW,QAAQ,CAAC,cAAc,UAAU,UAAU,sBAAsB,CAAC;AAC7E,SAAO;AAAA,IACH,GAAG;AAAA,IACH,GAAG,uCAAuC,sBAAsB;AAAA,IAChE,GAAG,4BAA4B,sBAAsB;AAAA,IACrD,GAAG,gCAAgC,sBAAsB;AAAA,IACzD,GAAG,6BAA6B,sBAAsB;AAAA,EAC1D;AACJ;;;ACLO,IAAM,cAAN,cAA0B,OAAS;AAAA,EACtC,eAAe,CAAC,aAAa,GAAG;AAC5B,UAAM,YAAYC,kBAAmB,iBAAiB,CAAC,CAAC;AACxD,UAAM,YAAY,gCAAgC,SAAS;AAC3D,UAAM,YAAY,uBAAuB,SAAS;AAClD,UAAM,YAAY,mBAAmB,SAAS;AAC9C,UAAM,YAAY,oBAAoB,SAAS;AAC/C,UAAM,YAAY,wBAAwB,SAAS;AACnD,UAAM,YAAY,sBAAsB,SAAS;AACjD,UAAM,YAAY,4BAA4B,SAAS;AACvD,UAAM,YAAY,yBAAyB,YAAW,+CAAe,eAAc,CAAC,CAAC;AACrF,UAAM,SAAS;AACf,SAAK,SAAS;AACd,SAAK,gBAAgB,IAAI,mBAAmB,KAAK,MAAM,CAAC;AACxD,SAAK,gBAAgB,IAAI,eAAe,KAAK,MAAM,CAAC;AACpD,SAAK,gBAAgB,IAAI,uBAAuB,KAAK,MAAM,CAAC;AAC5D,SAAK,gBAAgB,IAAI,oBAAoB,KAAK,MAAM,CAAC;AACzD,SAAK,gBAAgB,IAAI,gBAAgB,KAAK,MAAM,CAAC;AACrD,SAAK,gBAAgB,IAAI,4BAA4B,KAAK,MAAM,CAAC;AACjE,SAAK,gBAAgB,IAAI,uCAAuC,KAAK,QAAQ;AAAA,MACzE,kCAAkC;AAAA,MAClC,gCAAgC,OAAO,WAAW,IAAI,8BAA8B;AAAA,QAChF,kBAAkB,OAAO;AAAA,MAC7B,CAAC;AAAA,IACL,CAAC,CAAC;AACF,SAAK,gBAAgB,IAAI,qBAAqB,KAAK,MAAM,CAAC;AAAA,EAC9D;AAAA,EACA,UAAU;AACN,UAAM,QAAQ;AAAA,EAClB;AACJ;;;AC3CO,IAAM,wBAAN,MAAM,+BAA8B,iBAAmB;AAAA,EAC1D,YAAY,SAAS;AACjB,UAAM,OAAO;AACb,WAAO,eAAe,MAAM,uBAAsB,SAAS;AAAA,EAC/D;AACJ;;;ACLO,IAAM,2BAAN,MAAM,kCAAiC,sBAAgB;AAAA,EAC1D,YAAY,MAAM;AACd,UAAM;AAAA,MACF,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,GAAG;AAAA,IACP,CAAC;AACD,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,WAAO,eAAe,MAAM,0BAAyB,SAAS;AAAA,EAClE;AACJ;AACO,IAAM,0BAAN,MAAM,iCAAgC,sBAAgB;AAAA,EACzD,YAAY,MAAM;AACd,UAAM;AAAA,MACF,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,GAAG;AAAA,IACP,CAAC;AACD,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,WAAO,eAAe,MAAM,yBAAwB,SAAS;AAAA,EACjE;AACJ;AACO,IAAM,SAAS;AAAA,EAClB,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,UAAU;AACd;AACO,IAAM,eAAe;AAAA,EACxB,OAAO;AAAA,EACP,KAAK;AAAA,EACL,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,WAAW;AAAA,EACX,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AACZ;AACO,IAAM,SAAS;AAAA,EAClB,QAAQ;AAAA,EACR,MAAM;AACV;AACO,IAAM,UAAU;AAAA,EACnB,OAAO;AAAA,EACP,SAAS;AAAA,EACT,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,UAAU;AAAA,EACV,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,SAAS;AAAA,EACT,KAAK;AAAA,EACL,OAAO;AAAA,EACP,WAAW;AAAA,EACX,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,KAAK;AAAA,EACL,MAAM;AAAA,EACN,UAAU;AAAA,EACV,KAAK;AAAA,EACL,OAAO;AAAA,EACP,KAAK;AAAA,EACL,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,UAAU;AAAA,EACV,OAAO;AAAA,EACP,KAAK;AAAA,EACL,MAAM;AAAA,EACN,MAAM;AAAA,EACN,KAAK;AAAA,EACL,OAAO;AAAA,EACP,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,OAAO;AAAA,EACP,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,UAAU;AAAA,EACV,SAAS;AAAA,EACT,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,SAAS;AAAA,EACT,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,SAAS;AAAA,EACT,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,SAAS;AAAA,EACT,MAAM;AAAA,EACN,OAAO;AAAA,EACP,OAAO;AACX;AACO,IAAM,4BAAN,MAAM,mCAAkC,sBAAgB;AAAA,EAC3D,YAAY,MAAM;AACd,UAAM;AAAA,MACF,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,GAAG;AAAA,IACP,CAAC;AACD,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,WAAO,eAAe,MAAM,2BAA0B,SAAS;AAAA,EACnE;AACJ;AACO,IAAM,8BAAN,MAAM,qCAAoC,sBAAgB;AAAA,EAC7D,YAAY,MAAM;AACd,UAAM;AAAA,MACF,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,GAAG;AAAA,IACP,CAAC;AACD,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,WAAO,eAAe,MAAM,6BAA4B,SAAS;AAAA,EACrE;AACJ;AACO,IAAM,eAAe;AAAA,EACxB,MAAM;AAAA,EACN,KAAK;AAAA,EACL,YAAY;AAAA,EACZ,KAAK;AACT;AACO,IAAM,iBAAiB;AAAA,EAC1B,UAAU;AAAA,EACV,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,MAAM;AACV;AACO,IAAM,aAAa;AAAA,EACtB,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,WAAW;AACf;AACO,IAAM,WAAW;AAAA,EACpB,MAAM;AAAA,EACN,MAAM;AACV;AACO,IAAM,yBAAN,MAAM,gCAA+B,sBAAgB;AAAA,EACxD,YAAY,MAAM;AACd,UAAM;AAAA,MACF,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,GAAG;AAAA,IACP,CAAC;AACD,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,WAAO,eAAe,MAAM,wBAAuB,SAAS;AAAA,EAChE;AACJ;AACO,IAAM,iCAAN,MAAM,wCAAuC,sBAAgB;AAAA,EAChE,YAAY,MAAM;AACd,UAAM;AAAA,MACF,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,GAAG;AAAA,IACP,CAAC;AACD,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,WAAO,eAAe,MAAM,gCAA+B,SAAS;AAAA,EACxE;AACJ;AACO,IAAM,0BAAN,MAAM,iCAAgC,sBAAgB;AAAA,EACzD,YAAY,MAAM;AACd,UAAM;AAAA,MACF,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,GAAG;AAAA,IACP,CAAC;AACD,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,WAAO,eAAe,MAAM,yBAAwB,SAAS;AAAA,EACjE;AACJ;AACO,IAAM,2BAAN,MAAM,kCAAiC,sBAAgB;AAAA,EAC1D,YAAY,MAAM;AACd,UAAM;AAAA,MACF,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,GAAG;AAAA,IACP,CAAC;AACD,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,WAAO,eAAe,MAAM,0BAAyB,SAAS;AAAA,EAClE;AACJ;AACO,IAAM,wBAAN,MAAM,+BAA8B,sBAAgB;AAAA,EACvD,YAAY,MAAM;AACd,UAAM;AAAA,MACF,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,GAAG;AAAA,IACP,CAAC;AACD,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,WAAO,eAAe,MAAM,uBAAsB,SAAS;AAAA,EAC/D;AACJ;AACO,IAAM,6BAAN,MAAM,oCAAmC,sBAAgB;AAAA,EAC5D,YAAY,MAAM;AACd,UAAM;AAAA,MACF,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,GAAG;AAAA,IACP,CAAC;AACD,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,WAAO,eAAe,MAAM,4BAA2B,SAAS;AAAA,EACpE;AACJ;AACO,IAAM,8BAAN,MAAM,qCAAoC,sBAAgB;AAAA,EAC7D,YAAY,MAAM;AACd,UAAM;AAAA,MACF,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,GAAG;AAAA,IACP,CAAC;AACD,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,WAAO,eAAe,MAAM,6BAA4B,SAAS;AAAA,EACrE;AACJ;AACO,IAAM,uBAAN,MAAM,8BAA6B,sBAAgB;AAAA,EACtD,YAAY,MAAM;AACd,UAAM;AAAA,MACF,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,GAAG;AAAA,IACP,CAAC;AACD,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,WAAO,eAAe,MAAM,sBAAqB,SAAS;AAAA,EAC9D;AACJ;AACO,IAAM,gCAAN,MAAM,uCAAsC,sBAAgB;AAAA,EAC/D,YAAY,MAAM;AACd,UAAM;AAAA,MACF,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,GAAG;AAAA,IACP,CAAC;AACD,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,WAAO,eAAe,MAAM,+BAA8B,SAAS;AAAA,EACvE;AACJ;AACO,IAAM,+BAAN,MAAM,sCAAqC,sBAAgB;AAAA,EAC9D,YAAY,MAAM;AACd,UAAM;AAAA,MACF,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,GAAG;AAAA,IACP,CAAC;AACD,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,WAAO,eAAe,MAAM,8BAA6B,SAAS;AAAA,EACtE;AACJ;AACO,IAAM,sCAAN,MAAM,6CAA4C,sBAAgB;AAAA,EACrE,YAAY,MAAM;AACd,UAAM;AAAA,MACF,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,GAAG;AAAA,IACP,CAAC;AACD,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,WAAO,eAAe,MAAM,qCAAoC,SAAS;AAAA,EAC7E;AACJ;AACO,IAAM,mCAAN,MAAM,0CAAyC,sBAAgB;AAAA,EAClE,YAAY,MAAM;AACd,UAAM;AAAA,MACF,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,GAAG;AAAA,IACP,CAAC;AACD,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,WAAO,eAAe,MAAM,kCAAiC,SAAS;AAAA,EAC1E;AACJ;AACO,IAAM,qCAAN,MAAM,4CAA2C,sBAAgB;AAAA,EACpE,YAAY,MAAM;AACd,UAAM;AAAA,MACF,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,GAAG;AAAA,IACP,CAAC;AACD,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,WAAO,eAAe,MAAM,oCAAmC,SAAS;AAAA,EAC5E;AACJ;AACO,IAAM,kCAAN,MAAM,yCAAwC,sBAAgB;AAAA,EACjE,YAAY,MAAM;AACd,UAAM;AAAA,MACF,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,GAAG;AAAA,IACP,CAAC;AACD,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,WAAO,eAAe,MAAM,iCAAgC,SAAS;AAAA,EACzE;AACJ;AACO,IAAM,kCAAN,MAAM,yCAAwC,sBAAgB;AAAA,EACjE,YAAY,MAAM;AACd,UAAM;AAAA,MACF,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,GAAG;AAAA,IACP,CAAC;AACD,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,WAAO,eAAe,MAAM,iCAAgC,SAAS;AAAA,EACzE;AACJ;AACO,IAAM,4CAAN,MAAM,mDAAkD,sBAAgB;AAAA,EAC3E,YAAY,MAAM;AACd,UAAM;AAAA,MACF,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,GAAG;AAAA,IACP,CAAC;AACD,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,WAAO,eAAe,MAAM,2CAA0C,SAAS;AAAA,EACnF;AACJ;AACO,IAAM,8BAAN,MAAM,qCAAoC,sBAAgB;AAAA,EAC7D,YAAY,MAAM;AACd,UAAM;AAAA,MACF,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,GAAG;AAAA,IACP,CAAC;AACD,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,WAAO,eAAe,MAAM,6BAA4B,SAAS;AAAA,EACrE;AACJ;AACO,IAAM,4BAA4B,CAAC,SAAS;AAAA,EAC/C,GAAG;AAAA,EACH,GAAI,IAAI,WAAW,EAAE,SAAS,iBAAiB;AACnD;AACO,IAAM,qCAAqC,CAAC,SAAS;AAAA,EACxD,GAAG;AAAA,EACH,GAAI,IAAI,WAAW,EAAE,SAAS,0BAA0B,IAAI,OAAO,EAAE;AACzE;AACO,IAAM,oCAAoC,CAAC,SAAS;AAAA,EACvD,GAAG;AAAA,EACH,GAAI,IAAI,WAAW,EAAE,SAAS,iBAAiB;AACnD;AACO,IAAM,2CAA2C,CAAC,SAAS;AAAA,EAC9D,GAAG;AACP;;;ACtbO,IAAM,0BAA0B,OAAO,OAAO,YAAY;AAC7D,QAAMC,KAAI,eAAG,OAAO,OAAO;AAC3B,QAAM,UAAU,CAAC;AACjB,EAAAA,GAAE,GAAG,qBAAqB;AAC1B,EAAAA,GAAE,EAAE,QAAQ,MAAM,MAAM,MAAM,UAAU,KAAK;AAC7C,MAAI;AACJ,EAAAA,GAAE,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,EAAE,IAAI;AAC/B,SAAOA,GAAE,MAAM;AACnB;AACO,IAAM,2BAA2B,OAAO,OAAO,YAAY;AAC9D,QAAMA,KAAI,eAAG,OAAO,OAAO;AAC3B,QAAM,UAAU,CAAC;AACjB,EAAAA,GAAE,GAAG,YAAY;AACjB,QAAM,QAAQ,IAAI;AAAA,IACd,CAAC,EAAE,GAAG,CAAC,EAAE,MAAM,EAAE,CAAC;AAAA,IAClB,CAAC,GAAG,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC;AAAA,IACpB,CAAC,KAAK,GAAG,CAAC,MAAM,MAAM,mCAAmC,QAAQ,MAAM,MAAM,KAAK,EAAE,SAAS,CAAC;AAAA,IAC9F,CAAC,GAAG,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC;AAAA,EACxB,CAAC;AACD,MAAI;AACJ,EAAAA,GAAE,EAAE,KAAK,EAAE,EAAE,OAAO,EAAE,EAAE,KAAK,EAAE,EAAE,IAAI;AACrC,SAAOA,GAAE,MAAM;AACnB;AACO,IAAM,uBAAuB,OAAO,OAAO,YAAY;AAC1D,QAAMA,KAAI,eAAG,OAAO,OAAO;AAC3B,QAAM,UAAU,CAAC;AACjB,EAAAA,GAAE,GAAG,qBAAqB;AAC1B,EAAAA,GAAE,EAAE,QAAQ,MAAM,MAAM,MAAM,UAAU,KAAK;AAC7C,MAAI;AACJ,EAAAA,GAAE,EAAE,KAAK,EAAE,EAAE,OAAO,EAAE,EAAE,IAAI;AAC5B,SAAOA,GAAE,MAAM;AACnB;AACO,IAAM,mCAAmC,OAAO,OAAO,YAAY;AACtE,QAAMA,KAAI,eAAG,OAAO,OAAO;AAC3B,QAAM,UAAU,CAAC;AACjB,EAAAA,GAAE,GAAG,6BAA6B;AAClC,EAAAA,GAAE,EAAE,UAAU,MAAM,MAAM,QAAQ,YAAY,KAAK;AACnD,MAAI;AACJ,EAAAA,GAAE,EAAE,KAAK,EAAE,EAAE,OAAO,EAAE,EAAE,IAAI;AAC5B,SAAOA,GAAE,MAAM;AACnB;AACO,IAAM,yBAAyB,OAAO,OAAO,YAAY;AAC5D,QAAMA,KAAI,eAAG,OAAO,OAAO;AAC3B,QAAM,UAAU,CAAC;AACjB,EAAAA,GAAE,GAAG,cAAc;AACnB,QAAM,QAAQ,IAAI;AAAA,IACd,CAAC,GAAG,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC;AAAA,EACxB,CAAC;AACD,MAAI;AACJ,EAAAA,GAAE,EAAE,KAAK,EAAE,EAAE,OAAO,EAAE,EAAE,KAAK,EAAE,EAAE,IAAI;AACrC,SAAOA,GAAE,MAAM;AACnB;AACO,IAAM,qCAAqC,OAAO,OAAO,YAAY;AACxE,QAAMA,KAAI,eAAG,OAAO,OAAO;AAC3B,QAAM,UAAU,CAAC;AACjB,EAAAA,GAAE,GAAG,oBAAoB;AACzB,QAAM,QAAQ,IAAI;AAAA,IACd,CAAC,GAAG,GAAG,CAAC,MAAM,MAAM,eAAe,QAAQ,MAAM,MAAM,GAAG,EAAE,SAAS,CAAC;AAAA,IACtE,CAAC,GAAG,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC;AAAA,IACpB,CAAC,EAAE,GAAG,CAAC,EAAE,MAAM,EAAE,CAAC;AAAA,EACtB,CAAC;AACD,MAAI;AACJ,EAAAA,GAAE,EAAE,KAAK,EAAE,EAAE,OAAO,EAAE,EAAE,KAAK,EAAE,EAAE,IAAI;AACrC,SAAOA,GAAE,MAAM;AACnB;AACO,IAAM,uBAAuB,OAAO,OAAO,YAAY;AAC1D,QAAMA,KAAI,eAAG,OAAO,OAAO;AAC3B,QAAM,UAAU;AAAA,IACZ,gBAAgB;AAAA,EACpB;AACA,EAAAA,GAAE,GAAG,qBAAqB;AAC1B,EAAAA,GAAE,EAAE,QAAQ,MAAM,MAAM,MAAM,UAAU,KAAK;AAC7C,MAAI;AACJ,SAAO,KAAK,UAAU,KAAK,OAAO;AAAA,IAC9B,SAAS,CAAC;AAAA,EACd,CAAC,CAAC;AACF,EAAAA,GAAE,EAAE,KAAK,EAAE,EAAE,OAAO,EAAE,EAAE,IAAI;AAC5B,SAAOA,GAAE,MAAM;AACnB;AACO,IAAM,qCAAqC,OAAO,OAAO,YAAY;AACxE,QAAMA,KAAI,eAAG,OAAO,OAAO;AAC3B,QAAM,UAAU;AAAA,IACZ,gBAAgB;AAAA,EACpB;AACA,EAAAA,GAAE,GAAG,oBAAoB;AACzB,MAAI;AACJ,SAAO,KAAK,UAAU,KAAK,OAAO;AAAA,IAC9B,QAAQ,CAAC;AAAA,IACT,cAAc,CAAC;AAAA,IACf,cAAc,CAAC,MAAM,MAAM,CAAC;AAAA,IAC5B,cAAc,CAAC;AAAA,IACf,oBAAoB,CAAC;AAAA,IACrB,mBAAmB,CAAC;AAAA,IACpB,YAAY,CAAC;AAAA,IACb,aAAa,CAAC;AAAA,IACd,iBAAiB,CAAC,MAAM,MAAM,CAAC;AAAA,IAC/B,MAAM,CAAC;AAAA,IACP,UAAU,CAAC;AAAA,IACX,SAAS,CAAC;AAAA,EACd,CAAC,CAAC;AACF,EAAAA,GAAE,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,EAAE,IAAI;AAC7B,SAAOA,GAAE,MAAM;AACnB;AACO,IAAM,6BAA6B,OAAO,OAAO,YAAY;AAChE,QAAMA,KAAI,eAAG,OAAO,OAAO;AAC3B,QAAM,UAAU;AAAA,IACZ,gBAAgB;AAAA,EACpB;AACA,EAAAA,GAAE,GAAG,YAAY;AACjB,MAAI;AACJ,SAAO,KAAK,UAAU,KAAK,OAAO;AAAA,IAC9B,QAAQ,CAAC;AAAA,IACT,cAAc,CAAC;AAAA,IACf,cAAc,CAAC,MAAM,MAAM,CAAC;AAAA,IAC5B,cAAc,CAAC;AAAA,IACf,YAAY,CAAC;AAAA,IACb,iBAAiB,CAAC,MAAM,MAAM,CAAC;AAAA,IAC/B,MAAM,CAAC;AAAA,IACP,UAAU,CAAC;AAAA,IACX,SAAS,CAAC;AAAA,EACd,CAAC,CAAC;AACF,EAAAA,GAAE,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,EAAE,IAAI;AAC7B,SAAOA,GAAE,MAAM;AACnB;AACO,IAAM,0BAA0B,OAAO,QAAQ,YAAY;AAC9D,MAAI,OAAO,eAAe,OAAO,OAAO,cAAc,KAAK;AACvD,WAAO,gBAAgB,QAAQ,OAAO;AAAA,EAC1C;AACA,QAAM,WAAW,IAAI;AAAA,IACjB,WAAW,oBAAoB,MAAM;AAAA,EACzC,CAAC;AACD,QAAM,YAAY,OAAO,MAAM,OAAO;AACtC,SAAO;AACX;AACO,IAAM,2BAA2B,OAAO,QAAQ,YAAY;AAC/D,MAAI,OAAO,eAAe,OAAO,OAAO,cAAc,KAAK;AACvD,WAAO,gBAAgB,QAAQ,OAAO;AAAA,EAC1C;AACA,QAAM,WAAW,IAAI;AAAA,IACjB,WAAW,oBAAoB,MAAM;AAAA,EACzC,CAAC;AACD,QAAM,OAAO,cAAgB,aAAe,MAAM,cAAU,OAAO,MAAM,OAAO,CAAC,GAAG,MAAM;AAC1F,QAAM,MAAM,KAAK,MAAM;AAAA,IACnB,WAAW;AAAA,IACX,QAAQ;AAAA,EACZ,CAAC;AACD,SAAO,OAAO,UAAU,GAAG;AAC3B,SAAO;AACX;AACO,IAAM,uBAAuB,OAAO,QAAQ,YAAY;AAC3D,MAAI,OAAO,eAAe,OAAO,OAAO,cAAc,KAAK;AACvD,WAAO,gBAAgB,QAAQ,OAAO;AAAA,EAC1C;AACA,QAAM,WAAW,IAAI;AAAA,IACjB,WAAW,oBAAoB,MAAM;AAAA,EACzC,CAAC;AACD,QAAM,OAAO,cAAgB,aAAe,MAAM,cAAU,OAAO,MAAM,OAAO,CAAC,GAAG,MAAM;AAC1F,QAAM,MAAM,KAAK,MAAM;AAAA,IACnB,SAAS;AAAA,IACT,mBAAmB,CAAC,MAAM,qBAAqB,GAAG,OAAO;AAAA,EAC7D,CAAC;AACD,SAAO,OAAO,UAAU,GAAG;AAC3B,SAAO;AACX;AACO,IAAM,mCAAmC,OAAO,QAAQ,YAAY;AACvE,MAAI,OAAO,eAAe,OAAO,OAAO,cAAc,KAAK;AACvD,WAAO,gBAAgB,QAAQ,OAAO;AAAA,EAC1C;AACA,QAAM,WAAW,IAAI;AAAA,IACjB,WAAW,oBAAoB,MAAM;AAAA,EACzC,CAAC;AACD,QAAM,OAAO,cAAgB,aAAe,MAAM,cAAU,OAAO,MAAM,OAAO,CAAC,GAAG,MAAM;AAC1F,QAAM,MAAM,KAAK,MAAM;AAAA,IACnB,eAAe,CAAC,MAAM,iBAAiB,GAAG,OAAO;AAAA,EACrD,CAAC;AACD,SAAO,OAAO,UAAU,GAAG;AAC3B,SAAO;AACX;AACO,IAAM,yBAAyB,OAAO,QAAQ,YAAY;AAC7D,MAAI,OAAO,eAAe,OAAO,OAAO,cAAc,KAAK;AACvD,WAAO,gBAAgB,QAAQ,OAAO;AAAA,EAC1C;AACA,QAAM,WAAW,IAAI;AAAA,IACjB,WAAW,oBAAoB,MAAM;AAAA,EACzC,CAAC;AACD,QAAM,OAAO,cAAgB,aAAe,MAAM,cAAU,OAAO,MAAM,OAAO,CAAC,GAAG,MAAM;AAC1F,QAAM,MAAM,KAAK,MAAM;AAAA,IACnB,UAAU,CAAC,MAAM,0BAA0B,GAAG,OAAO;AAAA,IACrD,WAAW;AAAA,EACf,CAAC;AACD,SAAO,OAAO,UAAU,GAAG;AAC3B,SAAO;AACX;AACO,IAAM,qCAAqC,OAAO,QAAQ,YAAY;AACzE,MAAI,OAAO,eAAe,OAAO,OAAO,cAAc,KAAK;AACvD,WAAO,gBAAgB,QAAQ,OAAO;AAAA,EAC1C;AACA,QAAM,WAAW,IAAI;AAAA,IACjB,WAAW,oBAAoB,MAAM;AAAA,EACzC,CAAC;AACD,QAAM,OAAO,cAAgB,aAAe,MAAM,cAAU,OAAO,MAAM,OAAO,CAAC,GAAG,MAAM;AAC1F,QAAM,MAAM,KAAK,MAAM;AAAA,IACnB,WAAW;AAAA,IACX,gBAAgB,CAAC,MAAM,kBAAkB,GAAG,OAAO;AAAA,EACvD,CAAC;AACD,SAAO,OAAO,UAAU,GAAG;AAC3B,SAAO;AACX;AACO,IAAM,uBAAuB,OAAO,QAAQ,YAAY;AAC3D,MAAI,OAAO,eAAe,OAAO,OAAO,cAAc,KAAK;AACvD,WAAO,gBAAgB,QAAQ,OAAO;AAAA,EAC1C;AACA,QAAM,WAAW,IAAI;AAAA,IACjB,WAAW,oBAAoB,MAAM;AAAA,EACzC,CAAC;AACD,QAAM,YAAY,OAAO,MAAM,OAAO;AACtC,SAAO;AACX;AACO,IAAM,qCAAqC,OAAO,QAAQ,YAAY;AACzE,MAAI,OAAO,eAAe,OAAO,OAAO,cAAc,KAAK;AACvD,WAAO,gBAAgB,QAAQ,OAAO;AAAA,EAC1C;AACA,QAAM,WAAW,IAAI;AAAA,IACjB,WAAW,oBAAoB,MAAM;AAAA,EACzC,CAAC;AACD,QAAM,OAAO,cAAgB,aAAe,MAAM,cAAU,OAAO,MAAM,OAAO,CAAC,GAAG,MAAM;AAC1F,QAAM,MAAM,KAAK,MAAM;AAAA,IACnB,eAAe,CAAC,MAAM,iBAAiB,GAAG,OAAO;AAAA,EACrD,CAAC;AACD,SAAO,OAAO,UAAU,GAAG;AAC3B,SAAO;AACX;AACO,IAAM,6BAA6B,OAAO,QAAQ,YAAY;AACjE,MAAI,OAAO,eAAe,OAAO,OAAO,cAAc,KAAK;AACvD,WAAO,gBAAgB,QAAQ,OAAO;AAAA,EAC1C;AACA,QAAM,WAAW,IAAI;AAAA,IACjB,WAAW,oBAAoB,MAAM;AAAA,IACrC,CAAC,GAAG,GAAG,CAAC,EAAE,OAAO,QAAQ,GAAG,CAAC;AAAA,IAC7B,CAAC,GAAG,GAAG,CAAC,MAAM,WAAW,OAAO,QAAQ,IAAI,GAAG,MAAM,iBAAmB,OAAO,QAAQ,IAAI,CAAC,CAAC;AAAA,EACjG,CAAC;AACD,QAAM,OAAO,OAAO;AACpB,UAAQ,eAAe,IAAI;AAC3B,WAAS,cAAc;AACvB,SAAO;AACX;AACA,IAAM,kBAAkB,OAAO,QAAQ,YAAY;AAC/C,QAAM,eAAe;AAAA,IACjB,GAAG;AAAA,IACH,MAAM,MAAM,mBAAe,OAAO,MAAM,OAAO;AAAA,EACnD;AACA,QAAM,YAAY,sBAAsB,QAAQ,aAAa,IAAI;AACjE,UAAQ,WAAW;AAAA,IACf,KAAK;AAAA,IACL,KAAK;AACD,YAAM,MAAM,+BAA+B,cAAc,OAAO;AAAA,IACpE,KAAK;AAAA,IACL,KAAK;AACD,YAAM,MAAM,8BAA8B,cAAc,OAAO;AAAA,IACnE,KAAK;AAAA,IACL,KAAK;AACD,YAAM,MAAM,gCAAgC,cAAc,OAAO;AAAA,IACrE,KAAK;AAAA,IACL,KAAK;AACD,YAAM,MAAM,6BAA6B,cAAc,OAAO;AAAA,IAClE,KAAK;AAAA,IACL,KAAK;AACD,YAAM,MAAM,qCAAqC,cAAc,OAAO;AAAA,IAC1E,KAAK;AAAA,IACL,KAAK;AACD,YAAM,MAAM,8BAA8B,cAAc,OAAO;AAAA,IACnE,KAAK;AAAA,IACL,KAAK;AACD,YAAM,MAAM,mCAAmC,cAAc,OAAO;AAAA,IACxE,KAAK;AAAA,IACL,KAAK;AACD,YAAM,MAAM,uCAAuC,cAAc,OAAO;AAAA,IAC5E,KAAK;AAAA,IACL,KAAK;AACD,YAAM,MAAM,yCAAyC,cAAc,OAAO;AAAA,IAC9E,KAAK;AAAA,IACL,KAAK;AACD,YAAM,MAAM,sCAAsC,cAAc,OAAO;AAAA,IAC3E,KAAK;AAAA,IACL,KAAK;AACD,YAAM,MAAM,sCAAsC,cAAc,OAAO;AAAA,IAC3E,KAAK;AAAA,IACL,KAAK;AACD,YAAM,MAAM,kCAAkC,cAAc,OAAO;AAAA,IACvE,KAAK;AAAA,IACL,KAAK;AACD,YAAM,MAAM,+BAA+B,cAAc,OAAO;AAAA,IACpE,KAAK;AAAA,IACL,KAAK;AACD,YAAM,MAAM,4BAA4B,cAAc,OAAO;AAAA,IACjE,KAAK;AAAA,IACL,KAAK;AACD,YAAM,MAAM,iCAAiC,cAAc,OAAO;AAAA,IACtE,KAAK;AAAA,IACL,KAAK;AACD,YAAM,MAAM,kCAAkC,cAAc,OAAO;AAAA,IACvE,KAAK;AAAA,IACL,KAAK;AACD,YAAM,MAAM,2BAA2B,cAAc,OAAO;AAAA,IAChE,KAAK;AAAA,IACL,KAAK;AACD,YAAM,MAAM,oCAAoC,cAAc,OAAO;AAAA,IACzE,KAAK;AAAA,IACL,KAAK;AACD,YAAM,MAAM,0CAA0C,cAAc,OAAO;AAAA,IAC/E,KAAK;AAAA,IACL,KAAK;AACD,YAAM,MAAM,gDAAgD,cAAc,OAAO;AAAA,IACrF,KAAK;AAAA,IACL,KAAK;AACD,YAAM,MAAM,kCAAkC,cAAc,OAAO;AAAA,IACvE;AACI,YAAM,aAAa,aAAa;AAChC,aAAO,kBAAkB;AAAA,QACrB;AAAA,QACA;AAAA,QACA;AAAA,MACJ,CAAC;AAAA,EACT;AACJ;AACA,IAAM,oBAAoB,kBAAkB,qBAAe;AAC3D,IAAM,oCAAoC,OAAO,cAAc,YAAY;AACvE,QAAM,WAAW,IAAI,CAAC,CAAC;AACvB,QAAM,OAAO,aAAa;AAC1B,QAAM,MAAM,KAAK,MAAM;AAAA,IACnB,SAAS;AAAA,EACb,CAAC;AACD,SAAO,OAAO,UAAU,GAAG;AAC3B,QAAM,YAAY,IAAI,4BAA4B;AAAA,IAC9C,WAAW,oBAAoB,YAAY;AAAA,IAC3C,GAAG;AAAA,EACP,CAAC;AACD,SAAO,yBAA2B,WAAW,aAAa,IAAI;AAClE;AACA,IAAM,gCAAgC,OAAO,cAAc,YAAY;AACnE,QAAM,WAAW,IAAI,CAAC,CAAC;AACvB,QAAM,OAAO,aAAa;AAC1B,QAAM,MAAM,KAAK,MAAM;AAAA,IACnB,SAAS;AAAA,EACb,CAAC;AACD,SAAO,OAAO,UAAU,GAAG;AAC3B,QAAM,YAAY,IAAI,wBAAwB;AAAA,IAC1C,WAAW,oBAAoB,YAAY;AAAA,IAC3C,GAAG;AAAA,EACP,CAAC;AACD,SAAO,yBAA2B,WAAW,aAAa,IAAI;AAClE;AACA,IAAM,kCAAkC,OAAO,cAAc,YAAY;AACrE,QAAM,WAAW,IAAI,CAAC,CAAC;AACvB,QAAM,OAAO,aAAa;AAC1B,QAAM,MAAM,KAAK,MAAM;AAAA,IACnB,SAAS;AAAA,EACb,CAAC;AACD,SAAO,OAAO,UAAU,GAAG;AAC3B,QAAM,YAAY,IAAI,0BAA0B;AAAA,IAC5C,WAAW,oBAAoB,YAAY;AAAA,IAC3C,GAAG;AAAA,EACP,CAAC;AACD,SAAO,yBAA2B,WAAW,aAAa,IAAI;AAClE;AACA,IAAM,iCAAiC,OAAO,cAAc,YAAY;AACpE,QAAM,WAAW,IAAI,CAAC,CAAC;AACvB,QAAM,OAAO,aAAa;AAC1B,QAAM,MAAM,KAAK,MAAM;AAAA,IACnB,SAAS;AAAA,EACb,CAAC;AACD,SAAO,OAAO,UAAU,GAAG;AAC3B,QAAM,YAAY,IAAI,yBAAyB;AAAA,IAC3C,WAAW,oBAAoB,YAAY;AAAA,IAC3C,GAAG;AAAA,EACP,CAAC;AACD,SAAO,yBAA2B,WAAW,aAAa,IAAI;AAClE;AACA,IAAM,8BAA8B,OAAO,cAAc,YAAY;AACjE,QAAM,WAAW,IAAI,CAAC,CAAC;AACvB,QAAM,OAAO,aAAa;AAC1B,QAAM,MAAM,KAAK,MAAM;AAAA,IACnB,SAAS;AAAA,EACb,CAAC;AACD,SAAO,OAAO,UAAU,GAAG;AAC3B,QAAM,YAAY,IAAI,sBAAsB;AAAA,IACxC,WAAW,oBAAoB,YAAY;AAAA,IAC3C,GAAG;AAAA,EACP,CAAC;AACD,SAAO,yBAA2B,WAAW,aAAa,IAAI;AAClE;AACA,IAAM,mCAAmC,OAAO,cAAc,YAAY;AACtE,QAAM,WAAW,IAAI,CAAC,CAAC;AACvB,QAAM,OAAO,aAAa;AAC1B,QAAM,MAAM,KAAK,MAAM;AAAA,IACnB,SAAS;AAAA,EACb,CAAC;AACD,SAAO,OAAO,UAAU,GAAG;AAC3B,QAAM,YAAY,IAAI,2BAA2B;AAAA,IAC7C,WAAW,oBAAoB,YAAY;AAAA,IAC3C,GAAG;AAAA,EACP,CAAC;AACD,SAAO,yBAA2B,WAAW,aAAa,IAAI;AAClE;AACA,IAAM,oCAAoC,OAAO,cAAc,YAAY;AACvE,QAAM,WAAW,IAAI,CAAC,CAAC;AACvB,QAAM,OAAO,aAAa;AAC1B,QAAM,MAAM,KAAK,MAAM;AAAA,IACnB,SAAS;AAAA,EACb,CAAC;AACD,SAAO,OAAO,UAAU,GAAG;AAC3B,QAAM,YAAY,IAAI,4BAA4B;AAAA,IAC9C,WAAW,oBAAoB,YAAY;AAAA,IAC3C,GAAG;AAAA,EACP,CAAC;AACD,SAAO,yBAA2B,WAAW,aAAa,IAAI;AAClE;AACA,IAAM,6BAA6B,OAAO,cAAc,YAAY;AAChE,QAAM,WAAW,IAAI,CAAC,CAAC;AACvB,QAAM,OAAO,aAAa;AAC1B,QAAM,MAAM,KAAK,MAAM;AAAA,IACnB,SAAS;AAAA,EACb,CAAC;AACD,SAAO,OAAO,UAAU,GAAG;AAC3B,QAAM,YAAY,IAAI,qBAAqB;AAAA,IACvC,WAAW,oBAAoB,YAAY;AAAA,IAC3C,GAAG;AAAA,EACP,CAAC;AACD,SAAO,yBAA2B,WAAW,aAAa,IAAI;AAClE;AACA,IAAM,+BAA+B,OAAO,cAAc,YAAY;AAClE,QAAM,WAAW,IAAI,CAAC,CAAC;AACvB,QAAM,OAAO,aAAa;AAC1B,QAAM,MAAM,KAAK,MAAM;AAAA,IACnB,SAAS;AAAA,EACb,CAAC;AACD,SAAO,OAAO,UAAU,GAAG;AAC3B,QAAM,YAAY,IAAI,uBAAuB;AAAA,IACzC,WAAW,oBAAoB,YAAY;AAAA,IAC3C,GAAG;AAAA,EACP,CAAC;AACD,SAAO,yBAA2B,WAAW,aAAa,IAAI;AAClE;AACA,IAAM,sCAAsC,OAAO,cAAc,YAAY;AACzE,QAAM,WAAW,IAAI,CAAC,CAAC;AACvB,QAAM,OAAO,aAAa;AAC1B,QAAM,MAAM,KAAK,MAAM;AAAA,IACnB,SAAS;AAAA,EACb,CAAC;AACD,SAAO,OAAO,UAAU,GAAG;AAC3B,QAAM,YAAY,IAAI,8BAA8B;AAAA,IAChD,WAAW,oBAAoB,YAAY;AAAA,IAC3C,GAAG;AAAA,EACP,CAAC;AACD,SAAO,yBAA2B,WAAW,aAAa,IAAI;AAClE;AACA,IAAM,iCAAiC,OAAO,cAAc,YAAY;AACpE,QAAM,WAAW,IAAI,CAAC,CAAC;AACvB,QAAM,OAAO,aAAa;AAC1B,QAAM,MAAM,KAAK,MAAM;AAAA,IACnB,SAAS;AAAA,EACb,CAAC;AACD,SAAO,OAAO,UAAU,GAAG;AAC3B,QAAM,YAAY,IAAI,yBAAyB;AAAA,IAC3C,WAAW,oBAAoB,YAAY;AAAA,IAC3C,GAAG;AAAA,EACP,CAAC;AACD,SAAO,yBAA2B,WAAW,aAAa,IAAI;AAClE;AACA,IAAM,qCAAqC,OAAO,cAAc,YAAY;AACxE,QAAM,WAAW,IAAI,CAAC,CAAC;AACvB,QAAM,OAAO,aAAa;AAC1B,QAAM,MAAM,KAAK,MAAM;AAAA,IACnB,SAAS;AAAA,EACb,CAAC;AACD,SAAO,OAAO,UAAU,GAAG;AAC3B,QAAM,YAAY,IAAI,6BAA6B;AAAA,IAC/C,WAAW,oBAAoB,YAAY;AAAA,IAC3C,GAAG;AAAA,EACP,CAAC;AACD,SAAO,yBAA2B,WAAW,aAAa,IAAI;AAClE;AACA,IAAM,4CAA4C,OAAO,cAAc,YAAY;AAC/E,QAAM,WAAW,IAAI,CAAC,CAAC;AACvB,QAAM,OAAO,aAAa;AAC1B,QAAM,MAAM,KAAK,MAAM;AAAA,IACnB,SAAS;AAAA,EACb,CAAC;AACD,SAAO,OAAO,UAAU,GAAG;AAC3B,QAAM,YAAY,IAAI,oCAAoC;AAAA,IACtD,WAAW,oBAAoB,YAAY;AAAA,IAC3C,GAAG;AAAA,EACP,CAAC;AACD,SAAO,yBAA2B,WAAW,aAAa,IAAI;AAClE;AACA,IAAM,yCAAyC,OAAO,cAAc,YAAY;AAC5E,QAAM,WAAW,IAAI,CAAC,CAAC;AACvB,QAAM,OAAO,aAAa;AAC1B,QAAM,MAAM,KAAK,MAAM;AAAA,IACnB,SAAS;AAAA,EACb,CAAC;AACD,SAAO,OAAO,UAAU,GAAG;AAC3B,QAAM,YAAY,IAAI,iCAAiC;AAAA,IACnD,WAAW,oBAAoB,YAAY;AAAA,IAC3C,GAAG;AAAA,EACP,CAAC;AACD,SAAO,yBAA2B,WAAW,aAAa,IAAI;AAClE;AACA,IAAM,2CAA2C,OAAO,cAAc,YAAY;AAC9E,QAAM,WAAW,IAAI,CAAC,CAAC;AACvB,QAAM,OAAO,aAAa;AAC1B,QAAM,MAAM,KAAK,MAAM;AAAA,IACnB,SAAS;AAAA,EACb,CAAC;AACD,SAAO,OAAO,UAAU,GAAG;AAC3B,QAAM,YAAY,IAAI,mCAAmC;AAAA,IACrD,WAAW,oBAAoB,YAAY;AAAA,IAC3C,GAAG;AAAA,EACP,CAAC;AACD,SAAO,yBAA2B,WAAW,aAAa,IAAI;AAClE;AACA,IAAM,gCAAgC,OAAO,cAAc,YAAY;AACnE,QAAM,WAAW,IAAI,CAAC,CAAC;AACvB,QAAM,OAAO,aAAa;AAC1B,QAAM,MAAM,KAAK,MAAM;AAAA,IACnB,SAAS;AAAA,EACb,CAAC;AACD,SAAO,OAAO,UAAU,GAAG;AAC3B,QAAM,YAAY,IAAI,wBAAwB;AAAA,IAC1C,WAAW,oBAAoB,YAAY;AAAA,IAC3C,GAAG;AAAA,EACP,CAAC;AACD,SAAO,yBAA2B,WAAW,aAAa,IAAI;AAClE;AACA,IAAM,kDAAkD,OAAO,cAAc,YAAY;AACrF,QAAM,WAAW,IAAI,CAAC,CAAC;AACvB,QAAM,OAAO,aAAa;AAC1B,QAAM,MAAM,KAAK,MAAM;AAAA,IACnB,SAAS;AAAA,EACb,CAAC;AACD,SAAO,OAAO,UAAU,GAAG;AAC3B,QAAM,YAAY,IAAI,0CAA0C;AAAA,IAC5D,WAAW,oBAAoB,YAAY;AAAA,IAC3C,GAAG;AAAA,EACP,CAAC;AACD,SAAO,yBAA2B,WAAW,aAAa,IAAI;AAClE;AACA,IAAM,uCAAuC,OAAO,cAAc,YAAY;AAC1E,QAAM,WAAW,IAAI,CAAC,CAAC;AACvB,QAAM,OAAO,aAAa;AAC1B,QAAM,MAAM,KAAK,MAAM;AAAA,IACnB,SAAS;AAAA,EACb,CAAC;AACD,SAAO,OAAO,UAAU,GAAG;AAC3B,QAAM,YAAY,IAAI,+BAA+B;AAAA,IACjD,WAAW,oBAAoB,YAAY;AAAA,IAC3C,GAAG;AAAA,EACP,CAAC;AACD,SAAO,yBAA2B,WAAW,aAAa,IAAI;AAClE;AACA,IAAM,oCAAoC,OAAO,cAAc,YAAY;AACvE,QAAM,WAAW,IAAI,CAAC,CAAC;AACvB,QAAM,OAAO,aAAa;AAC1B,QAAM,MAAM,KAAK,MAAM;AAAA,IACnB,SAAS;AAAA,EACb,CAAC;AACD,SAAO,OAAO,UAAU,GAAG;AAC3B,QAAM,YAAY,IAAI,4BAA4B;AAAA,IAC9C,WAAW,oBAAoB,YAAY;AAAA,IAC3C,GAAG;AAAA,EACP,CAAC;AACD,SAAO,yBAA2B,WAAW,aAAa,IAAI;AAClE;AACA,IAAM,wCAAwC,OAAO,cAAc,YAAY;AAC3E,QAAM,WAAW,IAAI,CAAC,CAAC;AACvB,QAAM,OAAO,aAAa;AAC1B,QAAM,MAAM,KAAK,MAAM;AAAA,IACnB,SAAS;AAAA,EACb,CAAC;AACD,SAAO,OAAO,UAAU,GAAG;AAC3B,QAAM,YAAY,IAAI,gCAAgC;AAAA,IAClD,WAAW,oBAAoB,YAAY;AAAA,IAC3C,GAAG;AAAA,EACP,CAAC;AACD,SAAO,yBAA2B,WAAW,aAAa,IAAI;AAClE;AACA,IAAM,wCAAwC,OAAO,cAAc,YAAY;AAC3E,QAAM,WAAW,IAAI,CAAC,CAAC;AACvB,QAAM,OAAO,aAAa;AAC1B,QAAM,MAAM,KAAK,MAAM;AAAA,IACnB,SAAS;AAAA,EACb,CAAC;AACD,SAAO,OAAO,UAAU,GAAG;AAC3B,QAAM,YAAY,IAAI,gCAAgC;AAAA,IAClD,WAAW,oBAAoB,YAAY;AAAA,IAC3C,GAAG;AAAA,EACP,CAAC;AACD,SAAO,yBAA2B,WAAW,aAAa,IAAI;AAClE;AACA,IAAM,uBAAuB,CAAC,QAAQ,YAAY;AAC9C,SAAO,KAAK,QAAQ;AAAA,IAChB,UAAU;AAAA,IACV,cAAc;AAAA,IACd,cAAc,CAAC,MAAM,cAAgB,oBAAsB,aAAe,CAAC,CAAC,CAAC;AAAA,IAC7E,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,MAAM;AAAA,EACV,CAAC;AACL;AACA,IAAM,wBAAwB,CAAC,QAAQ,YAAY;AAC/C,SAAO,KAAK,QAAQ;AAAA,IAChB,YAAY,CAAC,MAAM,qBAAqB,GAAG,OAAO;AAAA,IAClD,MAAM;AAAA,EACV,CAAC;AACL;AACA,IAAM,4BAA4B,CAAC,QAAQ,YAAY;AACnD,QAAM,UAAU,UAAU,CAAC,GACtB,OAAO,CAACC,OAAMA,MAAK,IAAI,EACvB,IAAI,CAAC,UAAU;AAChB,WAAO,sBAAsB,OAAO,OAAO;AAAA,EAC/C,CAAC;AACD,SAAO;AACX;AACA,IAAM,mBAAmB,CAAC,QAAQ,YAAY;AAC1C,SAAO,KAAK,QAAQ;AAAA,IAChB,cAAc,CAAC,MAAM,cAAgB,oBAAsB,aAAe,CAAC,CAAC,CAAC;AAAA,IAC7E,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,cAAc;AAAA,IACd,cAAc;AAAA,IACd,WAAW;AAAA,IACX,mBAAmB;AAAA,IACnB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,UAAU;AAAA,IACV,SAAS;AAAA,EACb,CAAC;AACL;AACA,IAAM,oBAAoB,CAAC,QAAQ,YAAY;AAC3C,QAAM,UAAU,UAAU,CAAC,GACtB,OAAO,CAACA,OAAMA,MAAK,IAAI,EACvB,IAAI,CAAC,UAAU;AAChB,WAAO,iBAAiB,OAAO,OAAO;AAAA,EAC1C,CAAC;AACD,SAAO;AACX;AACA,IAAM,sBAAsB,CAAC,YAAY;AAAA,EACrC,gBAAgB,OAAO;AAAA,EACvB,WAAW,OAAO,QAAQ,kBAAkB,KAAK,OAAO,QAAQ,mBAAmB,KAAK,OAAO,QAAQ,kBAAkB;AAAA,EACzH,mBAAmB,OAAO,QAAQ,YAAY;AAAA,EAC9C,MAAM,OAAO,QAAQ,aAAa;AACtC;AAEA,IAAM,MAAM;AACZ,IAAM,KAAK;AACX,IAAM,QAAQ;AACd,IAAM,MAAM;AACZ,IAAM,MAAM;AACZ,IAAM,MAAM;AACZ,IAAM,MAAM;AACZ,IAAM,KAAK;AACX,IAAM,MAAM;AACZ,IAAM,OAAO;;;ACzpBN,IAAM,uBAAN,cAAmC,QACrC,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUC,UAAS,IAAI,QAAQC,IAAG;AACrC,SAAO;AAAA,IACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,IACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,EACxE;AACJ,CAAC,EACI,EAAE,aAAa,iBAAiB,CAAC,CAAC,EAClC,EAAE,eAAe,sBAAsB,EACvC,EAAE,QAAQ,MAAM,EAChB,IAAI,uBAAuB,EAC3B,GAAG,uBAAuB,EAC1B,MAAM,EAAE;AACb;;;ACfO,IAAM,wBAAN,cAAoC,QACtC,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUE,UAAS,IAAI,QAAQC,IAAG;AACrC,SAAO;AAAA,IACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,IACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,EACxE;AACJ,CAAC,EACI,EAAE,aAAa,kBAAkB,CAAC,CAAC,EACnC,EAAE,eAAe,uBAAuB,EACxC,EAAE,QAAQ,MAAM,EAChB,IAAI,wBAAwB,EAC5B,GAAG,wBAAwB,EAC3B,MAAM,EAAE;AACb;;;ACdO,IAAM,oBAAN,cAAgC,QAClC,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUE,UAAS,IAAI,QAAQC,IAAG;AACrC,SAAO;AAAA,IACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,IACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,EACxE;AACJ,CAAC,EACI,EAAE,aAAa,cAAc,CAAC,CAAC,EAC/B,EAAE,eAAe,mBAAmB,EACpC,EAAE,QAAQ,kCAAkC,EAC5C,IAAI,oBAAoB,EACxB,GAAG,oBAAoB,EACvB,MAAM,EAAE;AACb;;;AChBO,IAAM,gCAAN,cAA4C,QAC9C,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUE,UAAS,IAAI,QAAQC,IAAG;AACrC,SAAO;AAAA,IACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,IACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,EACxE;AACJ,CAAC,EACI,EAAE,aAAa,0BAA0B,CAAC,CAAC,EAC3C,EAAE,eAAe,+BAA+B,EAChD,EAAE,QAAQ,MAAM,EAChB,IAAI,gCAAgC,EACpC,GAAG,gCAAgC,EACnC,MAAM,EAAE;AACb;;;ACfO,IAAM,sBAAN,cAAkC,QACpC,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUE,UAAS,IAAI,QAAQC,IAAG;AACrC,SAAO;AAAA,IACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,IACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,EACxE;AACJ,CAAC,EACI,EAAE,aAAa,gBAAgB,CAAC,CAAC,EACjC,EAAE,eAAe,qBAAqB,EACtC,EAAE,QAAQ,MAAM,EAChB,IAAI,sBAAsB,EAC1B,GAAG,sBAAsB,EACzB,MAAM,EAAE;AACb;;;ACfO,IAAM,kCAAN,cAA8C,QAChD,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUE,UAAS,IAAI,QAAQC,IAAG;AACrC,SAAO;AAAA,IACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,IACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,EACxE;AACJ,CAAC,EACI,EAAE,aAAa,4BAA4B,CAAC,CAAC,EAC7C,EAAE,eAAe,iCAAiC,EAClD,EAAE,QAAQ,MAAM,EAChB,IAAI,kCAAkC,EACtC,GAAG,kCAAkC,EACrC,MAAM,EAAE;AACb;;;ACdO,IAAM,oBAAN,cAAgC,QAClC,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUE,UAAS,IAAI,QAAQC,IAAG;AACrC,SAAO;AAAA,IACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,IACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,EACxE;AACJ,CAAC,EACI,EAAE,aAAa,cAAc,CAAC,CAAC,EAC/B,EAAE,eAAe,mBAAmB,EACpC,EAAE,mCAAmC,MAAM,EAC3C,IAAI,oBAAoB,EACxB,GAAG,oBAAoB,EACvB,MAAM,EAAE;AACb;;;AChBO,IAAM,kCAAN,cAA8C,QAChD,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUE,UAAS,IAAI,QAAQC,IAAG;AACrC,SAAO;AAAA,IACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,IACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,EACxE;AACJ,CAAC,EACI,EAAE,aAAa,4BAA4B,CAAC,CAAC,EAC7C,EAAE,eAAe,iCAAiC,EAClD,EAAE,QAAQ,MAAM,EAChB,IAAI,kCAAkC,EACtC,GAAG,kCAAkC,EACrC,MAAM,EAAE;AACb;;;ACdO,IAAM,0BAAN,cAAsC,QACxC,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUE,UAAS,IAAI,QAAQC,IAAG;AACrC,SAAO;AAAA,IACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,IACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,EACxE;AACJ,CAAC,EACI,EAAE,aAAa,oBAAoB,CAAC,CAAC,EACrC,EAAE,eAAe,yBAAyB,EAC1C,EAAE,QAAQ,wCAAwC,EAClD,IAAI,0BAA0B,EAC9B,GAAG,0BAA0B,EAC7B,MAAM,EAAE;AACb;;;ACXA,IAAM,WAAW;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AACO,IAAM,QAAN,cAAoB,YAAY;AACvC;AACA,uBAAuB,UAAU,KAAK;;;ACrB/B,IAAM,mCAAmC,gBAAgB,aAAa,iCAAiC,aAAa,aAAa,YAAY;", "names": ["getRuntimeConfig", "t", "getRuntimeConfig", "b", "e", "Command", "o", "Command", "o", "Command", "o", "Command", "o", "Command", "o", "Command", "o", "Command", "o", "Command", "o", "Command", "o"]}