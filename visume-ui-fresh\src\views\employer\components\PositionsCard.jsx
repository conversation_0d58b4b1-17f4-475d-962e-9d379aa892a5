import Dropdown from "components/dropdown";
import React from "react";
import {
  HiOutlineBriefcase,
  HiOutlineUserGroup,
  HiOutlineDotsVertical,
  HiX,
} from "react-icons/hi";
import { useNavigate } from "react-router-dom";
// Adjust the import path as necessary

const PositionsCard = ({ job }) => {
  const navigate = useNavigate();
  const skillsArray = job.skills
    ? job.skills.split(",").map((skill) => skill.trim())
    : [];
  return (
    <div className="mb-1 flex items-center justify-between rounded-lg border border-gray-300 bg-gray-50 p-3 transition duration-200 hover:border-gray-400 dark:border-navy-500 dark:bg-navy-800 dark:hover:border-navy-700">
      {/* Left side: Job title, skills, and location */}
      <div className="flex flex-col">
        <h3 className="text-xs tracking-wide md:tracking-normal md:text-md flex items-center font-bold text-gray-800 dark:text-white">
          {job.role}
          <div className="ml-2 flex items-center text-sm text-gray-500 dark:text-gray-300">
            <HiOutlineBriefcase className="mr-1" />
            {job.candidateDetails.preferred_location}
          </div>
        </h3>
        <div className="mt-1 flex flex-wrap  space-x-1  md:space-x-2">
          {skillsArray.slice(0, 4).map((skill, index) => (
            <span key={index}
              className="relative tracking-wide md:tracking-normal overflow-hidden rounded-full bg-gray-100 px-2 py-1 text-[9px]  md:text-[11px] text-gray-800 dark:bg-gray-600 dark:text-white"
            >
              {skill}
              <span className="from-transparent to-transparent animate-shine absolute inset-0 bg-gradient-to-r via-white opacity-30" />
            </span>
          ))}
          {skillsArray.length > 4 && (
            <span className="relative overflow-hidden rounded-full bg-gray-100 px-2 py-1 text-[11px] text-gray-800 dark:bg-gray-600 dark:text-white">
              + {skillsArray.length - 4} more
              <span className="from-transparent to-transparent animate-shine absolute inset-0 bg-gradient-to-r via-white opacity-30" />
            </span>
          )}
        </div>
      </div>

      {/* Right side: View button and three dots */}
      <div className="flex items-center gap-4">
        <button
          className="text-sm md:text-base flex flex-row gap-1 items-center justify-center rounded-full bg-brand-500 px-3 py-1 text-center text-white transition hover:bg-brand-600 dark:bg-brand-600 dark:hover:bg-brand-700"
          onClick={() => navigate("../suggested-candidates")}
        >
          <HiOutlineUserGroup className="text-sm md:text-base"/>
            View
        </button>
        <Dropdown
          button={<HiOutlineDotsVertical className="text-gray-500" />}
          children={
            <div className="flex h-max w-max flex-col justify-start rounded-[20px] border border-gray-300 bg-white bg-cover bg-no-repeat p-4 shadow-[0_20px_25px_-5px] shadow-shadow-500 dark:border-navy-500 dark:!bg-navy-700 dark:text-white dark:shadow-none">
              <div className="flex flex-col ">
                <a
                  href=" "
                  className="flex items-center text-sm  text-gray-800 transition duration-200 hover:text-brand-500 dark:text-white dark:hover:text-brand-400"
                >
                  <HiOutlineBriefcase className="mr-1" />
                  Edit JD
                </a>
                <a
                  href=" "
                  className="mt-2 flex items-center text-sm font-medium text-red-500 transition duration-200 hover:text-red-400"
                >
                  <HiX className="mr-1" />
                  Delete
                </a>
              </div>
            </div>
          }
          classNames={"py-2 bottom-[-60px] -left-[100px] w-max"}
        />
      </div>
    </div>
  );
};

export default PositionsCard;
