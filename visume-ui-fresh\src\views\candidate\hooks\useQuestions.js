import { useState, useEffect, useCallback } from "react";
import Cookies from "js-cookie";
import toast from "react-hot-toast";

// Default questions for fallback
const getDefaultQuestion = (role, skill, type = 'technical') => {
  const questions = {
    technical: {
      question: `Describe a challenging problem you've solved using ${skill}. What was your approach and solution?`,
      type: 'technical'
    },
    behavioral: {
      question: `How do you handle difficult situations in your role as a ${role}? Give a specific example.`,
      type: 'behavioral'
    }
  };
  return questions[type] || questions.technical;
};

export function useQuestions() {
  // Initialize state from localStorage if available
  const [allQuestions, setAllQuestions] = useState(() => {
    const saved = localStorage.getItem("questions");
    return saved ? JSON.parse(saved) : [];
  });

  const [currentIndex, setCurrentIndex] = useState(0); // Always start with first question

  const [currentQuestion, setCurrentQuestion] = useState(() => {
    try {
      const saved = localStorage.getItem("questions");
      const questions = saved ? JSON.parse(saved) : [];
      const index = parseInt(localStorage.getItem("currentQuestionIndex"), 10) || 0;
      
      // Ensure we have a valid question
      if (questions && questions.length > 0 && index >= 0 && index < questions.length) {
        return questions[index];
      }
      
      // Return default question if no valid question found
      const defaultQuestion = getDefaultQuestion("Software Developer", "general", 'technical');
      console.log("Using default question:", defaultQuestion);
      return defaultQuestion;
    } catch (error) {
      console.error("Error initializing current question:", error);
      return getDefaultQuestion("Software Developer", "general", 'technical');
    }
  });

  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  // Keep currentQuestion in sync with questions and index
  // Keep currentQuestion in sync and ensure it's always valid
  useEffect(() => {
    if (allQuestions.length > 0 && currentIndex >= 0 && currentIndex < allQuestions.length) {
      const question = allQuestions[currentIndex];
      if (question && JSON.stringify(question) !== JSON.stringify(currentQuestion)) {
        console.log("Updating current question:", question);
        setCurrentQuestion(question);
      }
      localStorage.setItem("questions", JSON.stringify(allQuestions));
      localStorage.setItem("currentQuestionIndex", currentIndex.toString());
    } else {
      console.warn("Invalid questions state:", { questionsLength: allQuestions.length, currentIndex });
    }
  }, [allQuestions, currentIndex, currentQuestion]);

  const loadFirstQuestion = async () => {
    try {
      setIsLoading(true);
      
      // Get and validate cookie data with retries
      let createVRResJson;
      const maxRetries = 3;
      const retryDelay = 500; // 500ms

      for (let i = 0; i < maxRetries; i++) {
        createVRResJson = Cookies.get("CreateVRres");
        const allCookies = Cookies.get();
        console.log(`Cookie check attempt ${i + 1}:`, { createVRResJson, allCookies });

        if (createVRResJson) break;
        
        await new Promise(resolve => setTimeout(resolve, retryDelay));
      }

      if (!createVRResJson) {
        const allCookies = Cookies.get();
        const error = "Interview data not found after retries";
        console.error(error, { allCookies });
        
        // Try to restore from localStorage
        const savedData = localStorage.getItem("interviewData");
        if (savedData) {
          console.log("Restoring from localStorage");
          Cookies.set("CreateVRres", savedData);
          createVRResJson = savedData;
        } else {
          toast("Interview data missing - please start over", {
            icon: '❌',
            description: "Could not find required data"
          });
          setError(error);
          setIsLoading(false);
          return;
        }
      }

      // Save to localStorage as backup
      localStorage.setItem("interviewData", createVRResJson);

      // Parse and validate cookie data
      let parsedData;
      try {
        parsedData = JSON.parse(createVRResJson);
        console.log("Parsed interview data:", parsedData);
        
        if (!parsedData || typeof parsedData !== 'object') {
          throw new Error("Invalid data structure");
        }

        // Validate required fields
        const requiredFields = ['candId', 'jobRole']; // Add any other required fields
        const missingFields = requiredFields.filter(field => !parsedData[field]);
        
        if (missingFields.length > 0) {
          throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
        }

      } catch (err) {
        const error = `Invalid interview data: ${err.message}`;
        console.error(error, { createVRResJson, parsedData });
        toast("Invalid interview setup - please start over", {
          icon: '❌',
          description: err.message
        });
        setError(error);
        setIsLoading(false);
        return;
      }

      // Log all possible role fields
      console.log("Role fields in data:", {
        role: parsedData.role,
        position_name: parsedData.position_name,
        position: parsedData.position,
        job_role: parsedData.job_role,
        title: parsedData.title
      });

      // Validate and extract role
      // Extract role with jobRole being the primary field
      let role = parsedData.jobRole || "Software Developer";
      
      // Try other fields if jobRole is not available
      if (role === "Software Developer") {
        const possibleRoleFields = ['role', 'position_name', 'position', 'job_role', 'title'];
        for (const field of possibleRoleFields) {
          if (parsedData[field] && typeof parsedData[field] === 'string' && parsedData[field].trim()) {
            role = parsedData[field].trim();
            break;
          }
        }
      }
      
      console.log("Extracted role data:", {
        jobRole: parsedData.jobRole,
        finalRole: role,
        parsedFields: {
          role: parsedData.role,
          position_name: parsedData.position_name,
          position: parsedData.position,
          job_role: parsedData.job_role,
          title: parsedData.title
        }
      });

      // Validate and extract skills
      let skills = ["general"]; // Default skills
      const possibleSkillFields = ['skills', 'technical_skills', 'required_skills', 'technologies'];
      for (const field of possibleSkillFields) {
        if (parsedData[field]) {
          const skillData = parsedData[field];
          if (Array.isArray(skillData)) {
            skills = skillData.filter(s => s && typeof s === 'string' && s.trim());
          } else if (typeof skillData === 'string' && skillData.trim()) {
            skills = [skillData.trim()];
          }
          if (skills.length > 0) break;
        }
      }

      console.log("Using validated role:", role);
      console.log("Using validated skills:", skills);

      // Use first skill as primary if available
      const primarySkill = skills[0] || "general";

      // Get first question from API
      const response = await fetch(`${import.meta.env.VITE_APP_HOST}/api/v1/generate-question`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          role: role,
          skills: skills,
          primarySkill: primarySkill,
          previousQuestions: [] // Empty array for first question
        })
      });

      let firstQuestion;
      
      try {
        // For the first question, always use the experience question regardless of API response
        const skill = skills[0] || 'JavaScript';
        console.log('Starting interview with experience question for skill:', skill);

        firstQuestion = {
          question: `What is your experience with ${skill}?`,
          type: 'technical',
          keywords: ['experience', skill],
          difficulty: 'medium',
          startTimestamp: new Date().toISOString(), // Start timer immediately
          endTimestamp: null,
          answer: null,
          follow_up: "Please provide specific examples of projects or challenges where you've used this skill."
        };

        toast('Starting interview - tell us about your experience', { icon: '👋' });

        // We ignore the API response for the first question
        await response.json(); // consume response but don't use it
      } catch (error) {
        console.warn("Using default first question:", error);
        // Use primarySkill from the validated data above
        
        if (error.message === "Failed to fetch" || !navigator.onLine) {
          toast("Network error - using default question", { icon: '🌐' });
        } else {
          toast("Using default question", { icon: '⚠️' });
        }

        const defaultQ = getDefaultQuestion(role, skills[0] || 'JavaScript', 'technical');
        firstQuestion = {
          ...defaultQ,
          question: defaultQ.question.replace('[skill]', skills[0] || 'JavaScript'),
          startTimestamp: null,
          endTimestamp: null,
          answer: null
        };
        toast('Using default first question', { icon: '👋' });
      }

      // Clear any existing questions and start fresh
      localStorage.removeItem("questions");
      localStorage.removeItem("currentQuestionIndex");
      
      // Initialize questions with proper state management
      const initialQuestions = [firstQuestion];
      setAllQuestions(initialQuestions);
      setCurrentQuestion(firstQuestion);
      setCurrentIndex(0);
      setError(null);

      // Save initial state
      localStorage.setItem("questions", JSON.stringify(initialQuestions));
      localStorage.setItem("currentQuestionIndex", "0");
    } catch (err) {
      console.error("Error in useQuestions:", err);
      // Create experience question as fallback
      const skill = skills[0] || 'JavaScript';
      const fallbackQuestion = {
        question: `What is your experience with ${skill}?`,
        type: 'technical',
        keywords: ['experience', skill],
        difficulty: 'medium',
        startTimestamp: null,
        endTimestamp: null,
        answer: q.answer, // Preserve existing answer if any
        follow_up: "Please provide specific examples of projects or challenges where you've used this skill."
      };
      setAllQuestions([fallbackQuestion]);
      setCurrentQuestion(fallbackQuestion);
      setCurrentIndex(0); // Reset index to 0 for the fallback question
      setError(err.message || "Failed to load interview questions");
      toast("Using backup question due to error", { icon: '⚠️' });

      // Save fallback state to localStorage
      localStorage.setItem("questions", JSON.stringify([fallbackQuestion]));
      localStorage.setItem("currentQuestionIndex", "0");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadFirstQuestion();
  }, []);

  const startAnswering = () => {
    if (currentQuestion && !currentQuestion.startTimestamp) {
      const updatedQuestions = [...allQuestions];
      updatedQuestions[currentIndex] = {
        ...currentQuestion,
        startTimestamp: new Date().toISOString(),
        endTimestamp: null
      };
      setAllQuestions(updatedQuestions);
      setCurrentQuestion(updatedQuestions[currentIndex]);
    }
  };

  const nextQuestion = async () => {
    window.speechSynthesis.cancel();
    
    try {
      setError(null);
      setIsLoading(true);
      
      // Save current answer before proceeding
      if (currentQuestion && currentIndex >= 0) {
        const updatedQuestions = [...allQuestions];
        const currentAnswer = currentQuestion.answer;
        
        // Preserve the current answer exactly as is
        if (currentAnswer !== null && currentAnswer !== undefined) {
          updatedQuestions[currentIndex] = {
            ...currentQuestion,
            answer: currentAnswer
          };
          setAllQuestions(updatedQuestions);
          
          // Update localStorage with preserved answer
          localStorage.setItem("questions", JSON.stringify(updatedQuestions));
        }
      }

      // Get interview data
      // Try to get interview data with fallback to localStorage
      let createVRResJson = Cookies.get("CreateVRres");
      if (!createVRResJson) {
        const savedData = localStorage.getItem("interviewData");
        if (savedData) {
          console.log("Restoring from localStorage for next question");
          Cookies.set("CreateVRres", savedData);
          createVRResJson = savedData;
        } else {
          const error = "Session data lost";
          console.error(error, { allCookies: Cookies.get() });
          toast("Session data lost - please restart", {
            icon: '❌',
            description: "Could not recover interview data"
          });
          setError(error);
          setIsLoading(false);
          return;
        }
      }

      // Keep localStorage in sync
      localStorage.setItem("interviewData", createVRResJson);

      // Parse and validate data again
      let parsedData;
      try {
        parsedData = JSON.parse(createVRResJson);
        
        if (!parsedData || typeof parsedData !== 'object') {
          throw new Error("Invalid data structure");
        }

        // Revalidate required fields
        const requiredFields = ['candId', 'jobRole'];
        const missingFields = requiredFields.filter(field => !parsedData[field]);
        
        if (missingFields.length > 0) {
          throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
        }

      } catch (err) {
        const error = `Interview data corrupted: ${err.message}`;
        console.error(error, { createVRResJson });
        toast("Interview data error - please restart", {
          icon: '❌',
          description: err.message
        });
        setError(error);
        setIsLoading(false);
        return;
      }

      console.log("Next question - using validated data:", parsedData);

      // Validate and extract role (same as loadFirstQuestion)
      let role = "Software Developer";
      const possibleRoleFields = ['role', 'position_name', 'position', 'job_role', 'title'];
      for (const field of possibleRoleFields) {
        if (parsedData[field] && typeof parsedData[field] === 'string' && parsedData[field].trim()) {
          role = parsedData[field].trim();
          break;
        }
      }

      // Validate and extract skills (same as loadFirstQuestion)
      let skills = ["general"];
      const possibleSkillFields = ['skills', 'technical_skills', 'required_skills', 'technologies'];
      for (const field of possibleSkillFields) {
        if (parsedData[field]) {
          const skillData = parsedData[field];
          if (Array.isArray(skillData)) {
            skills = skillData.filter(s => s && typeof s === 'string' && s.trim());
          } else if (typeof skillData === 'string' && skillData.trim()) {
            skills = [skillData.trim()];
          }
          if (skills.length > 0) break;
        }
      }

      const primarySkill = skills[0] || "general";

      console.log('Generating next question:', {
        currentIndex: currentIndex,
        totalQuestions: allQuestions.length,
        role: role,
        skills: skills
      });

      // Determine next question type
      const previousType = currentQuestion?.type || 'behavioral';
      const nextType = previousType === 'technical' ? 'behavioral' : 'technical';

      // Collect previous Q&A history and validate answers
      const previousQA = allQuestions.map(q => {
        // Clean and validate answer
        let answer = q.answer;
        if (typeof answer === 'string') {
          answer = answer.trim();
        }
        
        return {
          question: q.question,
          answer: answer, // Pass through answer without modification
          type: q.type || 'technical'
        };
      });

      // Get next question from API with conversation history
      const response = await fetch(`${import.meta.env.VITE_APP_HOST}/api/v1/generate-question`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          role: role,
          skills: skills,
          primarySkill: primarySkill,
          preferredType: nextType,
          previousQuestions: previousQA
        })
      });

      let nextQuestionData;
      try {
        if (!response.ok) {
          throw new Error('Failed to fetch next question');
        }

        const data = await response.json();
        console.log('API Response:', data);

        if (data.success && data.question) {
          const questionText = typeof data.question.question === 'string'
            ? data.question.question
            : typeof data.question === 'string'
              ? data.question
              : null;

          if (!questionText) {
            throw new Error("Invalid question format");
          }

          // Check if question contains [skill] placeholder and replace it
          let formattedQuestion = questionText;
          if (formattedQuestion.includes('[skill]')) {
            console.log('Replacing [skill] placeholder with:', skills[0]);
            formattedQuestion = formattedQuestion.replace('[skill]', skills[0] || 'JavaScript');
          }

          nextQuestionData = {
            question: formattedQuestion,
            type: nextType,
            keywords: Array.isArray(data.question.keywords) ? data.question.keywords : [],
            difficulty: data.question.difficulty || 'medium',
            startTimestamp: null,
            endTimestamp: null,
            answer: null,
            follow_up: data.question.follow_up || null
          };
        }
        else if (data.fallback) {
          toast('Using fallback question', { icon: '⚠️' });
          
          // Format fallback question if it has [skill] placeholder
          let fallbackQuestion = data.fallback;
          if (fallbackQuestion.includes('[skill]')) {
            console.log('Replacing [skill] placeholder in fallback with:', skills[0]);
            fallbackQuestion = fallbackQuestion.replace('[skill]', skills[0] || 'JavaScript');
          }
          
          nextQuestionData = {
            question: fallbackQuestion,
            type: nextType,
            keywords: [],
            difficulty: 'medium',
            startTimestamp: null,
            endTimestamp: null,
            answer: null,
            follow_up: null
          };
        }
        else {
          throw new Error("Invalid response format");
        }
      } catch (error) {
        console.warn("API request failed:", error);
        
        const defaultQuestion = getDefaultQuestion(role, primarySkill, nextType);
        toast(
          error.message.includes("Failed to fetch")
            ? "Network error - using default question"
            : "API error - using default question",
          { icon: '⚠️' }
        );
        
        nextQuestionData = {
          ...defaultQuestion,
          startTimestamp: null,
          endTimestamp: null,
          answer: null,
          follow_up: null
        };
      }

      // Update current question and add new one
      const updatedQuestions = [...allQuestions];
      if (currentQuestion) {
        updatedQuestions[currentIndex] = {
          ...currentQuestion,
          endTimestamp: new Date().toISOString()
        };
      }

      updatedQuestions.push(nextQuestionData);
      setAllQuestions(updatedQuestions);
      setCurrentIndex(currentIndex + 1);
      setCurrentQuestion(nextQuestionData);
      toast(`Next question`, { icon: '➡️' });
      return false; // Return false unless we hit MAX_QUESTIONS
      
    } catch (error) {
      console.error("Error getting next question:", error);
      
      // Create fallback question for next question
      // Use default question with proper skill replacement
      const defaultQ = getDefaultQuestion(role || "Software Developer", skills[0] || "JavaScript", nextType);
      const fallbackQuestion = {
        ...defaultQ,
        question: defaultQ.question.replace('[skill]', skills[0] || 'JavaScript'),
        startTimestamp: null,
        endTimestamp: null,
        answer: null,
        follow_up: null
      };

      // Update questions with fallback
      const updatedQuestions = [...allQuestions];
      if (currentQuestion) {
        updatedQuestions[currentIndex] = {
          ...currentQuestion,
          endTimestamp: new Date().toISOString()
        };
      }
      updatedQuestions.push(fallbackQuestion);
      
      // Update state with fallback
      setAllQuestions(updatedQuestions);
      setCurrentIndex(currentIndex + 1);
      setCurrentQuestion(fallbackQuestion);
      
      // Save to localStorage
      localStorage.setItem("questions", JSON.stringify(updatedQuestions));
      localStorage.setItem("currentQuestionIndex", (currentIndex + 1).toString());
      
      setError(error.message || "Failed to get next question - using fallback");
      toast("Using backup question due to error", { icon: '⚠️' });
      return false; // Always allow more questions
    } finally {
      setIsLoading(false);
    }
  };

  const updateAnswer = useCallback((value) => {
    if (!currentQuestion || currentIndex < 0) {
      console.warn("Invalid state for answer update:", {
        hasCurrentQuestion: !!currentQuestion,
        currentIndex
      });
      return;
    }

    try {
      // Only update if the value is different from current answer
      if (currentQuestion.answer !== value) {
        console.log("Updating answer:", {
          questionIndex: currentIndex,
          previousAnswer: currentQuestion.answer,
          newAnswer: value
        });

        const updatedQuestions = [...allQuestions];
        const updatedQuestion = {
          ...currentQuestion,
          answer: value,
          lastModified: new Date().toISOString(),
          endTimestamp: value ? new Date().toISOString() : null
        };
        updatedQuestions[currentIndex] = updatedQuestion;
        
        // Update state
        setAllQuestions(updatedQuestions);
        setCurrentQuestion(updatedQuestion);

        // Immediately save to localStorage to prevent loss
        localStorage.setItem("questions", JSON.stringify(updatedQuestions));

        console.log("Answer updated successfully", {
          totalQuestions: updatedQuestions.length,
          currentIndex,
          hasAnswer: value !== null && value !== undefined
        });
      }
    } catch (error) {
      console.error("Error updating answer:", error);
      toast.error("Failed to save answer. Please try again.");
    }
  }, [currentQuestion, currentIndex, allQuestions]);

  // Add an effect to keep localStorage in sync with state changes
  useEffect(() => {
    if (allQuestions.length > 0) {
      localStorage.setItem("questions", JSON.stringify(allQuestions));
    }
  }, [allQuestions]);

  // Initialize questions if they exist in localStorage
  useEffect(() => {
    const savedQuestions = localStorage.getItem("questions");
    if (savedQuestions) {
      try {
        const parsed = JSON.parse(savedQuestions);
        if (Array.isArray(parsed) && parsed.length > 0) {
          setAllQuestions(parsed);
        }
      } catch (error) {
        console.error("Error parsing saved questions:", error);
      }
    }
  }, []);

  const initializeQuestions = (initialQuestions) => {
    try {
      if (!Array.isArray(initialQuestions)) {
        throw new Error('Invalid questions format');
      }

      // Clear existing data
      localStorage.removeItem("questions");
      localStorage.removeItem("currentQuestionIndex");

      // Normalize questions
      const normalizedQuestions = initialQuestions.map((q, index) => ({
        ...q,
        question: q.question || (typeof q === 'string' ? q : '') || "What is your experience with [skill]?",
        startTimestamp: index === 0 ? new Date().toISOString() : null, // Start timer for first question
        endTimestamp: null,
        answer: null,
        follow_up: null,
        type: q.type || 'technical'
      }));

      // Update state
      setAllQuestions(normalizedQuestions);
      setCurrentQuestion(normalizedQuestions[0]);
      setCurrentIndex(0);
      setError(null);

      // Save to localStorage
      localStorage.setItem("questions", JSON.stringify(normalizedQuestions));
      localStorage.setItem("currentQuestionIndex", "0");

      return true;
    } catch (error) {
      console.error("Error initializing questions:", error);
      setError("Failed to initialize questions");
      return false;
    }
  };

  return {
    questions: allQuestions,
    currentQuestion,
    currentIndex,
    nextQuestion,
    startAnswering,
    updateAnswer,
    initializeQuestions,
    error,
    isLoading
  };
}
