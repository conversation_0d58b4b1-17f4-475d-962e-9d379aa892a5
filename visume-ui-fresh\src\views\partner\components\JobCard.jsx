import React from 'react'

const JobCard = ({job,iconUrl}) => {
  return (
    <div className="col-span-full dark:bg-navy-900 sm:col-span-4 lg:col-span-6 p-[8px_12px] dk-border-one rounded-lg h-full hover:bg-gray-100 dark:hover:bg-gray-800  transition-all duration-300 ease-in-out border border-light-gray cursor-pointer">
    <div className="flex items-center justify-around gap-3 p-2">
      {/* Left Column: Image */}
      <div className="shrink-0">
        <img src={iconUrl} alt="Image" className="w-8 h-8" />
      </div>

      {/* Right Column: Text */}
      <div className="flex-1">
        <h2 className="text-sm font-semibold text-gray-900 dark:text-white">{job.title}</h2>
        <div className="flex justify-between items-center mt-0">
          <div className="text-gray-900 dark:text-white font-medium text-xs">{job.company}</div>
          <div className="text-gray-900 dark:text-gray-400 font-medium text-xs">
            <span>{job.recruited}</span> Recruited
          </div>
        </div>
      </div>
    </div>
  </div>
  )
}

export default JobCard