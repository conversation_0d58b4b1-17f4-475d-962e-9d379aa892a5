const pool = require("../config/db");
const { jobDescStripper } = require("../utils/helpers");
const fs = require("fs");
const { helperFilterCandidates } = require("./videoProfileController");

// Fetch profiles by employer ID
exports.getProfilesByEmployerId = async (req, res) => {
  const { emp_id } = req.params;

  try {
    // Get a connection from the pool
    pool.getConnection((err, connection) => {
      if (err) {
        console.error("Error getting database connection:", err);
        return res.status(500).send("Failed to connect to the database.");
      }

      // SQL query to fetch job seeker profiles by employer ID
      const sqlQuery = `
                SELECT *
                FROM employerprofiles
                WHERE emp_id = ?;
            `;

      // Execute the query
      connection.query(sqlQuery, [emp_id], (err, results) => {
        connection.release(); // Release the connection

        if (err) {
          console.error("Error fetching profiles from database:", err);
          return res.status(500).send("Failed to fetch profiles.");
        }

        if (results.length === 0) {
          return res
            .status(404)
            .json({ message: "No profiles found for this employer." });
        }

        // Send the response
        res.status(200).json({
          message: "Profiles fetched successfully.",
          data: results, // Returning all profiles under the employer
        });
      });
    });
  } catch (error) {
    console.error("Error fetching profiles:", error);
    res.status(500).send("Failed to fetch profiles.");
  }
};

// Fetch shortlisted profiles by employer ID
exports.getShortlistProfiles = async (req, res) => {
  const { emp_id } = req.params;

  // Validate the required field
  if (!emp_id) {
    return res.status(400).json({ message: "Employer ID is required." });
  }

  try {
    // Get a connection from the pool
    const conn = await pool.promise().getConnection();

    try {
      // SQL query to fetch video_resume_id and status for the employer
      const sqlQuery = `
        SELECT video_resume_id, status
        FROM employerprofiles
        WHERE emp_id = ?;
      `;

      // Execute the query to get video_resume_id and status
      const [employerProfiles] = await conn.query(sqlQuery, [emp_id]);

      if (employerProfiles.length === 0) {
        return res.status(404).json({
          message: "No shortlisted profiles found for this employer.",
        });
      }

      // Create a map of video_resume_id to status from employerProfiles
      const statusMap = employerProfiles.reduce((acc, profile) => {
        acc[profile.video_resume_id] = profile.status;
        return acc;
      }, {});

      // Extract video_resume_id array (which are actually `id`s from videoprofile)
      const videoProfileIdsArray = employerProfiles.map(
        (c) => c.video_resume_id
      );

      // SQL query to fetch real video_resume_id, profile data, and jobseeker data
      const candidateDataQuery = `
        SELECT vp.id AS profile_id, vp.video_profile_id, js.*, vp.* 
        FROM videoprofile vp
        INNER JOIN jobseeker js ON vp.cand_id = js.cand_id
        WHERE vp.id IN (?);
      `;

      // Execute the query to get video profile and jobseeker data
      const [candidateProfiles] = await conn.query(candidateDataQuery, [
        videoProfileIdsArray,
      ]);

      if (candidateProfiles.length === 0) {
        return res.status(404).json({
          message: "No candidate profiles found.",
        });
      }

      // Replace the status in candidateProfiles with the status from employerProfiles
      candidateProfiles.forEach((profile) => {
        profile.status = statusMap[profile.profile_id] || profile.status;
      });

      // Send the response with the merged data, including real video_resume_id
      res.status(200).json({
        message: "Candidate profiles with jobseeker data fetched successfully.",
        data: candidateProfiles, // Returning merged profiles with jobseeker data, updated status, and real video_resume_id
      });
    } finally {
      conn.release(); // Always release the connection after query execution
    }
  } catch (error) {
    console.error("Error fetching profiles:", error);
    return res.status(500).json({ message: "Failed to fetch profiles." });
  }
};

exports.getEmployerProfilesData = async (req, res) => {
  const emp_id = req.headers["authorization"];

  // Validate the required field
  if (!emp_id) {
    return res
      .status(400)
      .json({ message: "Unauthorized Access, Please Log in Again." });
  }

  try {
    // Get a connection from the pool
    const conn = await pool.promise().getConnection();

    try {
      // Fetch the employer's plan ID from employerplans
      const planIdQuery = `SELECT plan_id,creditsLeft,end_date FROM employerplans WHERE emp_id = ?;`;
      const [planIdResult] = await conn.query(planIdQuery, [emp_id]);

      // Check if any plan ID is found
      if (!planIdResult.length) {
        return res.status(404).json({
          message: "No employer profile plan found. Please log in again.",
        });
      }

      const planId = planIdResult[0].plan_id;

      // Fetch plan details from plans table
      const planDetailsQuery = `SELECT * FROM plans WHERE id = ?;`;
      const [planDetails] = await conn.query(planDetailsQuery, [planId]);

      // Check if plan details are found
      if (!planDetails.length) {
        return res.status(404).json({
          message: "No plan details found for the employer profile.",
        });
      }

      // Fetch employer name from employer table
      const employerNameQuery = `SELECT emp_name FROM employer WHERE id = ?;`;
      const [employerResult] = await conn.query(employerNameQuery, [emp_id]);

      // Check if employer name is found
      if (!employerResult.length) {
        return res.status(404).json({
          message: "Employer not found. Please log in again.",
        });
      }

      const emp_name = employerResult[0].emp_name;

      // Count shortlisted candidates
      const shortlistedCountQuery = `SELECT COUNT(*) AS shortlisted_count FROM employerprofiles WHERE emp_id = ? AND status = 'shortlisted';`;
      const [shortlistedCountResult] = await conn.query(shortlistedCountQuery, [
        emp_id,
      ]);
      const shortlisted_count = shortlistedCountResult[0].shortlisted_count;

      // Count unlocked candidates
      const unlockedCountQuery = `SELECT COUNT(*) AS unlocked_count FROM employerprofiles WHERE emp_id = ? AND status = 'unlocked';`;
      const [unlockedCountResult] = await conn.query(unlockedCountQuery, [
        emp_id,
      ]);
      const unlocked_count = unlockedCountResult[0].unlocked_count;

      // Combine the data into a single response object
      res.status(200).json({
        message: "Employer profile plan fetched successfully.",
        data: {
          ...planDetails[0],
          emp_name,
          creditsLeft: planIdResult[0]?.creditsLeft || 0,
          end_date: planIdResult[0]?.end_date || 0,
          candidate_counts: {
            shortlisted_count: shortlisted_count,
            unlocked_count: unlocked_count,
          },
        },
      });
    } finally {
      conn.release(); // Always release the connection after query execution
    }
  } catch (error) {
    console.error("Error fetching profiles:", error);
    return res.status(500).json({ message: "Failed to fetch profiles." });
  }
};

exports.getEmployerDetails = async (req, res) => {
  const emp_id = req.headers["authorization"];

  // Validate the required field
  if (!emp_id) {
    return res
      .status(400)
      .json({ message: "Unauthorized Access, Please Log in Again." });
  }

  try {
    // Get a connection from the pool
    const conn = await pool.promise().getConnection();

    try {
      // Fetch the employer's plan ID from employerplans
      const employerIdQuery = `SELECT * FROM employer WHERE id = ?;`;
      const [employerIdResult] = await conn.query(employerIdQuery, [emp_id]);

      // Check if any plan ID is found
      if (!employerIdResult.length) {
        return res.status(404).json({
          message: "No employer details Found. Please log in again.",
        });
      }

      const companyId = employerIdResult[0].company_id;

      // Fetch plan details from plans table
      const companyDetailsQuery = `SELECT * FROM company WHERE id = ?;`;
      const [companyDetails] = await conn.query(companyDetailsQuery, [
        companyId,
      ]);

      // Check if plan details are found
      if (!companyDetails.length) {
        return res.status(404).json({
          message: "No plan details found for the employer profile.",
        });
      }

      // Combine the data into a single response object
      res.status(200).json({
        message: "Employer profile data fetched successfully.",
        data: {
          ...employerIdResult[0],
          ...companyDetails[0],
        },
      });
    } finally {
      conn.release(); // Always release the connection after query execution
    }
  } catch (error) {
    console.error("Error fetching Employer Data:", error);
    return res.status(500).json({ message: "Failed to fetch Employer Data." });
  }
};

// Shortlist a video profile for an employer
exports.shortlistVideoProfile = async (req, res) => {
  const { emp_id, cand_id } = req.body;

  // Validate required fields
  if (!emp_id || !cand_id) {
    return res
      .status(400)
      .json({ message: "Employer ID and Candidate ID are required." });
  }

  try {
    // Get a connection from the pool
    const conn = await pool.promise().getConnection();

    try {
      // // Check if Candidate ID is valid
      const checkCandidateQuery = "SELECT id FROM videoprofile WHERE id = ?";
      const [candidate] = await conn.query(checkCandidateQuery, [cand_id]);

      if (candidate.length === 0) {
        return res.status(404).json({ message: "Invalid Video Profile ID." });
      }

      // Check if Employer ID is valid
      const checkEmployerQuery = "SELECT id FROM employer WHERE id = ?";
      const [employer] = await conn.query(checkEmployerQuery, [emp_id]);

      if (employer.length === 0) {
        return res.status(404).json({ message: "Invalid Employer ID." });
      }

      // SQL query to update the status to "shortlisted" for a candidate
      const updateQuery = `
        INSERT INTO employerprofiles (emp_id, video_resume_id, status, shortlisted_at, unlocked_at)
        VALUES (?, ?, 'shortlisted', NOW(), 'Not unlocked')
        ON DUPLICATE KEY UPDATE
          status = 'shortlisted',
          shortlisted_at = NOW(),
          unlocked_at = 'Not unlocked';
      `;

      // Execute the query to update or insert the shortlist status
      const [updateResult] = await conn.query(updateQuery, [emp_id, cand_id]);

      // SQL query to fetch the updated video profile data
      const selectQuery = `
        SELECT id, emp_id, video_resume_id, status, shortlisted_at, unlocked_at
        FROM employerprofiles
        WHERE video_resume_id = ? AND emp_id = ?;
      `;

      // Retrieve the updated profile data
      const [rows] = await conn.query(selectQuery, [cand_id, emp_id]);

      // Return the shortlisted profile data
      const { id, status, shortlisted_at, unlocked_at } = rows[0];
      return res.status(200).json({
        message: "Video profile shortlisted successfully.",
        data: {
          id,
          video_resume_id: emp_id,
          cand_id,
          status,
          shortlisted_at,
          unlocked_at,
        },
      });
    } finally {
      conn.release(); // Always release the connection after query execution
    }
  } catch (error) {
    console.error("Error shortlisting video profile:", error);
    return res
      .status(500)
      .json({ message: "Failed to shortlist video profile." });
  }
};

// UnShortlist a video profile for an employer
exports.unShortlistVideoProfile = async (req, res) => {
  const { emp_id, cand_id } = req.body;

  // Validate required fields
  if (!emp_id || !cand_id) {
    return res
      .status(400)
      .json({ message: "Employer ID and Candidate ID are required." });
  }

  try {
    // Get a connection from the pool
    const conn = await pool.promise().getConnection();

    try {
      // Check if Employer ID is valid
      const checkEmployerQuery = "SELECT id FROM employer WHERE id = ?";
      const [employer] = await conn.query(checkEmployerQuery, [emp_id]);

      if (employer.length === 0) {
        return res.status(404).json({ message: "Invalid Employer ID." });
      }

      // Check if the video profile exists
      const checkVideoQuery =
        "SELECT id FROM videoprofile WHERE video_profile_id = ?";
      const [videoProfile] = await conn.query(checkVideoQuery, [cand_id]);

      if (videoProfile.length === 0) {
        return res.status(404).json({ message: "Invalid Video Resume ID." });
      }

      const { id: videoProfileId } = videoProfile[0];

      // Check if the candidate is shortlisted
      const checkShortlistQuery =
        "SELECT * FROM employerprofiles WHERE video_resume_id = ? AND emp_id = ?";
      const [shortlistedCandidate] = await conn.query(checkShortlistQuery, [
        videoProfileId,
        emp_id,
      ]);

      if (shortlistedCandidate.length === 0) {
        return res
          .status(404)
          .json({ message: "Candidate is not shortlisted by this employer." });
      }

      // Remove the candidate from the shortlist
      const deleteShortlistQuery =
        "DELETE FROM employerprofiles WHERE video_resume_id = ? AND emp_id = ?";
      const [deleteResult] = await conn.query(deleteShortlistQuery, [
        videoProfileId,
        emp_id,
      ]);

      if (deleteResult.affectedRows === 0) {
        return res
          .status(404)
          .json({ message: "Failed to unshortlist candidate." });
      }

      return res.status(200).json({
        message: "Video profile unshortlisted successfully.",
      });
    } finally {
      conn.release(); // Always release the connection after query execution
    }
  } catch (error) {
    console.error("Error unshortlisting video profile:", error);
    return res
      .status(500)
      .json({ message: "Failed to unshortlist video profile." });
  }
};

exports.unlockVideoProfile = async (req, res) => {
  const { emp_id, video_profile_id } = req.body;

  // Validate required fields
  if (!emp_id || !video_profile_id) {
    return res
      .status(400)
      .json({ message: "Employer ID and Video Profile ID are required." });
  }

  try {
    // Get a connection from the pool
    const conn = await pool.promise().getConnection();

    try {
      // Check if employer has sufficient credits
      const planIdQuery = `SELECT creditsLeft FROM employerplans WHERE emp_id = ?;`;
      const [planResult] = await conn.query(planIdQuery, [emp_id]);

      if (!planResult.length) {
        return res.status(404).json({ message: "Employer profile not found." });
      }

      const { creditsLeft } = planResult[0];

      if (creditsLeft <= 0) {
        return res.status(429).json({
          message:
            "Please recharge to unlock this video profile. Your credits are over.",
        });
      }

      // Update profile status if credits are sufficient
      const updateQuery = `
        UPDATE employerprofiles
        SET status = ?, unlocked_at = NOW()
        WHERE video_resume_id = ? AND emp_id = ? AND status = ?;
      `;

      const [updateResult] = await conn.query(updateQuery, [
        "unlocked",
        video_profile_id,
        emp_id,
        "shortlisted",
      ]);

      if (updateResult.affectedRows === 0) {
        return res.status(404).json({
          message: "Profile not found or not in the shortlisted status.",
        });
      }

      // Deduct 1 credit after successful profile unlock
      const deductCreditQuery = `
        UPDATE employerplans
        SET creditsLeft = creditsLeft - 1
        WHERE emp_id = ?;
      `;

      await conn.query(deductCreditQuery, [emp_id]);

      // Retrieve and return the updated profile data
      const selectUpdatedQuery = `
        SELECT *
        FROM employerprofiles
        WHERE video_resume_id = ? AND emp_id = ?;
      `;

      const [updatedRows] = await conn.query(selectUpdatedQuery, [
        video_profile_id,
        emp_id,
      ]);

      res.status(200).json({
        message: "Profile updated to unlocked successfully.",
        data: updatedRows[0],
      });
    } finally {
      conn.release(); // Always release the connection after query execution
    }
  } catch (error) {
    console.error("Error processing video profile update:", error);
    res.status(500).json({ message: "Failed to process profile update." });
  }
};

// Removes a video profile for an employer from shortlisted
exports.removeVideoProfile = async (req, res) => {
  const { emp_id, cand_id } = req.body;

  // Validate required fields
  if (!emp_id || !cand_id) {
    return res
      .status(400)
      .json({ message: "Employer ID and Candidate ID are required." });
  }

  try {
    // Get a connection from the pool
    pool.getConnection((err, connection) => {
      if (err) {
        console.error("Error getting database connection:", err);
        return res
          .status(500)
          .json({ message: "Failed to connect to the database." });
      }

      // SQL query to check if the video profile is unlocked
      const checkQuery = `
        SELECT status 
        FROM employerprofiles 
        WHERE cand_id = ? AND emp_id = ?;
      `;

      connection.query(checkQuery, [cand_id, emp_id], (err, results) => {
        if (err) {
          connection.release(); // Release the connection on error
          console.error("Error checking video profile status:", err);
          return res
            .status(500)
            .json({ message: "Failed to check video profile status." });
        }

        if (results.length === 0) {
          connection.release(); // Release the connection if no results found
          return res.status(404).json({ message: "Video profile not found." });
        }

        const profileStatus = results[0].status;

        // Check if the profile is unlocked, cannot delete if unlocked
        if (profileStatus === "unlocked") {
          connection.release(); // Release the connection if status is unlocked
          return res
            .status(400)
            .json({ message: "Cannot delete an unlocked video profile." });
        }

        // SQL query to remove the profile from the shortlist
        const deleteQuery = `
          DELETE FROM employerprofiles 
          WHERE cand_id = ? AND emp_id = ? AND status != 'unlocked';
        `;

        connection.query(deleteQuery, [cand_id, emp_id], (err, results) => {
          connection.release(); // Release the connection after query

          if (err) {
            console.error("Error removing video profile from shortlist:", err);
            return res.status(500).json({
              message: "Failed to remove video profile from shortlist.",
            });
          }

          if (results.affectedRows === 0) {
            return res
              .status(404)
              .json({ message: "Video profile not found or already removed." });
          }

          // Return success response
          res.status(200).json({
            message: "Video profile removed from shortlist successfully.",
          });
        });
      });
    });
  } catch (error) {
    console.error("Error removing video profile:", error);
    res
      .status(500)
      .json({ message: "Failed to remove video profile from shortlist." });
  }
};

exports.uploadJobDescription = async (req, res) => {
  const { emp_id } = req.params;

  const jobDesc = req.files?.job_description
    ? req.files.job_description[0]
    : null;

  if (!jobDesc) {
    return res.status(400).json({ message: "Job description is required." });
  }

  if (!emp_id) {
    return res.status(400).json({ message: "Employer ID is required." });
  }
  try {
    const conn = await pool.promise().getConnection();

    // Check if Employer ID is valid
    const checkEmployerQuery = "SELECT id FROM employer WHERE id = ?";
    const [employer] = await conn.query(checkEmployerQuery, [emp_id]);

    if (employer.length === 0) {
      return res.status(404).json({ message: "Invalid Employer ID." });
    }

    // Load the uploaded PDF file
    const pdfBuffer = fs.readFileSync(jobDesc.path);

    // Process the PDF and fetch AI-enhanced data
    console.log("Fetching data from AI...");
    const enhancedData = await jobDescStripper(pdfBuffer);
    console.log("Data Fetched");

    // Send the processed response
    res.status(200).json({
      JobDescription: enhancedData,
      filePath: jobDesc.path,
      message: "Job Description Uploaded Successfully.",
    });
  } catch (error) {
    console.error("Error processing job description:", error);
    return res
      .status(500)
      .json({ message: "Failed to process job description." });
  }
};

exports.createJobDescription = async (req, res) => {
  const { emp_id, role, skills, experience, location, filePath } = req.body;

  if (!emp_id) {
    return res.status(400).json({ message: "Employer ID is required." });
  }

  try {
    const conn = await pool.promise().getConnection();

    // Check if Employer ID is valid
    const checkEmployerQuery = "SELECT id FROM employer WHERE id = ?";
    const [employer] = await conn.query(checkEmployerQuery, [emp_id]);

    if (employer.length === 0) {
      return res.status(404).json({ message: "Invalid Employer ID." });
    }

    // Call helper function to filter candidates
    const candidates = await helperFilterCandidates({
      preferred_location: location,
      role,
      selectedSkills: skills,
      experience,
    });

    // Extract video profile IDs from the filtered candidates
    const suggested_profiles = candidates.map((e) => e.video_profile_id);

    // Create job description object
    const JobDescription = JSON.stringify({
      role,
      skills,
      experience,
      location,
      filePath,
    });

    const createQuery = `
      INSERT INTO job_descriptions (employer_id, suggested_profiles, JobDescription)
      VALUES (?, ?, ?)
    `;

    // Execute the query to insert job description
    await conn.query(createQuery, [
      emp_id,
      JSON.stringify(suggested_profiles), // Store as JSON
      JobDescription,
    ]);

    res.json({
      message: "Job Description created successfully."
    });
  } catch (error) {
    console.error("Error creating Job Description:", error);
    res
      .status(500)
      .json({ message: "Failed to create Job Description.", error });
  }
};