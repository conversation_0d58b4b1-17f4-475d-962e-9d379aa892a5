/* eslint-disable */
import React from "react";
import { Link, useLocation } from "react-router-dom";
import DashIcon from "components/icons/DashIcon";
// chakra imports


// export default SidebarLinks;
export function SidebarLinks(props) {

  let location = useLocation();
  const layout = `/${location.pathname.split("/")[1]}`;   
  let { routes, onClose ,open } = props;

  // Verifies if routeName is the one active
  const activeRoute = (routeName) => {
    return location.pathname.includes(routeName);

  };

  const createLinks = (routes) => {

    return routes.filter((route) => route.layout === layout).map((route, index) => {

        const linkPath = layout === "/employer" && route.path === "profile-search" ? "/profile-search" : `${route.layout}/${route.path}`;
      
       return (

          <Link key={index} to={linkPath}   onClick={() => {
              // Only hide sidebar on smaller screens
                if (window.innerWidth < 768 && open) onClose();
            }}
          >
            <div className={`relative flex hover:cursor-pointer ${layout == "/employer"?"mb-4":"mb-3"} `}>

              <li className="my-[2px] flex cursor-pointer items-center pl-5 pr-8">
                <span
                  className={`${
                    activeRoute(route.path)
                      ? "font-bold text-brand-500 dark:text-white"
                      : "font-medium text-gray-600"
                  }`}
                >
                  {route.icon ? route.icon : <DashIcon />}{" "}
                </span>
                <p
                  className={`leading-1 ml-4 flex ${
                    activeRoute(route.path)
                      ? "font-bold text-navy-700 dark:text-white"
                      : "font-medium text-gray-600"
                  }`}
                >
                  {route.name}
                </p>
              </li>
              {activeRoute(route.path) && (
                <div className="absolute right-0 top-px h-9 w-1 rounded-lg bg-brand-500 dark:bg-brand-400" />
              )}
            </div>
          </Link>

        );
      });
  };

  return createLinks(routes);
}
export default SidebarLinks;