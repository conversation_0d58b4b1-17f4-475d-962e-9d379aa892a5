import { parse } from "postcss";
import React, { useRef, useEffect, useState } from "react";

export default function VideoPreview({ stream }) {
  const [name, setName] = useState("Tejas S P");
  const [role, setRole] = useState("Node.js Developer");
  const [image, setImage] = useState(
    "https://cdn8.dissolve.com/p/D18_249_012/D18_249_012_0004_600.jpg"
  );
  const videoRef = useRef(null);

  useEffect(() => {
    if (videoRef.current && stream) {
      videoRef.current.srcObject = stream;
    }
  }, [stream]);

  useEffect(() => {
    const data = localStorage.getItem("formData");
    if (data) {
      const parsedData = JSON.parse(data);
      setRole(parsedData.jobRole);
      const url = `${import.meta.env.VITE_APP_HOST}/api/v1/candidate/${
        parsedData.candId
      }`;
      console.log(parsedData);
      // Make an API call using fetch
      fetch(url)
        .then((res) => res.json())
        .then((data) => {
          setName(data.candidateProfile[0].cand_name);
          data.candidateProfile[0].profile_picture &&
            setImage(
              `${import.meta.env.VITE_APP_HOST}/${
                data.candidateProfile[0].profile_picture
              }`
            );
        })
        .catch((err) => console.log(err));
    }
  }, []);
  return (
    <div className="relative h-full overflow-hidden rounded-xl border-2 border-gray-300 bg-gray-800 shadow-lg">
      <video
        ref={videoRef}
        className="h-full w-full object-cover"
        autoPlay
        muted
      />
      <div className="to-transparent absolute bottom-0 left-0 w-full bg-gradient-to-t from-gray-900 p-4">
        <div className="flex items-center space-x-3 text-white">
          <img
            src={image}
            alt="User"
            className="h-12 w-12 rounded-full object-cover"
          />
          <div>
            <div className="text-lg font-semibold">{name}</div>
            <div className="text-sm text-gray-300">{role}</div>
          </div>
        </div>
      </div>
    </div>
  );
}
